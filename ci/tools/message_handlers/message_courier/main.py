import os
import xml.etree.ElementTree as ET
import re
from internal_tools.gitlab_internal_tools import *
import internal_tools.slack_cadinality_download
MR_IID = os.environ.get("CI_MERGE_REQUEST_IID")
CI_PROJECT_ID = os.environ.get("CI_PROJECT_ID")


def find_xml_files():
    xml_files = []
    for file in os.listdir():
        if file.endswith(".xml"):
            xml_files.append(file)
    print(f"[DEBUG] Found {len(xml_files)} xml files: \n{xml_files}")
    return xml_files