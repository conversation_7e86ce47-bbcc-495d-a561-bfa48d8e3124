import urllib3
import os
import requests
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# CORTEX_SLACK_BOT_TOKEN
SLACK_TOKEN = os.environ.get("CORTEX_SLACK_BOT_TOKEN")  # Bot or User token
LINK = "https://panw-rnd.slack.com/archives/C02NTMPURSN/p1751907070635699"
DOWNLOAD_DIR = "./cardinality_reports"

client = WebClient(token=SLACK_TOKEN)

def parse_link(link):
    """Extracts channel_id and timestamp from a Slack link."""
    parts = link.split('/')
    channel_id = parts[-2]
    message_ts_raw = parts[-1]
    # Convert 'p1234567890123456' -> '1234567890.123456'
    timestamp = f"{message_ts_raw[1:-6]}.{message_ts_raw[-6:]}"
    return channel_id, timestamp

def download_file(url, filename, token):
    """Downloads a file from <PERSON>lack using the auth token."""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(url, headers=headers, verify=False)
    
    if response.status_code == 200:
        filepath = os.path.join(DOWNLOAD_DIR, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        print(f"Downloaded: {filename}")
    else:
        print(f"Failed to download {filename}: Status {response.status_code}")

def main():
    if not os.path.exists(DOWNLOAD_DIR):
        os.makedirs(DOWNLOAD_DIR)

    try:
        channel_id, thread_ts = parse_link(LINK)
        
        # 1. Fetch the thread messages
        # Note: If the link points to a reply, this might fail. 
        # Ideally, ensure 'thread_ts' is the parent message's timestamp.
        response = client.conversations_replies(channel=channel_id, ts=thread_ts)
        messages = response.get('messages', [])

        print(f"Found {len(messages)} messages in thread.")

        # 2. Iterate through messages to find text and files
        for msg in messages:
            user = msg.get('user', 'Unknown')
            text = msg.get('text', '')
            print(f"[{user}]: {text}")

            # 3. Check for files
            if 'files' in msg:
                counter = 0
                for file_info in msg['files']:
                    # prefer 'url_private_download' or 'url_private'
                    download_url = file_info.get('url_private_download')
                    filename = file_info.get('name')
                    
                    if download_url:
                        download_file(download_url, filename + '_' + str(counter), SLACK_TOKEN)

    except SlackApiError as e:
        print(f"Error fetching conversations: {e}")

if __name__ == "__main__":
    main()