import os
import requests
import gitlab
GITLAB_MONITORING_CI_TOKEN = os.environ.get("GITLAB_MONITORING_CI_TOKEN")
MR_IID = os.environ.get("CI_MERGE_REQUEST_IID")
CI_PROJECT_ID = os.environ.get("CI_PROJECT_ID")
GITLAB_BASE_URL = "https://gitlab.xdr.pan.local"
GITLAB_API_SUFFIX = f"/api/v4/projects/{CI_PROJECT_ID}/merge_requests/{MR_IID}"
GITLAB_NOTES_URL = f"{GITLAB_BASE_URL}{GITLAB_API_SUFFIX}/notes"
GIT<PERSON>B_DISCUSSION_URL = f"{GITLAB_BASE_URL}{GITLAB_API_SUFFIX}/discussions"
AUTH_TOKEN = GITLAB_MONITORING_CI_TOKEN

def create_comment(comment_message):
    """Post a comment to the GitLab MR using the API."""

    # Debug: Check if token is set
    if not AUTH_TOKEN:
        print("[ERROR] No auth token available (G<PERSON><PERSON><PERSON>_MONITORING_CI_TOKEN)!")
        return

    # Determine which token and header to use
    
    token_header = "PRIVATE-TOKEN"
    token_source = "GITLAB_MONITORING_CI_TOKEN"
    
    print(f"[DEBUG] Using {token_source}, Token length: {len(AUTH_TOKEN)}, URL: {GITLAB_NOTES_URL}")

    headers = {
        token_header: AUTH_TOKEN,
        "Content-Type": "application/json"
    }

    payload = {
        "body": comment_message
    }

    response = requests.post(GITLAB_NOTES_URL, headers=headers, json=payload, verify=False)

    if response.status_code == 201:
        print("[SUCCESS] Comment posted successfully")
    else:
        print(f"[ERROR] Failed to post comment: {response.status_code} - {response.text}")


def post_comment_on_line(file_path: str, line_number: int, comment: str, head_sha: str, base_sha: str, start_sha: str, project_id=CI_PROJECT_ID, mr_iid=MR_IID, api_token=AUTH_TOKEN):
    url = f"{GITLAB_DISCUSSION_URL}"
    
    headers = {
        "PRIVATE-TOKEN": api_token,
        "Content-Type": "application/json"
    }
    file_path = file_path.strip()
    print(f"[DEBUG] \n base_sha: {base_sha}\n start_sha: {start_sha}\n head_sha: {head_sha}\n file_path: {file_path}\n line_number: {line_number}\n comment: {comment}")
    
    print(f"File Path (Raw):  {repr(file_path)}")
    print(f"Line Number (Raw): {repr(line_number)}")
    print(f"Type of Line:      {type(line_number)}")

    payload = {
        "body": comment,
        "position": {
            "position_type": "text",
            "base_sha": base_sha,
            "start_sha": start_sha,
            "head_sha": head_sha,
            "new_path": file_path,
            "new_line": int(line_number)
        }
    }
    print(f"[DEBUG] Posting comment on {file_path}:{line_number} with message: {comment}")
    try:
        # Use discussions endpoint to start a thread on a specific line
        response = requests.post(url, headers=headers, json=payload, verify=False)
        
        if response.status_code == 201:
            print(f"Success: Comment posted on {file_path}:{line_number}")
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"Exception: {e}")
        return None
    

def get_merge_request_details(api_token=AUTH_TOKEN):
    url = f"{GITLAB_BASE_URL}{GITLAB_API_SUFFIX}"
    headers = {
        "PRIVATE-TOKEN": api_token,
        "Content-Type": "application/json"
    }
    response = requests.get(url, headers=headers, verify=False)
    if response.status_code == 200:
        print(response.json()['diff_refs'])
        return response.json()['diff_refs']
    else:
        print(f"Error {response.status_code}: {response.text}")
        return None
    

def find_modified_files_in_mr():
    # Authenticate with GitLab
    gl = gitlab.Gitlab(url=GITLAB_BASE_URL, private_token=AUTH_TOKEN, ssl_verify=False)

    # Get the project and merge request objects
    project = gl.projects.get(f"{CI_PROJECT_ID}")
    mr = project.mergerequests.get(f"{MR_IID}")
    mr_changes = mr.changes()
    # Extract just the file paths from the changes array
    new_paths = [change['new_path'] for change in mr_changes.get('changes', [])]
    unique_prefixes = set()

    for path in new_paths:
        if path.split('/')[0] == 'tools':
            unique_prefixes.add(path)
            continue
        root = path.split('/')[0]
        if root == '.gitlab-ci.yml':
            root = 'gitlab-ci'
    
        unique_prefixes.add(root)

    print(f"[DEBUG] Unique prefixes: {unique_prefixes}")
    return unique_prefixes


def add_label_to_mr(label_name):
    
    # Authenticate with GitLab
    gl = gitlab.Gitlab(url=GITLAB_BASE_URL, private_token=AUTH_TOKEN, ssl_verify=False)

    # Get the project and merge request objects
    project = gl.projects.get(f"{CI_PROJECT_ID}")
    mr = project.mergerequests.get(f"{MR_IID}")

    # Add new labels
    # The 'labels' attribute in the object model handles adding/removing automatically
    # when you save the object after modification.
    mr.labels.append(f'{label_name}')
    # mr.labels.append('')

    # Save the changes via the API
    mr.save()

    print("Labels added to Merge Request")


def comment_already_exists(file_path: str, line_number: int, message: str, project_id=CI_PROJECT_ID, mr_iid=MR_IID, api_token=AUTH_TOKEN):
    url = f"{GITLAB_DISCUSSION_URL}"
    headers = {
        "PRIVATE-TOKEN": api_token,
        "Content-Type": "application/json"
    }

    # Handle pagination - GitLab returns 20 items per page by default
    page = 1
    per_page = 100  # Max allowed per page
    all_discussions = []

    while True:
        params = {"page": page, "per_page": per_page}
        response = requests.get(url, headers=headers, params=params, verify=False)

        if response.status_code != 200:
            print(f"Error {response.status_code}: {response.text}")
            return False

        discussions = response.json()
        if not discussions:
            break  # No more pages

        all_discussions.extend(discussions)
        page += 1
    for discussion in all_discussions:
        for note in discussion['notes']:  
            if note.get('type') == "DiffNote" and note.get('resolved') == False:
                note_position = int(note.get('position')['new_line'])
                note_path = note.get('position')['new_path']
                if note_position == int(line_number) and note_path == file_path:
                    if message in note.get('body'):
                        print(f"[DEBUG] Note Position: {note_position}, Note Path: {note_path}, Line Number: {line_number}, File Path: {file_path}")
                        print(f"Comment already exists on {file_path}:{line_number}, returning True for comment_already_exists()")
                    return True
    return False

def get_mr_description_body():
    url = f"{GITLAB_BASE_URL}{GITLAB_API_SUFFIX}"
    headers = {
        "PRIVATE-TOKEN": AUTH_TOKEN,
        "Content-Type": "application/json"
    }
    response = requests.get(url, headers=headers, verify=False)
    if response.status_code == 200:
        return response.json()['description']
    else:
        print(f"Error {response.status_code}: {response.text}")
        return None