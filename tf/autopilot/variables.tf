variable "ttl" {
  type        = string
  default     = "180"
  description = "Self-destruct interval in minutes, defaults to 3 hours (180 minutes)"
}

variable "region" {
  description = "The GCP region"
  type        = string
}

variable "host_project" {
  description = "The host_project project"
  type        = string
}

variable "project_id" {
  description = "The host project ID"
  type        = string
}

variable "zone" {
  description = "The zone"
  type        = string
}

variable "viso_env" {
  type = string
}

variable "lcaas" {
  description = "Tenant GCP project ID"
  type        = string
}

variable "is_platform" {
  type = bool
}

variable "siloed_conf_start_time" {
  type = string
}

variable "siloed_conf_end_time" {
  type = string
}

variable "autopilot_enable_pii" {
  type    = string
  default = "true"
}

variable "autopilot_fail_on_error" {
  type    = string
  default = "false"
}

variable "autopilot_log_level" {
  type    = string
  default = "INFO"
}

variable "autopilot_enable_vt" {
  type    = string
  default = "true"
}

variable "siloed_conf_query_type" {
  type    = string
  default = "incidents"
}

variable "siloed_conf_skip_unresolved_incidents" {
  type    = string
  default = "false"
}

variable "siloed_conf_mal_threshold" {
  type    = string
  default = "0.9"
}

variable "siloed_conf_susp_threshold" {
  type    = string
  default = "0.7"
}

variable "siloed_conf_ids" {
  type    = string
  default = ""
}

variable "siloed_conf_enable_graph_export" {
  type    = string
  default = "false"
}

variable "siloed_conf_enable_graph_import" {
  type    = string
  default = "false"
}

variable "siloed_enable_queue_execution" {
  type    = string
  default = "true"
}

variable "siloed_conf_clear_db" {
  type    = string
  default = "false"
}

variable "siloed_conf_import_snapshot_timestamp" {
  type    = string
  default = null
}

variable "siloed_enable_timeout_execution" {
  type    = string
  default = "true"
}

variable "siloed_fail_on_timeout" {
  type    = string
  default = "2"
}

variable "siloed_max_subgraph_size" {
  type    = string
  default = "1200"
}

variable "siloed_enable_continues_execution" {
  type    = string
  default = "true"
}

variable "siloed_continues_time_frame" {
  type    = string
  default = "30"
}

variable "instance_id" {
  type        = string
  default     = "01"
  description = "Instance ID for the autopilot VM (01-99), used to support multiple VMs per tenant"
}