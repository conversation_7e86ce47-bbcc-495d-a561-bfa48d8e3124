provider "google" {
  project = var.host_project
  region  = var.region
  alias   = "st"
}

resource "google_compute_instance" "autopilot_vm_instance" {

  provider     = google.st
  name         = "autopilot-${var.lcaas}-${var.instance_id}"
  machine_type = "e2-standard-4"
  zone         = var.zone
  project      = var.project_id

  boot_disk {
    initialize_params {
      image = data.google_compute_image.autopilot_vm_image.self_link
      size  = 50
    }
  }
  tags = ["research-autopilot-vm"]

  network_interface {
    network            = "network-${var.lcaas}"
    subnetwork         = "subnetwork-${var.lcaas}-${var.region}"
    subnetwork_project = var.project_id
  }

  labels = {
    name            = "tenant_autopilot_instance"
    app             = "xdr_shared"
    project_id      = var.project_id
    env             = var.viso_env
    host_project_id = var.host_project
  }

  metadata = {
    enable-os-login                       = "true"
    SILOED_CONF_START_TIME                = var.siloed_conf_start_time
    SILOED_CONF_END_TIME                  = var.siloed_conf_end_time
    SELF_DESTRUCT_INTERVAL_MINUTES        = var.ttl
    AUTOPILOT_FAIL_ON_ERROR               = var.autopilot_fail_on_error
    AUTOPILOT_ENABLE_PII                  = var.autopilot_enable_pii
    AUTOPILOT_PROFILE_DEV_ENV             = var.viso_env == "dev" ? "true" : "false"
    AUTOPILOT_LOG_LEVEL                   = var.autopilot_log_level
    AUTOPILOT_ENABLE_VT                   = var.autopilot_enable_vt
    SILOED_CONF_QUERY_TYPE                = var.siloed_conf_query_type
    SILOED_CONF_SKIP_UNRESOLVED_INCIDENTS = var.siloed_conf_skip_unresolved_incidents
    SILOED_CONF_MAL_THRESHOLD             = var.siloed_conf_mal_threshold
    SILOED_CONF_SUSP_THRESHOLD            = var.siloed_conf_susp_threshold
    SILOED_CONF_IDS                       = var.siloed_conf_ids
    SILOED_CONF_ENABLE_GRAPH_EXPORT       = var.siloed_conf_enable_graph_export
    SILOED_CONF_ENABLE_GRAPH_IMPORT       = var.siloed_conf_enable_graph_import
    SILOED_CONF_CLEAR_DB                  = var.siloed_conf_clear_db
    SILOED_CONF_IMPORT_SNAPSHOT_TIMESTAMP = var.siloed_conf_import_snapshot_timestamp
    SILOED_CONF_PLATFORM_TENANT           = var.is_platform
    SILOED_ENABLE_TIMEOUT_EXECUTION       = var.siloed_enable_timeout_execution
    SILOED_FAIL_ON_TIMEOUT                = var.siloed_fail_on_timeout
    SILOED_CONTINUES_TIME_FRAME           = var.siloed_continues_time_frame
    SILOED_ENABLE_CONTINUES_EXECUTION     = var.siloed_enable_continues_execution
    SILOED_MAX_SUBGRAPH_SIZE              = var.siloed_max_subgraph_size
    SILOED_ENABLE_QUEUE_EXECUTION         = var.siloed_enable_queue_execution
  }

  service_account {
    email  = data.google_service_account.autopilot.email
    scopes = ["cloud-platform"]
  }

  metadata_startup_script = <<-EOT
    #!/usr/bin/env bash
    set -x
    set -o pipefail
    exec &> >(tee -a /var/log/startup_script.log) 2>&1
    /bin/bash /opt/runtime_startup_script.sh
  EOT
}