module "metro_workload_identity" {
  source   = "../../modules/workload_identity"
  for_each = local.metro_apps
  providers = {
    google     = google
    google.mt  = google.mt
    kubernetes = kubernetes
  }
  service_account_name  = each.value.service_account_name
  mt_additional_group   = lookup(each.value, "mt_additional_group", "false")
  mt_dedicated_group    = lookup(each.value, "mt_dedicated_group", false)
  dedicated_group_name  = lookup(each.value, "dedicated_group_name", "")
  project_id            = var.project_id
  metro_host_project_id = var.metro_id
  wi_project_id         = var.project_id
  data                  = lookup(each.value, "workload_identity", {})
  namespace             = local.metro_namespace
  viso_env              = var.viso_env
  create_kubernetes_sa  = false
  extra_namespaces      = [local.metro_namespace]
}

locals {
  metro_apps = {
    cts = {
      service_account_name = "cts-pod"
      workload_identity = {
        # Example: Add additional project-level IAM roles to the service account
        # additional_project_roles = ["roles/iam.serviceAccountTokenCreator"]
      }
    }
    cwp-sp-account-controller = {
      service_account_name = "cwp-sp-account-controller"
      dedicated_group_name = "crtx-ads-shared-acc"
      mt_dedicated_group   = true
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    fetcher = {
      service_account_name = "fetcher"
      workload_identity    = {}
    }
    cloud-onboarding-manager = {
      service_account_name = "cloud-onboarding-manager"
      workload_identity    = {}
    }
    issue-fetcher = {
      service_account_name = "issue-fetcher"
      workload_identity    = {}
    }
    issues-updater = {
      service_account_name = "issues-updater"
      workload_identity    = {}
    }
    frontend = {
      service_account_name = "frontend"
      workload_identity    = {}
    }
    modules-issue-ingester = {
      service_account_name = "modules-issue-ingester"
      workload_identity    = {}
    }
    uvem-netscan-processor = {
      service_account_name = "uvem-netscan-processor"
      workload_identity    = {}
    }
    uvem-vip-api = {
      service_account_name = "uvem-vip-api"
      workload_identity    = {}
    }
    notifier = {
      service_account_name = "notifier"
      workload_identity    = {}
    }
    ca-collection-coordinator-pod = {
      service_account_name = "ca-collection-coordinator-pod"
      workload_identity    = {}
    }
    ca-collection-worker-pod = {
      service_account_name = "ca-collection-worker-pod"
      workload_identity    = {}
    }
    ca-collection-eai-worker-pod = {
      service_account_name = "ca-collection-eai-worker-pod"
      workload_identity    = {}
    }
    # DSPM
    dspm-cb-lsr = {
      service_account_name = "dspm-cb-lsr"
      workload_identity    = {}
    }
    dspm-x-files-momento = {
      service_account_name = "dspm-x-files-momento"
      workload_identity    = {}
    }
    dspm-x-files-skinner = {
      service_account_name = "dspm-x-files-skinner"
      workload_identity    = {}
    }
    dspm-crespo-resource-listener = {
      service_account_name = "dspm-crespo-resource-listener"
      workload_identity    = {}
    }
    dspm-crespo-jose = {
      service_account_name = "dspm-crespo-jose"
      workload_identity    = {}
    }
    dspm-crespo-midfielder = {
      service_account_name = "dspm-crespo-midfielder"
      workload_identity    = {}
    }
    dspm-crespo-worker = {
      service_account_name = "dspm-crespo-worker"
      workload_identity    = {}
    }
    dspm-oo-boarder = {
      service_account_name = "dspm-oo-boarder"
      workload_identity    = {}
    }
    dspm-oo-joeker = {
      service_account_name = "dspm-oo-joeker"
      workload_identity    = {}
    }
    dspm-oo-ready-job-evaluator = {
      service_account_name = "dspm-oo-ready-job-evaluator"
      workload_identity    = {}
    }
    dspm-oo-genie = {
      service_account_name = "dspm-oo-genie"
      workload_identity    = {}
    }
    dspm-oo-worker = {
      service_account_name = "dspm-oo-worker"
      workload_identity    = {}
    }
    dspm-fda-api = {
      service_account_name = "dspm-fda-api"
      workload_identity    = {}
    }
    dspm-fda-lsr = {
      service_account_name = "dspm-fda-lsr"
      workload_identity    = {}
    }
    dspm-fda-worker = {
      service_account_name = "dspm-fda-worker"
      workload_identity    = {}
    }
    dspm-dpc = {
      service_account_name = "dspm-dpc"
      workload_identity    = {}
    }
    dspm-mac = {
      service_account_name = "dspm-mac"
      workload_identity    = {}
    }
    dspm-mac-worker = {
      service_account_name = "dspm-mac-worker"
      workload_identity    = {}
    }
    dspm-dacs = {
      service_account_name = "dspm-dacs"
      workload_identity    = {}
    }
    dspm-dacs-ts = {
      service_account_name = "dspm-dacs-ts"
      workload_identity    = {}
    }
    dspm-worker = {
      service_account_name = "dspm-worker"
      workload_identity    = {}
    }
    dspm-datasource-worker = {
      service_account_name = "dspm-datasource-worker"
      workload_identity    = {}
    }
    dspm-datasource = {
      service_account_name = "dspm-datasource"
      workload_identity    = {}
    }
    dspm-voyager = {
      service_account_name = "dspm-voyager"
      workload_identity    = {}
    }
    # Analytics
    analytics-po = {
      service_account_name = "analytics-po"
      workload_identity    = {}
    }
    analytics-ae = {
      service_account_name = "analytics-ae"
      workload_identity    = {}
    }
    analytics-d = {
      service_account_name = "analytics-d"
      workload_identity    = {}
    }
    # CIEM (moved to end with explicit roles)
    # CWP
    cwp-api = {
      service_account_name = "cwp-api"
      workload_identity    = {}
    }
    cwp-core-asset-analyzer = {
      service_account_name = "cwp-core-asset-analyzer"
      workload_identity    = {}
    }
    cwp-sp-bc-fetcher = {
      service_account_name = "cwp-sp-bc-fetcher"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-bc-object-handler = {
      service_account_name = "cwp-sp-bc-object-handler"
      workload_identity    = {}
    }
    cwp-sp-bc-distributor = {
      service_account_name = "cwp-sp-bc-distributor"
        workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-workload-orchestration= {
      service_account_name = "cwp-sp-workload-orchestration"
        workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-bc-log-ingestion = {
      service_account_name = "cwp-sp-bc-log-ingestion"
      workload_identity = {
        additional_project_roles = ["roles/logging.logWriter", "roles/cloudprofiler.agent"]
      }
    }
    cwp-sp-bc-metrics-ingestor = {
      service_account_name = "cwp-sp-bc-metrics-ingestor"
      workload_identity    = {}
    }
    cwp-fpp-worker = {
      service_account_name = "cwp-fpp-worker"
      workload_identity    = {}
    }
    cwp-fpp-policy-evaluator = {
      service_account_name = "cwp-fpp-policy-evaluator"
      workload_identity    = {}
    }
    cwp-fpp-dispatcher = {
      service_account_name = "cwp-fpp-dispatcher"
      workload_identity    = {}
    }
    cwp-fpp-dispatcher-2 = {
      service_account_name = "cwp-fpp-dispatcher-2"
      workload_identity    = {}
    }
    cwp-rules-management = {
      service_account_name = "cwp-rules-management"
      workload_identity    = {}
    }
    cwp-scan-results-enricher = {
      service_account_name = "cwp-scan-results-enricher"
      workload_identity    = {}
    }
    cwp-k8s-inventory-publisher = {
      service_account_name = "cwp-k8s-inventory-publisher"
      workload_identity    = {}
    }
    cwp-k8s-api = {
      service_account_name = "cwp-k8s-api"
      workload_identity    = {}
    }
    cwp-image-analyzer = {
      service_account_name = "cwp-image-analyzer"
      workload_identity    = {}
    }
    cwp-image-analyzer-cron-job = {
      service_account_name = "cwp-image-analyzer-cron-job"
      workload_identity    = {}
    }
    cwp-sbom-analyzer = {
      service_account_name = "cwp-sbom-analyzer"
      workload_identity    = {}
    }
    # CAS
    cas-billing-api = {
      service_account_name = "billing-cas"
      workload_identity    = {}
    }
    cas-scans-management = {
      service_account_name = "scans-management-cas"
      workload_identity    = {}
    }
    cas-policies-api = {
      service_account_name = "policies-cas"
      workload_identity    = {}
    }
    persistence-flow-cas = {
      service_account_name = "persistence-flow-cas"
      workload_identity    = {}
    }
    cas-customers-management-api = {
      service_account_name = "customers-management-cas"
      workload_identity    = {}
    }
    cas-scans = {
      service_account_name = "scans-cas"
      workload_identity    = {}
    }
    cas-detection-rules = {
      service_account_name = "detection-rules-cas"
      workload_identity    = {}
    }
    # XSOAR MT
    elastic-search = {
      service_account_name = "elastic-search"
      workload_identity    = {}
    }
    xsoar-workers-router = {
      service_account_name = "xsoar-workers-router"
      workload_identity    = {}
    }
    xsoar-workers-gateway = {
      service_account_name = "xsoar-workers-gateway"
      workload_identity    = {}
    }
    task-scheduling = {
      service_account_name = "task-scheduling"
      workload_identity    = {}
    }
    argo-controller = {
      service_account_name = "argo-controller"
      workload_identity    = {}
    }
    argo-server = {
      service_account_name = "argo-server"
      workload_identity    = {}
    }
    # Agent Management
    agent-api = {
      service_account_name = "agent-api"
      workload_identity    = {}
    }
    log-processor = {
      service_account_name = "log-processor"
      workload_identity    = {}
    }
    # APISEC MT
    apisec-enricher-service = {
      service_account_name = "apisec-enricher-service"
      workload_identity    = {}
    }
    apisec-asset-manager = {
      service_account_name = "apisec-asset-manager"
      workload_identity    = {}
    }
    apisec-risk-engine = {
      service_account_name = "apisec-risk-engine"
      workload_identity    = {}
    }
    apisec-grouping-service = {
      service_account_name = "apisec-grouping-service"
      workload_identity    = {}
    }
    apisec-bff-service = {
      service_account_name = "apisec-bff-service"
      workload_identity    = {}
    }
    apisec-spec-service = {
      service_account_name = "apisec-spec-service"
      workload_identity    = {}
    }
    apisec-issue-patcher-cron-job = {
      service_account_name = "apisec-issue-patcher-cron-job"
      workload_identity    = {}
    }
    apisec-spec-cron-job = {
      service_account_name = "apisec-spec-cron-job"
      workload_identity    = {}
    }
    apisec-spec-gate-cron-job = {
      service_account_name = "apisec-spec-gate-cron-job"
      workload_identity    = {}
    }
    apisec-inspection-service = {
      service_account_name = "apisec-inspection-service"
      workload_identity    = {}
    }
    apisec-issuer = {
      service_account_name = "apisec-issuer"
      workload_identity    = {}
    }
    # ITDR
    itdr-api = {
      service_account_name = "itdr-api"
      workload_identity    = {}
    }
    itdr-asset-retention = {
      service_account_name = "itdr-asset-retention"
      workload_identity    = {}
    }
    itdr-asset-updates-syncer = {
      service_account_name = "itdr-asset-updates-syncer"
      workload_identity    = {}
    }
    itdr-audits = {
      service_account_name = "itdr-audits"
      workload_identity    = {}
    }
    itdr-bigquery-migrator = {
      service_account_name = "itdr-bigquery-migrator"
      workload_identity    = {}
    }
    itdr-ca-baseline-cron = {
      service_account_name = "itdr-ca-baseline-cron"
      workload_identity    = {}
    }
    itdr-ca-realtime-cron = {
      service_account_name = "itdr-ca-realtime-cron"
      workload_identity    = {}
    }
    itdr-cap-block-responder = {
      service_account_name = "itdr-cap-block-responder"
      workload_identity    = {}
    }
    itdr-daily-risk-cron = {
      service_account_name = "itdr-daily-risk-cron"
      workload_identity    = {}
    }
    itdr-data-pipeline = {
      service_account_name = "itdr-data-pipeline"
      workload_identity    = {}
    }
    itdr-data-syncer = {
      service_account_name = "itdr-data-syncer"
      workload_identity    = {}
    }
    itdr-risk-processor-service = {
      service_account_name = "itdr-risk-processor-service"
      workload_identity    = {}
    }
    itdr-secret-rotation-cron = {
      service_account_name = "itdr-secret-rotation-cron"
      workload_identity    = {}
    }
    # Cloudsec
    cloudsec-dashboard-api = {
      service_account_name = "dashboard-api"
      workload_identity    = {}
    }
    rule-management = {
      service_account_name = "rule-management"
      workload_identity    = {}
    }
    cloud-api-service = {
      service_account_name = "cloud-api-service"
      workload_identity    = {}
    }
    xspm-scanner = {
      service_account_name = "xspm-scanner"
      workload_identity    = {}
    }
    search-and-investigate = {
      service_account_name = "search-and-investigate"
      workload_identity    = {}
    }
    # CIEM
    ciem-access-table-cron-job = {
      service_account_name = "ciem-access-table-cron-job"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-account-manager = {
      service_account_name = "ciem-account-manager"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-api = {
      service_account_name = "ciem-api"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-aws-access-analyzer = {
      service_account_name = "ciem-aws-access-analyzer"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-db-maintenance = {
      service_account_name = "ciem-db-maintenance"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-epc = {
      service_account_name = "ciem-epc"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-ipc = {
      service_account_name = "ciem-ipc"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-la-publisher-cron-job = {
      service_account_name = "ciem-la-publisher-cron-job"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-lap = {
      service_account_name = "ciem-lap"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-pre-calculation = {
      service_account_name = "ciem-pre-calculation"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-rule-scanner-aispm = {
      service_account_name = "ciem-rule-scanner-aispm"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-rule-scanner-ciem = {
      service_account_name = "ciem-rule-scanner-ciem"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-rule-scanner-dspm = {
      service_account_name = "ciem-rule-scanner-dspm"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-static-evidence = {
      service_account_name = "ciem-static-evidence"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    dspm-aispm-static-evidence = {
      service_account_name = "dspm-aispm-static-evidence"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    ciem-gam-cron-job = {
      service_account_name = "ciem-gam-cron-job"
      workload_identity = {
        additional_project_roles = ["roles/bigquery.jobUser", "roles/iam.workloadIdentityUser"]
      }
    }
    cwp-compliance-uai-scanner = {
      service_account_name = "cwp-compliance-uai-scanner"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator"]
      }
    }
    cwp-compliance-agent-rules-api = {
      service_account_name = "cwp-compliance-agent-rules-api"
      workload_identity    = {}
    }
    cwp-sp-snapshot = {
      service_account_name = "cwp-sp-snapshot"
      dedicated_group_name = "crtx-ads-shared-acc"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-ads-api = {
      service_account_name = "cwp-ads-api"
      dedicated_group_name = "crtx-ads-shared-acc"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-ads-snapshots = {
      service_account_name = "cwp-ads-snapshots"
      dedicated_group_name = "crtx-ads-shared-acc"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-ads-platform-sync = {
      service_account_name = "cwp-ads-platform-sync"
      dedicated_group_name = "crtx-ads-shared-acc"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    cwp-ads-scan-status-observer = {
      service_account_name = "cwp-ads-scan-status-observer"
      dedicated_group_name = "crtx-ads-shared-acc"
      workload_identity = {
        additional_project_roles = ["roles/iam.serviceAccountTokenCreator", "roles/cloudprofiler.agent"]
      }
    }
    storybuilder = {
      service_account_name = "storybuilder"
      workload_identity    = {}
    }
    cronus = {
      service_account_name = "cronus"
      workload_identity    = {}
    }
    cwp-k8s-content = {
      service_account_name = "cwp-k8s-content"
      workload_identity = {}
    }
  }
}
