module "application_service_accounts" {
  source                              = "../../modules/iam"
  project_id                          = var.project_id
  service_accounts                    = local.gsa_service_accounts
  project_iam_members                 = {}
  custom_roles                        = {}
  service_accounts_no_prevent_destroy = {}
}

locals {
  gsa_service_accounts = {
    fetcher = {
      account_id = "fetcher"
    },
    cloud-onboarding-manager = {
      account_id = "cloud-onboarding-manager"
    }
    issue-fetcher = {
      account_id = "issue-fetcher"
    },
    issues-updater = {
      account_id = "issues-updater"
    },
    modules-issue-ingester = {
      account_id = "modules-issue-ingester"
    },
    uvem-netscan-processor = {
      account_id = "uvem-netscan-processor"
    }
    uvem-vip-api = {
      account_id = "uvem-vip-api"
    }
    uvem-vxp-api = {
      account_id = "uvem-vxp-api"
    }
    notifier = {
      account_id = "notifier"
    }
    ca-collection-coordinator-pod = {
      account_id = "ca-collection-coordinator-pod"
    }
    ca-collection-worker-pod = {
      account_id = "ca-collection-worker-pod"
    }
    ca-collection-eai-worker-pod = {
      account_id = "ca-collection-eai-worker-pod"
    }
    # DSPM
    dspm-cb-lsr = {
      account_id = "dspm-cb-lsr"
    }
    dspm-x-files-momento = {
      account_id = "dspm-x-files-momento"
    }
    dspm-x-files-skinner = {
      account_id = "dspm-x-files-skinner"
    }
    dspm-crespo-resource-listener = {
      account_id = "dspm-crespo-resource-listener"
    }
    dspm-crespo-jose = {
      account_id = "dspm-crespo-jose"
    }
    dspm-crespo-midfielder = {
      account_id = "dspm-crespo-midfielder"
    }
    dspm-crespo-worker = {
      account_id = "dspm-crespo-worker"
    }
    dspm-oo-boarder = {
      account_id = "dspm-oo-boarder"
    }
    dspm-oo-joeker = {
      account_id = "dspm-oo-joeker"
    }
    dspm-oo-ready-job-evaluator = {
      account_id = "dspm-oo-ready-job-evaluator"
    }
    dspm-oo-genie = {
      account_id = "dspm-oo-genie"
    }
    dspm-oo-worker = {
      account_id = "dspm-oo-worker"
    }
    dspm-fda-api = {
      account_id = "dspm-fda-api"
    }
    dspm-fda-lsr = {
      account_id = "dspm-fda-lsr"
    }
    dspm-fda-worker = {
      account_id = "dspm-fda-worker"
    }
    dspm-dpc = {
      account_id = "dspm-dpc"
    }
    dspm-mac = {
      account_id = "dspm-mac"
    }
    dspm-mac-worker = {
      account_id = "dspm-mac-worker"
    }
    dspm-dacs = {
      account_id = "dspm-dacs"
    }
    dspm-dacs-ts = {
      account_id = "dspm-dacs-ts"
    }
    dspm-worker = {
      account_id = "dspm-worker"
    }
    dspm-datasource-worker = {
      account_id = "dspm-datasource-worker"
    }
    dspm-datasource = {
      account_id = "dspm-datasource"
    }
    dspm-voyager = {
      account_id = "dspm-voyager"
    }
    ##
    analytics-po = {
      account_id = "analytics-po"
    }
    analytics-ae = {
      account_id = "analytics-ae"
    }
    analytics-d = {
      account_id = "analytics-d"
    }
    # CIEM
    ciem-access-table-cron-job = {
      account_id = "ciem-access-table-cron-job"
    }
    ciem-account-manager = {
      account_id = "ciem-account-manager"
    }
    ciem-api = {
      account_id = "ciem-api"
    }
    ciem-db-maintenance = {
      account_id = "ciem-db-maintenance"
    }
    ciem-epc = {
      account_id = "ciem-epc"
    }
    ciem-ipc = {
      account_id = "ciem-ipc"
    }
    ciem-lap = {
      account_id = "ciem-lap"
    }
    ciem-pre-calculation = {
      account_id = "ciem-pre-calculation"
    }
    ciem-rule-scanner-aispm = {
      account_id = "ciem-rule-scanner-aispm"
    }
    ciem-rule-scanner-ciem = {
      account_id = "ciem-rule-scanner-ciem"
    }
    ciem-rule-scanner-dspm = {
      account_id = "ciem-rule-scanner-dspm"
    }
    ciem-static-evidence = {
      account_id = "ciem-static-evidence"
    }
    # CWP
    cwp-api = {
      account_id = "cwp-api"
    }
    cwp-core-asset-analyzer = {
      account_id = "cwp-core-asset-analyzer"
    }
    cwp-sp-bc-object-handler = {
      account_id = "cwp-sp-bc-object-handler"
    }
    cwp-fpp-worker = {
      account_id = "cwp-fpp-worker"
    }
    cwp-fpp-policy-evaluator = {
      account_id = "cwp-fpp-policy-evaluator"
    }
    cwp-fpp-dispatcher = {
      account_id = "cwp-fpp-dispatcher"
    }
    cwp-rules-management = {
      account_id = "cwp-rules-management"
    }
    cwp-scan-results-enricher = {
      account_id = "cwp-scan-results-enricher"
    }
    # CAS
    cas-billing-api = {
      account_id = "billing-cas"
    }
    cas-scans-management = {
      account_id = "scans-management-cas"
    }
    cas-policies-api = {
      account_id = "policies-cas"
    }
    persistence-flow-cas = {
      account_id = "persistence-flow-cas"
    }
    cas-customers-management-api = {
      account_id = "customers-management-cas"
    }
    cas-scans = {
      account_id = "scans-cas"
    }
    cas-detection-rules = {
      account_id = "detection-rules-cas"
    }
    # XSOAR MT
    xsoar-workers-router = {
      account_id = "xsoar-workers-router"
    }
    xsoar-workers-gateway = {
      account_id = "xsoar-workers-gateway"
    }
    task-scheduling = {
      account_id = "task-scheduling"
    }
    argo-controller = {
      account_id = "argo-controller"
    }
    argo-server = {
      account_id = "argo-server"
    }
    # Agent Management
    agent-api = {
      account_id = "agent-api"
    }
    log-processor = {
      account_id = "log-processor"
    }
    # APISEC MT
    apisec-enricher-service = {
      account_id = "apisec-enricher-service"
    }
    apisec-asset-manager = {
      account_id = "apisec-asset-manager"
    }
    apisec-risk-engine = {
      account_id = "apisec-risk-engine"
    }
    apisec-grouping-service = {
      account_id = "apisec-grouping-service"
    }
    apisec-bff-service = {
      account_id = "apisec-bff-service"
    }
    itdr-api = {
      account_id = "itdr-api"
    }
    itdr-asset-retention = {
      account_id = "itdr-asset-retention"
    }
    itdr-asset-updates-syncer = {
      account_id = "itdr-asset-updates-syncer"
    }
    itdr-audits = {
      account_id = "itdr-audits"
    }
    itdr-bigquery-migrator = {
      account_id = "itdr-bigquery-migrator"
    }
    itdr-ca-baseline-cron = {
      account_id = "itdr-ca-baseline-cron"
    }
    itdr-ca-realtime-cron = {
      account_id = "itdr-ca-realtime-cron"
    }
    itdr-cap-block-responder = {
      account_id = "itdr-cap-block-responder"
    }
    itdr-daily-risk-cron = {
      account_id = "itdr-daily-risk-cron"
    }
    itdr-data-pipeline = {
      account_id = "itdr-data-pipeline"
    }
    itdr-data-syncer = {
      account_id = "itdr-data-syncer"
    }
    itdr-risk-processor-service = {
      account_id = "itdr-risk-processor-service"
    }
    itdr-secret-rotation-cron = {
      account_id = "itdr-secret-rotation-cron"
    }
    # Cloudsec 
    cloudsec-dashboard-api = {
      account_id = "dashboard-api"
    }
    rule-management = {
      account_id = "rule-management"
    }
    cloud-api-service = {
      account_id = "cloud-api-service"
    }
    cloudsec-verdict-manager = {
      account_id = "verdict-manager"
    }
    cloudsec-xspm-scanner = {
      account_id = "xspm-scanner"
    }
    cloudsec-search-and-investigate = {
      account_id = "search-and-investigate"
    }
    prometheus = {
      account_id = "prometheus"
    }
  }
}


resource "google_service_account_iam_binding" "ksa_gsa_iam_binding" {
  for_each = local.gsa_service_accounts
  service_account_id = module.application_service_accounts.service_account_id[each.key]
  role               = "roles/iam.workloadIdentityUser"
  members            = ["serviceAccount:${var.project_id}.svc.id.goog[xdr-mt/${each.key}]"]
}