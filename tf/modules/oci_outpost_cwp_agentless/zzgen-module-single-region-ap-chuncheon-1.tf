# DO NOT EDIT - automatically generated from templates/module-region.tf.tmpl

#provider "oci" {
#  alias  = "ap-chuncheon-1"

  # unlike AWS, if the region is not subscribed in the tenant, the provider does not generate an error.
  # so this is safe to generate for all regions.
#  region = "ap-chuncheon-1"
#}

module "single_region_ap-chuncheon-1" {
  for_each = toset(contains(keys(local.enabled_regions), "ap-chuncheon-1") ? ["enabled"] : [])

  source = "./single_region"

  compartment_ocid               = var.compartment_ocid
  region                         = "ap-chuncheon-1"
  objectstorage_bucket_namespace = data.oci_objectstorage_namespace.main.namespace
  project_id                     = var.oci_project_id
  providers = {
    oci = oci.ap-chuncheon-1
  }
}

locals {
  notification_output_regional_ap-chuncheon-1 = length(module.single_region_ap-chuncheon-1) > 0 ? [
    {
      name = "ap-chuncheon-1"
      regional_resources = {
        scanners_network_security_group_id       = module.single_region_ap-chuncheon-1["enabled"].scanners_network_security_group_id
        scanners_subnet_id                       = module.single_region_ap-chuncheon-1["enabled"].scanners_subnet_id
        communication_object_storage_bucket_name = module.single_region_ap-chuncheon-1["enabled"].communication_object_storage_bucket_name
        artifact_object_storage_bucket_name      = module.single_region_ap-chuncheon-1["enabled"].artifact_object_storage_bucket_name
        communication_stream_id                  = module.single_region_ap-chuncheon-1["enabled"].communication_stream_id
        communication_stream_endpoint            = module.single_region_ap-chuncheon-1["enabled"].communication_stream_endpoint
        availability_domains                     = module.single_region_ap-chuncheon-1["enabled"].availability_domains
        egress_network_security_group_id         = module.single_region_ap-chuncheon-1["enabled"].egress_network_security_group_id
        reserved_public_ip                       = module.single_region_ap-chuncheon-1["enabled"].reserved_public_ip
        kms_vault_id                             = module.single_region_ap-chuncheon-1["enabled"].kms_master_vault_id
        kms_key_id                               = module.single_region_ap-chuncheon-1["enabled"].kms_master_key_id        
      }
  }] : []
}