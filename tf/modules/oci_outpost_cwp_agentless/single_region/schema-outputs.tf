output "scanners_network_security_group_id" {
  value = oci_core_network_security_group.cwp_ads.id
}

output "scanners_subnet_id" {
  value = oci_core_subnet.flat.id
}

output "communication_object_storage_bucket_name" {
  value = oci_objectstorage_bucket.communication.name
}

output "artifact_object_storage_bucket_name" {
  value = oci_objectstorage_bucket.artifact.name
}

output "communication_stream_id" {
  value = oci_streaming_stream.communication_bucket_updates.id
}

output "communication_stream_endpoint" {
  value = oci_streaming_stream.communication_bucket_updates.messages_endpoint
}

output "availability_domains" {
  value = local.availability_domains
}

output "egress_network_security_group_id" {
  value = oci_core_network_security_group.proxy.id
}
 
output "reserved_public_ip" {
  value = oci_core_public_ip.outpost_public_ip.ip_address
}

output "kms_master_vault_id" {
  description = "The generated ocid of the KMS Master Vault."
  value       = oci_kms_vault.kms_master_vault.id
}

output "kms_master_key_id" {
  description = "The generated ocid of the KMS Master Key."
  value       = oci_kms_key.kms_master_key.id
}