#provider "oci" {
#  alias  = "$REGION"

  # unlike AWS, if the region is not subscribed in the tenant, the provider does not generate an error.
  # so this is safe to generate for all regions.
#  region = "$REGION"
#}

module "single_region_$REGION" {
  for_each = toset(contains(keys(local.enabled_regions), "$REGION") ? ["enabled"] : [])

  source = "./single_region"

  compartment_ocid               = var.compartment_ocid
  region                         = "$REGION"
  objectstorage_bucket_namespace = data.oci_objectstorage_namespace.main.namespace
  project_id                     = var.oci_project_id
  providers = {
    oci = oci.$REGION
  }
}

locals {
  notification_output_regional_$REGION = length(module.single_region_$REGION) > 0 ? [
    {
      name = "$REGION"
      regional_resources = {
        scanners_network_security_group_id       = module.single_region_$REGION["enabled"].scanners_network_security_group_id
        scanners_subnet_id                       = module.single_region_$REGION["enabled"].scanners_subnet_id
        communication_object_storage_bucket_name = module.single_region_$REGION["enabled"].communication_object_storage_bucket_name
        artifact_object_storage_bucket_name      = module.single_region_$REGION["enabled"].artifact_object_storage_bucket_name
        communication_stream_id                  = module.single_region_$REGION["enabled"].communication_stream_id
        communication_stream_endpoint            = module.single_region_$REGION["enabled"].communication_stream_endpoint
        availability_domains                     = module.single_region_$REGION["enabled"].availability_domains
        egress_network_security_group_id         = module.single_region_$REGION["enabled"].egress_network_security_group_id
        reserved_public_ip                       = module.single_region_$REGION["enabled"].reserved_public_ip
        kms_vault_id                             = module.single_region_$REGION["enabled"].kms_master_vault_id
        kms_key_id                               = module.single_region_$REGION["enabled"].kms_master_key_id        
      }
  }] : []
}