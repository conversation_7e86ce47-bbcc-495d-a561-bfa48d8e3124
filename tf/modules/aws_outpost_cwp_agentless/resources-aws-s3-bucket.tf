# This bucket is used to hold the onboarding Cloud Formation template. 
# The template is uploaded to the bucket by the application that runs in the cortex single-tenant.

resource "aws_s3_bucket" "s3_cloudformation_template" {
  count         = var.create_cf_templates_bucket ? 1 : 0
  bucket        = local.names.s3_cf_template_bucket_name
  force_destroy = true
  tags          = local.merged_tags
  provider      = aws.us-east-1
}

resource "aws_s3_bucket_public_access_block" "cloudformation_bucket_access_block" {
  count                   = var.create_cf_templates_bucket ? 1 : 0
  bucket                  = aws_s3_bucket.s3_cloudformation_template[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
  provider                = aws.us-east-1
}

moved {
  from = aws_s3_bucket.s3_cloudformation_template
  to   = aws_s3_bucket.s3_cloudformation_template[0]
}