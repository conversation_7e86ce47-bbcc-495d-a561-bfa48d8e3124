locals {
  _vm_image = "projects/xdr-shared-cicdvms-prod-eu-01/global/images/prisma-outpost-proxy-public-x86-202509021607"
}

resource "random_shuffle" "zone_selector" {
  input        = data.google_compute_zones.available.names
  result_count = 1
}

resource "google_compute_instance" "proxy_vm" {
  allow_stopping_for_update = true
  machine_type              = lookup(var.overrides, "gcp_outpost_cas_proxy_vm_machine_type", var.viso_env == "dev" ? "e2-micro" : "e2-medium")
  name                      = "egress-proxy-cas" # must start with egress-proxy-* for the public IP restriction exception
  zone                      = one(random_shuffle.zone_selector.result)

  boot_disk {
    initialize_params {
      image = local._vm_image
      size  = lookup(var.overrides, "gcp_outpost_cas_proxy_vm_boot_disk_size", 20)
      type  = "pd-balanced"
    }
  }

  network_interface {
    network    = google_compute_network.cas_gke_network.name
    subnetwork = google_compute_subnetwork.cas_gke_subnet.name
    access_config {
      nat_ip = google_compute_address.proxy_vm_ip.address
    }
  }

  service_account {
    email  = google_service_account.proxy_vm_cas.email
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }

  # reference: https://gitlab.xdr.pan.local/xdr/development/cwp/scan-platform/workload-orchestration/sp-workload-orchestrator/-/blob/dev/internal/service/dynamic_resources/egress/startup_script/startup-script.tmpl.sh?ref_type=heads#L28
  metadata_startup_script = <<-EOT
      #!/bin/bash
      echo "Setting JWKS and enabling OPA"
      cat << EOF > /etc/opa/opa.env
      EGRESS_JWKS="${var.proxy_vm_jwks}"
      EOF
      systemctl enable --now opa
  EOT

  labels = merge(
    {
      app                      = "cas"
      c7n_gcp_public_ip_exempt = "true" # required for the public IP restriction exception
    },
    local.labels.merged
  )

  lifecycle {
    replace_triggered_by = [null_resource.proxy_vm_image_change_trigger]
  }
}

resource "null_resource" "proxy_vm_image_change_trigger" {
  triggers = {
    image_id = local._vm_image
  }
}