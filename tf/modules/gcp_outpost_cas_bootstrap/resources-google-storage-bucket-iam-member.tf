# approved by <PERSON> to not use CTS token, when interacting with the k8s Control Plane from single tenant
# thread in #cas-cortex-sca-dynamic-analysis - https://panw-global.slack.com/archives/C086U0DME05/p1747808260305199
resource "google_storage_bucket_iam_member" "sca_scanner_cas_object_user" {
  bucket = google_storage_bucket.cas_communication.name
  role   = "roles/storage.objectUser"
  member = "serviceAccount:${var.sca_scanner_cas_email}"
}

resource "google_storage_bucket_iam_member" "scanner_orchestration_cas_object_user" {
  bucket = google_storage_bucket.cas_communication.name
  role   = "roles/storage.objectUser"
  member = "serviceAccount:${var.scanner_orchestration_cas_email}"
}

resource "google_storage_bucket_iam_member" "pre_scan_periodic_cas_object_admin" {
  bucket = google_storage_bucket.cas_communication.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${var.pre_scan_periodic_cas_email}"
}

resource "google_storage_bucket_iam_member" "pre_scan_pr_cas_object_admin" {
  bucket = google_storage_bucket.cas_communication.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${var.pre_scan_pr_cas_email}"
}

resource "google_storage_bucket_iam_member" "sca_scanner_job_cas_write" {
  bucket = var.outpost_communication_bucket_name
  role   = "roles/storage.objectCreator"
  member = "serviceAccount:${google_service_account.sca_scanner_job_cas_sa.email}"

  condition {
    title       = "Only permit writing to output/"
    description = "Grant write access only to the output/ folder"
    expression  = "resource.name.startsWith(\"projects/_/buckets/${var.outpost_communication_bucket_name}/objects/output/\")"
  }
}