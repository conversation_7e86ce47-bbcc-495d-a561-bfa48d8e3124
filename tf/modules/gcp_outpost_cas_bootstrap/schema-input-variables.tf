variable "custom_resources_tags" {}
variable "lcaas" {}
variable "outpost_communication_bucket_name" {}
variable "outpost_project_id" {}
variable "overrides" {}
variable "project_id" {}
variable "project_prefix" {}
variable "proxy_vm_jwks" {}
variable "region" {}
variable "viso_env" {}
variable "xdr_env" {}
variable "sca_scanner_cas_email" {}
variable "scanner_orchestration_cas_email" {}
variable "sca_artifactory_management_cas_email" {}
variable "pre_scan_periodic_cas_email" {}
variable "pre_scan_pr_cas_email" {}

variable "namespace" {
  type    = string
  default = "cas"
}