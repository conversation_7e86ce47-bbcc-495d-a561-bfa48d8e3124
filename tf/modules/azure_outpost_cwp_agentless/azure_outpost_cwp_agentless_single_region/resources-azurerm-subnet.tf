# Configuring the Outpost subnet where scanner VMs are running on.

resource "random_string" "subnet_suffix" {
  length  = 16
  special = false
  upper   = false
}

resource "azurerm_subnet" "outpost_subnet" {
  resource_group_name               = var.resource_group_name
  virtual_network_name              = azurerm_virtual_network.outpost_vnet.name
  name                              = random_string.subnet_suffix.result
  default_outbound_access_enabled   = false
  service_endpoints                 = local.service_endpoints_list
  address_prefixes                  = ["${local.azurerm_virtual_network.location_cidr_map[var.location]}/20"]
  private_endpoint_network_policies = "NetworkSecurityGroupEnabled"

  lifecycle {
    ignore_changes = [ name ]
  }
}