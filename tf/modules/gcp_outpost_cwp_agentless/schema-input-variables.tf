variable "firewall_add_iap_ingress" {
  type        = bool
  description = "Whether or not to add a rule to the outpost firewall permitting ingress from GCP Identity-Aware Proxy (IAP). Useful for debugging purposes."
  default     = false
}

variable "upload_output_url" {
  description = "The URL to which the outpost registration should be sent"
  type        = string
}

variable "outpost_registration_authorization_header_value" {
  description = "The value to be attached to the Authorization header for the outpost registration call, so that it can be authenticated"
  type        = string
  default     = ""
}

variable "outpost_registration_url_is_pubsub_topic" {
  description = "If set to true, then the outpost registration notification payload will be wrapped in an envelope to publish to Pub/Sub"
  type        = bool
  default     = false
}

variable "region_allowlist" {
  description = "Which GCP regions to allow. If specified, only the regions in this list will be enabled. If not specified, all known regions will be enabled."
  type        = list(string)
  default     = []
}

variable "region_denylist" {
  description = "Which GCP regions to forbid. If specified, none of the regions in this list will be enabled."
  type        = list(string)
  default     = []
}

variable "cts_sa_email" {
  description = "GCP service account (email address) which should be granted access to the Cortex service account."
  type        = string
}

variable "cts_unique_id" {
  description = "Unique id of the CTS service account"
  type        = string
  default     = "**********"
}

variable "template_id" {
  description = "template id, used for uploaded artifacts"
}

variable "project_id" {
  description = "Input for non-managed outpost"
}

variable "custom_resources_tags" {
  description = "Custom resource tags"
  type        = map(string)
  default = {}
}

variable "url_is_gcs_signed_url" {
  default     = true
  description = "Control variable for outpost notification request"
  type        = bool
}

variable "outpost_schema_json" {
  description = "path to JSON schema, used for outpost registration notification"
  default     = "./iacoutput.json"
}
