resource "google_project_iam_custom_role" "dspm_big_query" {
  role_id     = "dspmBigQuery"
  title       = "DSPM - BigQuery"
  description = "Permits DSPM to have BigQuery permissions for scanning BigQuery datasets."
  permissions = [
    "bigquery.jobs.create",
  ]
}

resource "google_project_iam_custom_role" "dspm_cloud_sql" {
  role_id     = "dspmCloudSql"
  title       = "DSPM - CloudSQL"
  description = "Permits DSPM to have CloudSQL permissions for scanning CloudSQL databases."
  permissions = [
    "cloudsql.databases.create",
    "cloudsql.databases.delete",
    "cloudsql.databases.update",
    "cloudsql.databases.get",
    "cloudsql.databases.list",
    "cloudsql.instances.list",
    "cloudsql.instances.get",
    "cloudsql.instances.connect",
    "cloudsql.instances.create",
    "cloudsql.instances.delete",
    "cloudsql.instances.login",
    "cloudsql.instances.restart",
    "cloudsql.instances.restoreBackup",
    "cloudsql.instances.update",
    "cloudsql.instances.createTagBinding",
    "cloudsql.instances.deleteTagBinding",
    "cloudsql.instances.listTagBindings",
    "cloudsql.users.create",
    "cloudsql.users.delete",
    "cloudsql.users.update",
    "cloudsql.users.get",
    "cloudsql.users.list",
  ]
}

resource "google_project_iam_custom_role" "dspm_secret_manager" {
  role_id     = "dspmSecretManager"
  title       = "DSPM - Secret Manager"
  description = "Permits DSPM to have Secret Manager permissions for maintaining credentials to connect to scan targets."
  permissions = [
    "secretmanager.secrets.create",
    "secretmanager.secrets.update",
    "secretmanager.secrets.delete",
    "secretmanager.secrets.get",
    "secretmanager.secrets.list",
    "secretmanager.versions.access",
    "secretmanager.versions.add",
    "secretmanager.versions.destroy",
    "secretmanager.versions.disable",
    "secretmanager.versions.enable",
    "secretmanager.versions.get",
    "secretmanager.versions.list",
  ]
}

resource "google_project_iam_custom_role" "dspm_customer_data_storage" {
  role_id     = "dspmCustomerDataStorage"
  title       = "DSPM - Storage (write to customer-data bucket)"
  description = "Grants permissions to the service account (DSPM) used by GCP managed services to write export results to a customer-data bucket in the Outpost project."
  permissions = [
    "storage.objects.create",
    "storage.objects.delete"
  ]
}

resource "google_project_iam_custom_role" "dspm_list_customer_data_storage" {
  role_id     = "dspmListCustomerDataStorage"
  title       = "DSPM - Storage (list customer-data bucket)"
  description = "Grants permissions to Cortex DSPM to list the objects written by GCP managed services to a customer-data bucket in the Outpost project."
  permissions = ["storage.objects.list"]
}

resource "google_project_iam_custom_role" "dspm_delete_customer_data_storage" {
  role_id     = "dspmDeleteCustomerDataStorage"
  title       = "DSPM - Storage (delete objects in customer-data bucket)"
  description = "Grants permissions to Cortex DSPM to delete the objects written by GCP managed services to a customer-data bucket in the Outpost project."
  permissions = ["storage.objects.delete"]
}

resource "google_project_iam_custom_role" "dspm_cloud_kms" {
  role_id     = "dspmCloudKMS"
  title       = "DSPM - Cloud KMS"
  description = "Grants permissions to Cortex DSPM for Cloud KMS operations"
  permissions = [
    "cloudkms.cryptoKeyVersions.create",
    "cloudkms.cryptoKeyVersions.destroy",
    "cloudkms.cryptoKeyVersions.get",
    "cloudkms.cryptoKeyVersions.list",
    "cloudkms.cryptoKeyVersions.update",
    "cloudkms.cryptoKeyVersions.useToDecrypt",
    "cloudkms.cryptoKeyVersions.useToEncrypt",
    "cloudkms.cryptoKeys.create",
    "cloudkms.cryptoKeys.setIamPolicy",
    "cloudkms.cryptoKeys.getIamPolicy",
    "cloudkms.cryptoKeys.update",
    "cloudkms.keyRings.create"
  ]
}

resource "google_project_iam_custom_role" "scan_runner_self_destroy" {
  role_id     = "scanRunnerSelfDestroy"
  title       = "Scan Runner - allow VM to destroy itself"
  description = "Grants the scanner VM permission to destroy itself."
  permissions = ["compute.instances.delete"]
}

resource "google_project_iam_custom_role" "scan_runner_bucket_role" {
  role_id     = "scanRunnerBucketRole"
  title       = "Scan Runner - custom role for bucket-based communication"
  description = "Grants permissions to the scan runner for storage bucket operations"
  permissions = [
    "storage.objects.get",
    "storage.objects.create",
  ]
}
