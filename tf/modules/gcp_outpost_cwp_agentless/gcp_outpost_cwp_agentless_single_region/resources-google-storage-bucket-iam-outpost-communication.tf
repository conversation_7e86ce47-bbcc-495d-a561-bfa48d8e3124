# Configuring storage premissions on the outpost communication buckets, to allow Scanner VMs and Cortex Platform to execute the bucket-communication process.

resource "google_storage_bucket_iam_member" "outpost_communication_scan_runner" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.scan_runner_service_account_email}"
}

resource "google_storage_bucket_iam_member" "outpost_communication_dspm_scan_runner" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.dspm_scan_runner_service_account_email}"
}

resource "google_storage_bucket_iam_member" "outpost_communication_proxy_creator" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.proxy_service_account_email}"

  condition {
    title      = "Only permit writing to output/logs"
    expression = "resource.name.startsWith(\"projects/_/buckets/${google_storage_bucket.outpost_communication.name}/objects/output/logs/\")"
  }
}

resource "google_storage_bucket_iam_member" "outpost_communication_registry_scan_runner" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.registry_scan_runner_service_account_email}"
}

resource "google_storage_bucket_iam_member" "outpost_communication_scanner_of_serverless_runner" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.scanner_of_serverless_runner_service_account_email}"
}

resource "google_storage_bucket_iam_member" "outpost_communication_cortex_engine" {
  bucket = google_storage_bucket.outpost_communication.name
  role   = var.scanner_bucket_custom_role
  member = "serviceAccount:${var.cortex_engine_service_account_email}"
}

# Note: it is not necessary to explicitly grant permissions to the GCS service agent to read from the bucket
# in order for Pub/Sub notifications to work.
# ref: https://cloud.google.com/storage/docs/reporting-changes#grant-required-role-to-service-agent
