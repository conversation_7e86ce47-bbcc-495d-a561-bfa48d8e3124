variable "bucket_communication_pubsub_topic_id" {}
variable "compute_network_self_link" {}
variable "cortex_engine_service_account_email" {}
variable "custom_resources_tags" {}
variable "dspm_customer_data_storage_custom_role_id" {}
variable "dspm_delete_customer_data_storage_custom_role_id" {}
variable "dspm_list_customer_data_storage_custom_role_id" {}
variable "dspm_scan_runner_service_account_email" {}
variable "proxy_service_account_email" {}
variable "registry_scan_runner_service_account_email" {}
variable "scanner_of_serverless_runner_service_account_email" {}
variable "region" {}
variable "scan_runner_service_account_email" {}
variable "google_compute_subnetwork_regional_cidr" {}
variable "scanner_bucket_custom_role" {}
