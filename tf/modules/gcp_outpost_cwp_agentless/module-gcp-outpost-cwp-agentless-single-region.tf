# Calling this module allows configuring regional resources for all enabled regions (Hardened VPCs, GCS buckets, Pub/Sub)

module "gcp_outpost_cwp_agentless_single_region" {
  source = "./gcp_outpost_cwp_agentless_single_region"

  for_each = local.enabled_regions

  bucket_communication_pubsub_topic_id               = google_pubsub_topic.bucket_communication.id
  compute_network_self_link                          = google_compute_network.cwp_agentless.self_link
  cortex_engine_service_account_email                = google_service_account.cortex_engine.email
  custom_resources_tags                              = var.custom_resources_tags
  dspm_customer_data_storage_custom_role_id          = google_project_iam_custom_role.dspm_customer_data_storage.id
  dspm_delete_customer_data_storage_custom_role_id   = google_project_iam_custom_role.dspm_delete_customer_data_storage.id
  dspm_list_customer_data_storage_custom_role_id     = google_project_iam_custom_role.dspm_list_customer_data_storage.id
  dspm_scan_runner_service_account_email             = google_service_account.dspm_scan_runner.email
  proxy_service_account_email                        = google_service_account.proxy.email
  registry_scan_runner_service_account_email         = google_service_account.registry_scan_runner.email
  scanner_of_serverless_runner_service_account_email = google_service_account.scanner_of_serverless.email
  scanner_bucket_custom_role                         = google_project_iam_custom_role.scan_runner_bucket_role.id
  region                                             = each.value
  scan_runner_service_account_email                  = google_service_account.scan_runner.email
  google_compute_subnetwork_regional_cidr            = local.google_compute_subnetwork.region_cidr_map[each.value]
  depends_on = [
    terraform_data.forbid_conflicts_region_allowlist_denylist,
    google_pubsub_topic_iam_member.google_storage_project_service_account_on_bucket_communication_pubsub_topic
  ]
}
