data "google_service_account" "cts_sa" {
  provider   = google.st
  account_id = "cts-pod" # hardcoded, must match the service.
}

data "google_service_account" "sca_scanner_cas" {
  provider   = google.st
  account_id = "sca-scanner-cas"
}

data "google_service_account" "scanner_orchestration_cas" {
  provider   = google.st
  account_id = "scanner-orchestration-cas"
}

data "google_service_account" "sca_artifactory_management_cas" {
  provider   = google.st
  account_id = "sca-artifactory-management-cas"
}

data "google_service_account" "pre_scan_periodic_cas" {
  provider   = google.st
  account_id = "pre-scan-periodic-cas"
}

data "google_service_account" "pre_scan_pr_cas" {
  provider   = google.st
  account_id = "pre-scan-pr-cas"
}