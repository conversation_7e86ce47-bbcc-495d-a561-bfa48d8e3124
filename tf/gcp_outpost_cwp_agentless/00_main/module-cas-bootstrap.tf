module "cas_bootstrap" {
  for_each = toset(local.cas.enabled ? ["enabled"] : [])

  source = "../../modules/gcp_outpost_cas_bootstrap"

  project_id                           = var.project_id
  project_prefix                       = var.project_prefix
  custom_resources_tags                = local.cloudops_tags
  lcaas                                = var.lcaas
  region                               = var.region
  outpost_communication_bucket_name    = module.bootstrap.google_storage_bucket_outpost_communication_name
  outpost_project_id                   = var.outpost_project_id
  overrides                            = var.overrides
  viso_env                             = var.viso_env
  xdr_env                              = var.xdr_env
  proxy_vm_jwks                        = base64encode(data.google_kms_crypto_key_version.sp_jwt.public_key[0].pem)
  sca_scanner_cas_email                = data.google_service_account.sca_scanner_cas.email
  scanner_orchestration_cas_email      = data.google_service_account.scanner_orchestration_cas.email
  sca_artifactory_management_cas_email = data.google_service_account.sca_artifactory_management_cas.email
  pre_scan_periodic_cas_email          = data.google_service_account.pre_scan_periodic_cas.email
  pre_scan_pr_cas_email                = data.google_service_account.pre_scan_pr_cas.email

  providers = {
    google = google.outpost
  }
}