{
        "Server": {
                "ExternalHostname": "${external_fqdn}",
                "HttpPort":"8080",
                "XsoarNG": {
                    "enabled": ${is_xsoar}
                },
                "xsiam": {
                    "enabled": ${is_xsiam}
                },
                "remote": {
                    "type": "server"
                },
                "externalEntities": "audit, incident, configuration",
                "confDefinitions":{
                    "useOverrideFile":true
                },
                "OneTimeConfPath": "/usr/local/demisto/otc.conf.json",
                "CloudStorage": {
                    "BucketName": "${xsoarconf_bucket_name}",
                    "ParentPath": "xsoar",
                    "Platform": "Google-Storage"
                }
        },
        "ContentSecrets": {
                "WildFire-Reports": {
                    "token": "${hashed_wildfire_key}"
                },
                "Http_Connector": {
                    "token": "${hashed_xdr_http_token}",
                    "url": "${external_fqdn}"
                }
        },
        "elasticsearch": {
               "debug": {
                   "enableQuery": true
               },
               "periodicalBackup" : {
                   "enabled": ${backup_enable},
                   "bucket_name": "${backup_bucket}",
                   "repoName" : "my_gcs_repository",
                   "snapshotName": "es-backup",
                   "schedule" : "${backup_schedule}",
                   "retention.expireAfter" : "30d",
                   "retention.minCount" : 5,
                   "retention.maxCount" : 50
               },
               "defaultReplicasPerIndex":0,
               "enabled": true,
               "indexPrefix": "",
               "maxResultWindow": 10000,
               "proxy": false,
               "refreshOnBulkInsert": true,
               "responseHeaderTimeoutSeconds": 30,
               "username": "elastic",
               "url": "https://elastic-es-xsoar:9200",
               "insecure": true
        },
        "engine.load.module.timeout": 30,
        "Monitoring.monitoringKey": "${hashed_monitoring_key}",
        "defaultEngines": [{"id": "${engine_id}","name": "DefaultRunner","isDefault": true}],
        "workers.count.Tasks": "${workers_count}",
        "marketplace.initial.sync.delay": 0,
        %{~ if is_xsoar ~}
        "marketplace.bootstrap.bypass.url": "${marketplace_bootstrap_bypass_url}",
        "marketplace.bootstrap.url": "${marketplace_bootstrap_bypass_url}",
        %{~ else ~}
        "marketplace.v2.url": "${marketplace_v2_url}",
        %{~ endif ~}
        %{~ if is_xpanse ~}
        "task.clear.non.executed.enabled" : true,
        "investigation.task.partial.index": 7,
        "application.statistics.update.disable" : true,
        %{~ endif ~}
        "http.tunnel.over.ws.url": "http://xdr-st-${lcaas_id}-engine-hub:8081",
        %{~ if is_xsoar_6_migration ~}
        "migration.filesEncryption.enabled": true,
        "migration.filesEncryption.keyInBase64": "${migration_token}",
        "legacyEngines": [{"id": "${gcpconf_project_id}-engine-1000", "name": "Legacy IP Engine"}],
        %{~ endif ~}
        %{~ if is_xsoar ~}
        "http.tunnel.over.ws.domain.whitelist": "${xsoar_domain_whitelist}",
        "http.tunnel.over.ws.port.whitelist": "${xsoar_port_whitelist}",
        %{~ endif ~}
        "external.saml.session.sso.asc": "${marketplace_sso_asc}",
        "external.saml.session.sso.endpoint": "${marketplace_sso_endpoint}",
        "marketplace.premium.gateway.service.url":"${marketplace_premium_content_gateway_service_url}",
        "marketplace.subscription.service.url": "${marketplace_subscription_service_url}",
        "auth.jwt.enabled": true,
        "folders.temp": "/home/<USER>/temp",
        "data.collection.external.ask.parent.route": "/external/ask",
        "data.collection.external.form.parent.route": "/external/form",
        "container.engine.type": "podman",
        "custom.fields.validate.grid.values": true,
        "db.index.entry.disable": true,
        "unit42intel.service.url": "${unit42intel_service_url}",
        "server.engine.base.url": "api-${external_fqdn}/xsoar",
        "allow.all.get.license.fields": true,
        "allow.custom.internal.apicalls": true,
        "repo.mysql": true,
        "repo.olap.incidents": true,
        "repo.mysql.investigations": true,
        "server.inv.playbook.cache.enabled": false,
        "tim.mysql.replicate.indicators.failures.subscription": "xsoar-tim-indicator-failures-sub-${lcaas_id}",
        "tim.mysql.replicate.indicators.failures.topic": "xsoar-tim-indicator-failures-topic-${lcaas_id}",
        "xsoar.ng.engine.hub.enabled": true,
        "xsoar.ng.playbook.topic.start": "playbook-runner-start",
        "xsoar.ng.playbook.publisher": true,
        "xsoarconf.ng.content.url": "http://xdr-st-${lcaas_id}-xsoar-content:5566",
        "xsoarconf.ng.gcp.git": ${gcp_git},
        "xsoar.ng.redis.message.size": "6000000",
        "xsoarconf.ng.vc.branch": "${repo_branch}",
        "xsoarconf.ng.vc.enabled": "${enable_vc}",
        "xsoarconf.ng.vc.external.content.url": "https://api-${parent_external_fqdn}/xsoar/vc/bundle",
        "xsoarconf.ng.vc.git.url": "${git_url}",
        "xsoarconf.ng.is.dev": "${customer_dev_tenant}"
}
