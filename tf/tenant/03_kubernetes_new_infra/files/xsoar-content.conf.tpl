{
        "Server": {
                "ExternalHostname": "${external_fqdn}",
                "HttpPort":"8080",
                "XsoarNG": {
                    "enabled": ${is_xsoar}
                },
                "xsiam": {
                    "enabled": ${is_xsiam}
                },
                "remote": {
                    "type": "server"
                },
                "externalEntities": "audit, incident, configuration",

                "OneTimeConfPath": "/usr/local/demisto/otc.conf.json",
                "CloudStorage": {
                    "BucketName": "${xsoarconf_bucket_name}",
                    "ParentPath": "xsoar",
                    "Platform": "Google-Storage"
                }
        },
        "ContentSecrets": {
                "WildFire-Reports": {
                    "token": "${hashed_wildfire_key}"
                },
                "Http_Connector": {
                    "token": "${hashed_xdr_http_token}",
                    "url": "${external_fqdn}"
                }
        },
        "elasticsearch": {
               "debug": {
                   "enableQuery": true
                 },
               "enabled": true,
               "indexPrefix": "",
               "maxResultWindow": 10000,
               "proxy": false,
               "refreshOnBulkInsert": true,
               "responseHeaderTimeoutSeconds": 30,
               "username": "elastic",
               "url": "https://elastic-es-xsoar:9200",
               "insecure": true
        },
        "Monitoring.monitoringKey": "${hashed_monitoring_key}",
        "defaultEngines": [{"id": "${engine_id}","name": "DefaultRunner","isDefault": true}],
        "workers.count.Tasks": "${workers_count}",
        "content.validate.docker.images": false,
        "marketplace.initial.sync.delay": 0,
        %{~ if is_xsoar ~}
        "marketplace.bootstrap.bypass.url": "${marketplace_bootstrap_bypass_url}",
        "marketplace.bootstrap.url": "${marketplace_bootstrap_bypass_url}",
        %{~ else ~}
        "marketplace.v2.url": "${marketplace_v2_url}",
        %{~ endif ~}
        %{~ if is_xsoar_6_migration ~}
        "migration.filesEncryption.enabled": true,
        "migration.filesEncryption.keyInBase64": "${migration_token}",
        %{~ endif ~}
        "http.tunnel.over.ws.url": "http://xdr-st-${lcaas}-engine-hub:8081",
        "external.saml.session.sso.endpoint": "${marketplace_sso_endpoint}",
        "marketplace.premium.gateway.service.url":"${marketplace_premium_content_gateway_service_url}",
        "marketplace.subscription.service.url": "${marketplace_subscription_service_url}",
        "auth.jwt.enabled": true,
        "folders.temp": "/home/<USER>/temp",
        "data.collection.external.ask.parent.route": "/external/ask",
        "data.collection.external.form.parent.route": "/external/form",
        "container.engine.type": "podman",
        "custom.fields.validate.grid.values": true,
        "db.index.entry.disable": true,
        "unit42intel.service.url": "${unit42intel_service_url}",
        "server.engine.base.url": "api-${external_fqdn}/xsoar",
        "allow.all.get.license.fields": true,
        "allow.custom.internal.apicalls": true,
        "xsoarconf.ng.gcp.git": ${gcp_git},
        "xsoarconf.ng.vc.branch": "${repo_branch}",
        "xsoarconf.ng.vc.enabled": "${enable_vc}",
        "xsoarconf.ng.vc.git.url": "${git_url}",
        "xsoarconf.ng.is.dev": "${customer_dev_tenant}"
}