{
        "Server": {
                "ExternalHostname": "${external_fqdn}",
                "HttpPort":"8080",
                "xsiam": {
                    "enabled": true
                },
                "OneTimeConfPath": "/usr/local/demisto/otc.conf.json",
                "CloudStorage": {
                    "BucketName": "${xsoarconf_alerts_artifacts_bucket_name}",
                    "ArtifactsPath": "artifacts",
                    "Platform": "Google-Storage"
                }
        },
        "ContentSecrets": {
                "Core": {
                  "gcpconf_papi_bucket": "${gcpconf_papi_bucket}",
                  "gcpconf_project_id": "${gcpconf_project_id}"
                },
                "WildFire-Reports": {
                    "token": "${hashed_wildfire_key}"
                },
                "Http_Connector": {
                    "token": "${hashed_xdr_http_token}",
                    "url": "${external_fqdn}"
                }
        },
        "Monitoring.monitoringKey": "${hashed_monitoring_key}",
        "defaultEngines": [{"id": "${engine_id}","name": "DefaultRunner","isDefault": true}],
        "workers.count.Tasks": "${workers_count}",
        "marketplace.initial.sync.delay": ${marketplace_initial_sync_delay},
        "marketplace.bootstrap.bypass.url": "${marketplace_bootstrap_bypass_url}",
        "marketplace.v2.url": "${marketplace_v2_url}",
        "playbook.willnotexecute.old.eval": true,
        "auth.jwt.enabled": true,
        "auth.jwt.skip.claims.validation": true,
        "folders.temp": "/home/<USER>/temp",
        "data.collection.external.ask.parent.route": "/external/ask",
        "data.collection.external.form.parent.route": "/external/form",
        "container.engine.type": "podman",
        "custom.fields.validate.grid.values": true,
        "db.index.entry.disable": true,
        "unit42intel.service.url": "${unit42intel_service_url}",
        "server.engine.base.url": "api-${external_fqdn}/xsoar",
        "allow.all.get.license.fields": true,
        "allow.custom.internal.apicalls": true,
        "default.engine.excluded.integrations.brands": "EDL,Cortex Core - IOC,Cortex Core - IR"
}
