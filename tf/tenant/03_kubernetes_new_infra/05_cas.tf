locals {
  cas_service_accounts = [
    {
      service_account_name = "customers-management-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "cas_policies", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "scans_management", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
        topics = [
          lookup(var.topics_output, "bq_stats_topic", "")
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "source-control-cas"
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "source_control", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "cas_requests_payload", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "ci_build_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "cas_enriched_reports", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "code_to_cloud", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "cas_pubsub_failures", null), "ReadAndWrite"],

        ]
        topics = [
          lookup(var.topics_output, "cas_webhooks", null),
          lookup(var.topics_output, "cas_vcs_enrichment", null),
          lookup(var.topics_output, "cas_cli_git_users_persistence", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cas_integration_entities_lifecycle_events", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr_dlq", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic_dlq", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli_dlq", null),
          lookup(var.topics_output, "cas_post_persistence_pr", null),
          lookup(var.topics_output, "cas_post_persistence_cli", null),
          lookup(var.topics_output, "cas_post_persistence_periodic", null),
          lookup(var.topics_output, "cas_pr_internal_data_collector_topic", null),
          lookup(var.topics_output, "cas_periodic_internal_data_collector_topic", null),
          lookup(var.topics_output, "cas_pr_data_collector_tasks_topic", null),
          lookup(var.topics_output, "cas_periodic_data_collector_tasks_topic", null),
          lookup(var.topics_output, "cas_pr_data_collector_results_topic", null),
          lookup(var.topics_output, "cas_periodic_data_collector_results_topic", null),
          lookup(var.topics_output, "ap_issue_actions_upsert", null),
          lookup(var.topics_output, "ap_issue_actions_ingestion_errors", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_webhooks", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_vcs_enrichment", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_integration_entities_lifecycle_events", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_integration_entities_lifecycle_events", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr_dlq", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr_dlq", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic_dlq", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic_dlq", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli_dlq", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli_dlq", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_persistence_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_persistence_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_cli", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_post_persistence_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pr_internal_data_collector_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_internal_data_collector_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pr_internal_data_collector_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_periodic_internal_data_collector_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pr_data_collector_tasks_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_periodic_data_collector_tasks_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pr_data_collector_tasks_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_data_collector_tasks_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pr_data_collector_worker_res_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_data_collector_worker_res_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pr_data_collector_worker_res_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_periodic_data_collector_worker_res_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pr_data_collector_results_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_periodic_data_collector_results_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "ap_issue_actions_upsert", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_webhooks_sub", null),
          lookup(var.subscriptions_output, "cas_vcs_enrichment_cicd_sub", null),
          lookup(var.subscriptions_output, "cas_integration_entities_lifecycle_events_sub", null),
          lookup(var.subscriptions_output, "cas_cli_git_users_persistence_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_pr_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_cli_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_pr_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_periodic_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_cli_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_pr_errors_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_periodic_errors_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_actions_cli_errors_sub", null),
          lookup(var.subscriptions_output, "cas_post_persistence_pr_sub", null),
          lookup(var.subscriptions_output, "cas_post_persistence_cli_sub", null),
          lookup(var.subscriptions_output, "cas_post_persistence_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_pr_internal_data_collector_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_internal_data_collector_sub", null),
          lookup(var.subscriptions_output, "cas_pr_data_collector_worker_res_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_data_collector_worker_res_sub", null),
          lookup(var.subscriptions_output, "cas_pr_data_collector_tasks_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_data_collector_tasks_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_post_collection_processor_sub", null),
        ]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "application", null), "roles/bigquery.dataOwner"],
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "transporter-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "collectors-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "roles/storage.objectUser"],
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "source-control-platform-bridge"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "detection-rules-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service     = local.metro_v2
        # metro_st_namespace       = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataOwner"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "sbom-management-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "sbom_reports", null), "ReadAndWrite"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "sca-scanner-cas"
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"]
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cas_periodic_scan", null),
          lookup(var.topics_output, "cas_pr_scan", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pr_scan", ""), "roles/pubsub.subscriber"]
        ]
         subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_scan_sca_s_sub", null),
          lookup(var.subscriptions_output, "cas_pr_scan_sca_s_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_sca_findings_s_sub", null),
          lookup(var.subscriptions_output, "cas_pr_scan_sca_findings_s_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq_job_user = true # Allow the service account to run BigQuery jobs
      }

      automount_service_account_token = true
    },
    {
      service_account_name = "persistence-flow-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          lookup(var.topics_output, "cas_persistence_periodic", ""),
          lookup(var.topics_output, "cas_persistence_pr", ""),
          lookup(var.topics_output, "cas_persistence_cli", ""),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "ap_issue_upsert", null),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "deletion_api", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "ap_issue_ingestion_errors", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_ingestion_errors", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "ap_issue_ingest_feedback", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_ingest_feedback", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "dp_finding_ingestion_errors", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_finding_ingestion_errors", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "deletion_api", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_cli", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_pr_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_cli_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_pr_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_cli_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingestion_errors_cas_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingest_feedback_sub_cas", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_cas_sub", null),
          lookup(var.subscriptions_output, "dp_uai_asset_ingestion_errors_cas_sub", null),
          lookup(var.subscriptions_output, "deletion_api_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "dp_scan_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cas_enriched_reports", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "cas_pubsub_failures", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "code_to_cloud", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"]
        ]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "enrichment-manager-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          lookup(var.topics_output, "cas_persistence_periodic", ""),
          lookup(var.topics_output, "cas_persistence_pr", ""),
          lookup(var.topics_output, "cas_persistence_cli", ""),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_enrichment_scanner_reports_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_pr_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_cli_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_periodic_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_pr_sub", null),
          lookup(var.subscriptions_output, "cas_post_enrichment_actions_cli_sub", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_pr_sub_2", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_periodic_sub_2", null),
          lookup(var.subscriptions_output, "cas_pre_enrichment_scanner_reports_cli_sub_2", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cas_enriched_reports", null), "roles/storage.objectAdmin"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"],
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "persistence-manager-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          # CAS persistence-queues
          lookup(var.topics_output, "cas_persistence_periodic", ""),
          lookup(var.topics_output, "cas_persistence_pr", ""),
          lookup(var.topics_output, "cas_persistence_cli", ""),
          lookup(var.topics_output, "cas_post_persistence_pr", ""),
          lookup(var.topics_output, "cas_post_persistence_cli", ""),
          lookup(var.topics_output, "cas_post_persistence_periodic", ""),
          lookup(var.topics_output, "cas_persistence_dlq", ""),
          lookup(var.topics_output, "cas_entities_deletion", ""),
          lookup(var.topics_output, "cas_persistence_data_ready_periodic", ""),
          lookup(var.topics_output, "cas_persistence_data_ready_pr", ""),
          lookup(var.topics_output, "cas_persistence_data_ready_cli", ""),
          # Cortex queues
          lookup(var.topics_output, "dp_uai_asset_observations_topic", ""),
          lookup(var.topics_output, "ap_issue_upsert", ""),
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "deletion_api", null),
        ]
        topic_additional_roles = [
          # CAS
          [lookup(var.topics_output, "cas_post_persistence_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_post_persistence_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_post_persistence_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_post_persistence_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_entities_deletion", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_entities_deletion", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_entities_deletion", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_data_ready_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_data_ready_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_data_ready_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_data_ready_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_data_ready_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_data_ready_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_data_ready_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_persistence_data_ready_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_data_ready_cli", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_periodic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_periodic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_pr", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_pr", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_persistence_cli", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_persistence_cli", ""), "roles/pubsub.subscriber"],
          # Cortex
          [lookup(var.topics_output, "ap_issue_ingestion_errors", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_ingestion_errors", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "ap_issue_ingest_feedback", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "ap_issue_ingest_feedback", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "dp_finding_ingestion_errors", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_finding_ingestion_errors", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "deletion_api", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          # CAS
          lookup(var.subscriptions_output, "cas_persistence_periodic_metedata", ""),
          lookup(var.subscriptions_output, "cas_persistence_periodic_large_reports", ""),
          lookup(var.subscriptions_output, "cas_persistence_periodic_small_reports", ""),
          lookup(var.subscriptions_output, "cas_persistence_pr_metadata", ""),
          lookup(var.subscriptions_output, "cas_persistence_pr_large_reports", ""),
          lookup(var.subscriptions_output, "cas_persistence_pr_small_reports", ""),
          lookup(var.subscriptions_output, "cas_persistence_cli_metadata", ""),
          lookup(var.subscriptions_output, "cas_persistence_cli_large_reports", ""),
          lookup(var.subscriptions_output, "cas_persistence_cli_small_reports", ""),
          lookup(var.subscriptions_output, "cas_entities_deletion_sub", ""),
          lookup(var.subscriptions_output, "cas_persistence_large_data_ready_periodic", ""),
          lookup(var.subscriptions_output, "cas_persistence_small_data_ready_periodic", ""),
          lookup(var.subscriptions_output, "cas_persistence_large_data_ready_pr", ""),
          lookup(var.subscriptions_output, "cas_persistence_small_data_ready_pr", ""),
          lookup(var.subscriptions_output, "cas_persistence_large_data_ready_cli", ""),
          lookup(var.subscriptions_output, "cas_persistence_small_data_ready_cli", ""),
          # Cortex
          lookup(var.subscriptions_output, "ap_issue_ingestion_errors_new_persistence_cas_sub", null),
          lookup(var.subscriptions_output, "ap_issue_ingest_feedback_sub_new_persistence_cas", null),
          lookup(var.subscriptions_output, "dp_finding_ingestion_errors_new_persistence_cas_sub", null),
          lookup(var.subscriptions_output, "dp_uai_asset_ingestion_errors_new_persistence_cas_sub", null),
          lookup(var.subscriptions_output, "deletion_api_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "dp_finding_emits", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "dp_scan_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cas_persistence", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cas_enriched_reports", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "cas_pubsub_failures", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"]
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },

    {
      service_account_name = "code-to-cloud-service-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]

        buckets = [
          [lookup(var.buckets_output, "code_to_cloud", null), "roles/storage.objectAdmin"]
        ]

        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "application-flow-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_uai_asset_observations_topic", null),
          lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_observations_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_uai_asset_ingestion_errors_topic", ""), "roles/pubsub.subscriber"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "application_upsert_cases_sub", []),
          lookup(var.subscriptions_output, "application_upsert_risk_score_sub", []),
          lookup(var.subscriptions_output, "application_upsert_counters_sub", []),
          lookup(var.subscriptions_output, "application_upsert_delete_application_from_assets_sub", []),
          lookup(var.subscriptions_output, "dp_uai_asset_ingestion_errors_cas_sub", null),
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
        ]

        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "application", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "supply_chain_tools", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "scans_management", null), "roles/bigquery.dataOwner"],
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "scanner-remediation-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"]
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "scans-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"]
        ]
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "scans-management-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "scans_management", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataViewer"]
        ],
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "cas_enriched_reports", null), "roles/storage.objectUser"]
        ],
        topics = [
          lookup(var.topics_output, "cas_scan_events", null),
          lookup(var.topics_output, "cas_scan_events_dl", null)
        ],
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_scan_events", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_scan_events", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_scan_events_dl", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_scan_events_dl", ""), "roles/pubsub.viewer"]
        ],
        subscriptions = [
          lookup(var.subscriptions_output, "cas_pr_scan_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_sub", null),
          lookup(var.subscriptions_output, "cas_cli_scan_sub", null)
        ],
        bq_job_user = true,
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "public-apis-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
      },
      automount_service_account_token = true
    },
    {
      service_account_name = "supply-chain-tools-cas"
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "supply_chain_tools", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
        ]
        bq_job_user = true,
      }
    },
    {
      service_account_name = "dashboards-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "cas_policies", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "scans_management", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
        ]
        bq_job_user = true,
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "db-migrations-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas_policies", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "scans_management", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "application", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "supply_chain_tools", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq_job_user = true,
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "policies-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas_policies", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"]
        ]
        bq_job_user = true,
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "billing-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        # multi_tenant_service = local.metro_v2
        # metro_st_namespace   = local.metro_namespace
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"],
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq_job_user = true
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"]
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "product-analytics-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "code-to-cloud-workflow-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas_detection_rules", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "cas", null), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "external_data", null), "roles/bigquery.dataViewer"],
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq_job_user = true
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "dp_finding_emits", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "dp_finding_emits", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.publisher"],
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "secret-verifier-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        bq_job_user = true # Allow the service account to run BigQuery jobs
        bq = [
          [lookup(var.bq_output, "public_access_views", null), "roles/bigquery.dataOwner"]
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cas_periodic_scan", null),
          lookup(var.topics_output, "cas_pr_scan", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pr_scan", ""), "roles/pubsub.subscriber"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_scan_secrets_s_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_githistory_s_sub", ""),
          lookup(var.subscriptions_output, "cas_periodic_scan_githistory_m_sub", ""),
          lookup(var.subscriptions_output, "cas_periodic_scan_githistory_l_sub", ""),
          lookup(var.subscriptions_output, "cas_pr_scan_secrets_s_sub", ""),
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "scanner-orchestration-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "sbom_reports", null), "roles/storage.objectUser"]
        ]
        topics = [
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", null),
          lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", null),
          lookup(var.topics_output, "cas_trigger_argo_wf", null),
          lookup(var.topics_output, "bq_stats_topic", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_pr", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_cli", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_trigger_argo_wf", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_trigger_argo_wf_sarif_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"]
        ]
        bq_job_user = true # Allow the service account to run BigQuery jobs
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "third-party-parser-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topics = [
          lookup(var.topics_output, "cas_trigger_argo_wf", null),
          lookup(var.topics_output, "cas_jenkins_trigger_argo_wf", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_trigger_argo_wf", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_trigger_argo_wf", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_jenkins_trigger_argo_wf", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_jenkins_trigger_argo_wf", ""), "roles/pubsub.subscriber"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_trigger_argo_wf_sarif_sub", null),
          lookup(var.subscriptions_output, "cas_jenkins_state_trigger_argo_wf_plugin_sub", null),
          lookup(var.subscriptions_output, "cas_jenkins_logs_trigger_argo_wf_plugin_sub", null),
          lookup(var.subscriptions_output, "cas_jenkins_pipelines_trigger_argo_wf_plugin_sub", null),
        ]
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "pre-scan-pr-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topic_additional_roles = [
        ]
         subscriptions = [
           lookup(var.subscriptions_output, "cas_pr_lobby_to_pre_scan_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"]
        ]
        bq_job_user = true
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "pre-scan-periodic-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "outpost_communication", null), "AllowBucketsReadAccess"]
        ]
        topic_additional_roles = [
        ]
         subscriptions = [
           lookup(var.subscriptions_output, "cas_periodic_lobby_to_pre_scan_sub", null),
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"]
        ]
        bq_job_user = true
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "post-scan-pr-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_post_scan_results_topic", ""), "roles/pubsub.publisher"],
        ]
         subscriptions = [
          lookup(var.subscriptions_output, "cas_pr_tasks_manager_to_post_scan_sub", null),
        ]
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "post-scan-periodic-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_post_scan_results_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_post_scan_results_dlq_topic", ""), "roles/pubsub.viewer"],
        ]
         subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_tasks_manager_to_post_scan_sub", null),
        ]
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "lobby-pr-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_lobby_results_topic", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pre_enrichment_scanner_reports_periodic", ""), "roles/pubsub.publisher"],
        ]
         subscriptions = [
          lookup(var.subscriptions_output, "cas_pr_data_collector_to_lobby_sub", null),
          lookup(var.subscriptions_output, "cas_pr_post_scan_to_lobby_sub", null),
          lookup(var.subscriptions_output, "cas_pr_post_scan_to_lobby_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_pr_lobby_to_pre_scan_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_pr_tasks_manager_to_post_scan_dlq_sub", null),
        ]
      },
     automount_service_account_token = true
    },
    {
      service_account_name = "lobby-periodic-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"]
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_lobby_results_topic", ""), "roles/pubsub.publisher"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_data_collector_to_lobby_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_post_scan_to_lobby_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_post_scan_to_lobby_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_lobby_to_pre_scan_dlq_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_tasks_manager_to_post_scan_dlq_sub", null),
        ]
      },
      automount_service_account_token = true
    },
    {
      service_account_name = "iac-scanner-cas"
      mt_dedicated_group   = true
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "supply_chain_tools", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.user"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.user"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cas_periodic_scan", null),
          lookup(var.topics_output, "cas_pr_scan", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pr_scan", ""), "roles/pubsub.subscriber"],
        ]
         subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_scan_iac_s_sub", null),
          lookup(var.subscriptions_output, "cas_pr_scan_iac_s_sub", null),
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "cicd-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "ci_build_logs", null), "roles/storage.objectAdmin"]
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "application", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "supply_chain_tools", null), "roles/bigquery.dataOwner"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", null),
          lookup(var.topics_output, "cas_webhooks", null),
          lookup(var.topics_output, "cas_periodic_scan", null),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.subscriber"],
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_webhooks_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_enry_cicd_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_pipex_cicd_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_ci_match_cicd_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scan_deppy_cicd_sub", null),
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true,
    },
    {
      service_account_name = "unified-cli-api-cas"
      enabled              = local.enable_cortex_platform
      mt_dedicated_group   = true
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        additional_sa_role_iam_members = ["roles/iam.ServiceAccountTokenCreator"]
        additional_sa_roles = [
          "projects/${var.project_id}/roles/GCSSignedUrlBlobGenerator",
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "scanner-tasks-manager-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        topics = [
          lookup(var.topics_output, "cas_periodic_scan", null),
          lookup(var.topics_output, "cas_pr_scan", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_pr_scan", ""), "roles/pubsub.publisher"],
          [lookup(var.topics_output, "cas_periodic_scanner_tasks_deadletter", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_pr_scanner_tasks_deadletter", ""), "roles/pubsub.subscriber"],
          [lookup(var.topics_output, "cas_tasks_manager_results_topic", ""), "roles/pubsub.publisher"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_scanner_tasks_deadletter_sub", null),
          lookup(var.subscriptions_output, "cas_periodic_scanner_tasks_deadletter_sub", null),
          lookup(var.subscriptions_output, "cas_pr_scanner_tasks_deadletter_sub", null),
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "client-metrics-cas"
      enabled              = local.enable_cortex_platform
      namespace            = "cas"
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "deppy-scanner-cas"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectUser"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "argo_worklows_sensitive_customer_data", null), "roles/storage.objectUser"],
        ]
        topics = [
          lookup(var.topics_output, "cas_periodic_scan", null)
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "cas_periodic_scan", ""), "roles/pubsub.subscriber"]
        ]
        subscriptions = [
          lookup(var.subscriptions_output, "cas_periodic_scan_deppy_cicd_sub", null)
        ]
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "aspm-issue-urgency"
      enabled              = local.enable_cortex_platform
      workload_identity = {
        additional_project_roles = [
          "roles/cloudtrace.agent"
        ]
        buckets = [
          [lookup(var.buckets_output, "argo_worklows_artifacts", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "argo_worklows_logs", null), "roles/storage.objectAdmin"],
          [lookup(var.buckets_output, "code_to_cloud", null), "ReadAndWrite"],
        ]
        bq = [
          [lookup(var.bq_output, "public_platform", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "asset_inventory", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "application", null), "roles/bigquery.dataOwner"],
          [lookup(var.bq_output, "ds", null), "roles/bigquery.dataOwner"],
        ]
        topics = [
          lookup(var.topics_output, "bq_stats_topic", ""),
        ]
        topic_additional_roles = [
          [lookup(var.topics_output, "bq_stats_topic", ""), "roles/pubsub.viewer"],
        ]
        bq_job_user = true,
      }
      automount_service_account_token = true
    },
    {
      service_account_name = "sca-artifactory-management-cas"
      enabled              = local.enable_cortex_platform
      namespace            = "cas"
      automount_service_account_token = true
    },
  ]
}

module "cas_workload_identity" {
  source = "../../modules/workload_identity"

  for_each = {
    for sa in local.cas_service_accounts :
    sa.service_account_name => sa
    if lookup(sa, "enabled", true) && !lookup(sa, "skip_workload_identity", false)
  }

  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }

  service_account_name            = each.value.service_account_name
  mt_dedicated_group              = lookup(each.value, "mt_dedicated_group", false)
  project_id                      = var.project_id
  metro_host_project_id           = var.metro_host_project_id
  wi_project_id                   = local.workload_identity_project_id
  namespace                       = lookup(each.value, "namespace", local.cas_namespace)
  viso_env                        = var.viso_env
  create_kubernetes_sa            = false
  automount_service_account_token = lookup(each.value, "automount_service_account_token", false)
  data                            = lookup(each.value, "workload_identity", {})
  extra_namespaces                = var.is_metro_tenant || var.metro_all_in_one  ? [local.metro_namespace, local.st_namespace] : [local.st_namespace]
  dedicated_group_name  = lookup(each.value, "dedicated_group_name", "")
}
