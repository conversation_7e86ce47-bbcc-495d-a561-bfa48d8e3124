# Used to copy artifacts from MT Google Artifact Registry to an ST bucket,
# for distribution to Outpost buckets (part of cwp-agentless)

locals {
    # The registry_scanner_version should be same as gcp-configuration/tf/tenant/03_kubernetes_new_infra/03_upload_binary.tf
  registry_scanner_version = var.viso_env != "dev" ? "2.3.41" : "2.3.41"  # This version will also be reflected as an env var in cwp's configmap

  registries_projects_ids = {
    dev     = "xdr-registry-dev-01"
    prod-fr = "xdr-registry-prod-fr-01"
    prod-gv = "xdr-registry-prod-gv-01"
  }
  # fallback to prod in order to not declare each viso region
  gar_project_id =  lookup(local.registries_projects_ids, var.viso_env, "xdr-registry-prod-us-01")

}

module "upload_binary" {
  for_each = toset(local.enable_cortex_platform && !local.is_agentix && !var.pool_tenant_creation ? ["enabled"] : [])

  source = "../../modules/upload_binary"

  # TODO: should not use a hardcoded value here: https://panw-rnd.slack.com/archives/C07HG8JPQSG/p1728824108340289?thread_ts=1728810805.127229&cid=C07HG8JPQSG
  gar_project_id    = local.gar_project_id
  gar_name          = "agentless-apt-private"
  gar_region        = "us-central1"
  gcs_access_token  = data.google_client_config.default.access_token
  gcs_bucket_name   = "${var.project_id}-cwp-sp-bc-data"
  gcs_bucket_prefix = "payloads/"

  packages_map = {
    "sp-scan-runner" =  "0.4.7"
    "ads-scanner"    =  "5.0.8"
    "registryscanner" = local.registry_scanner_version
  }
}

locals {
  dspm_image_sia              = lookup(var.app_images,"dspm_sia","us-docker.pkg.dev/xdr-registry-dev-01/dig/sia-standalone:main-v0.1-295-g328d954")
  dspm_image_dbear            = lookup(var.app_images,"dspm_dbear","us-docker.pkg.dev/xdr-registry-dev-01/dig/dbear:main-v0.1-333-g733d03d")
  dspm_image_xporter          = lookup(var.app_images,"dspm_xporter","us-docker.pkg.dev/xdr-registry-dev-01/dig/xporter:dev-v0.1-9-g02425c8")
  dspm_image_scar             = lookup(var.app_images,"dspm_scar","us-docker.pkg.dev/xdr-registry-dev-01/dig/scar:master-v0.1-25-g25d94cc")
}

module "upload_sia_binary" {
  for_each = toset(local.enable_cortex_platform && !local.is_agentix && !var.pool_tenant_creation ? ["enabled"] : [])

  source = "../../modules/upload_binary_generic"

  gar_project_id    =  split("/",local.dspm_image_sia)[1]
  gar_name          = "dig-generic"
  gar_region        = "us"
  package_version   = split(":",local.dspm_image_sia)[1]
  gcs_access_token  = data.google_client_config.default.access_token
  gcs_bucket_name   = "${var.project_id}-cwp-sp-bc-data"
  gcs_bucket_prefix = "payloads/"
  package = "sia-standalone"
  files = [{
    name = "image.tar.gz"
    rename = "dspm-sia.tar.gz"
  }]

}

module "upload_scar_binary" {
  for_each = toset(local.enable_cortex_platform && !local.is_agentix && !var.pool_tenant_creation ? ["enabled"] : [])

  source = "../../modules/upload_binary_generic"

  gar_project_id    =  split("/",local.dspm_image_scar)[1]
  gar_name          = "dig-generic"
  gar_region        = "us"
  package_version   = split(":",local.dspm_image_scar)[1]
  gcs_access_token  = data.google_client_config.default.access_token
  gcs_bucket_name   = "${var.project_id}-cwp-sp-bc-data"
  gcs_bucket_prefix = "payloads/"
  package = "scar"
  files = [{
    name = "image.tar.gz"
    rename = "dspm-scar.tar.gz"
  }]

}


module "upload_dbear_binary" {
  for_each = toset(local.enable_cortex_platform && !local.is_agentix && !var.pool_tenant_creation ? ["enabled"] : [])
  source = "../../modules/upload_binary_generic"
  gar_project_id    =  split("/",local.dspm_image_dbear)[1]
  gar_name          = "dig-generic"
  gar_region        = "us"
  package_version   = split(":",local.dspm_image_dbear)[1]
  gcs_access_token  = data.google_client_config.default.access_token
  gcs_bucket_name   = "${var.project_id}-cwp-sp-bc-data"
  gcs_bucket_prefix = "payloads/"
  package = "dbear"
  files = [{
    name = "image.tar.gz"
    rename = "dspm-dbear.tar.gz"
}]
}

module "upload_xporter_binary" {
  for_each = toset(local.enable_cortex_platform && !local.is_agentix && !var.pool_tenant_creation ? ["enabled"] : [])
  source = "../../modules/upload_binary_generic"
  gar_project_id    =  split("/",local.dspm_image_xporter)[1]
  gar_name          = "dig-generic"
  gar_region        = "us"
  package_version   = split(":",local.dspm_image_xporter)[1]
  gcs_access_token  = data.google_client_config.default.access_token
  gcs_bucket_name   = "${var.project_id}-cwp-sp-bc-data"
  gcs_bucket_prefix = "payloads/"
  package = "xporter"
  files = [{
    name = "image.tar.gz"
    rename = "dspm-xporter.tar.gz"
  }]
}
