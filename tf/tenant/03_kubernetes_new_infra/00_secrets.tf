module "create_secret" {
  source     = "../../modules/secret"
  secrets    = local.secrets
  depends_on = [module.create_namespace]
}

#certificate for engine0
resource "tls_private_key" "engine_cert" {
  algorithm = "RSA"
}

data "kubernetes_secret" "mt-secrets" {
  count = local.metro_v2 ? 1 : 0
  metadata {
    name      = "mt-secrets"
    namespace = local.metro_namespace
  }
}

locals {
  secrets  = concat(local.general_secrets, local.dspm_secrets)
  metro_v2 = var.is_metro_tenant && can(regex("^mt[0-9]+$", var.metro_host_id)) && !var.metro_all_in_one

  secrets_refs = {
    "ApnKey.p8"                                       = data.google_secret_manager_secret_version.apple_push_key.secret_data
    "dss_server.crt"                                  = data.google_secret_manager_secret_version.dss_server_crt.secret_data
    "dss.key"                                         = data.google_secret_manager_secret_version.dss_key.secret_data
    "dss.pem"                                         = data.google_secret_manager_secret_version.dss_crt.secret_data
    "egress.crt"                                      = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
    "script_public_prod.key"                          = data.google_secret_manager_secret_version.scripts_public_key_prod.secret_data
    "script_public.key"                               = data.google_secret_manager_secret_version.scripts_public_key.secret_data
    "wf-verdict-service.crt.pem"                      = data.google_secret_manager_secret_version.wf_verdict_service_crt.secret_data
    "wf-verdict-service.key.pem"                      = data.google_secret_manager_secret_version.wf_verdict_service_key.secret_data
    agent_api_redis_password                          = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["agent_api_redis_password"] : element(concat(random_password.agent_api_redis_password.*.result, tolist([""])), 0)
    agentix_hub_mysql_password                        = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["agentix_hub_mysql_password"] : random_password.agentix_hub_mysql_password.result
    agentix_hub_mysql_username                        = "agentix"
    analytics_redis_password                          = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["analytics_redis_password"] : element(concat(random_password.analytics_redis_password.*.result, tolist([""])), 0)
    assets_redis_password                             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["assets_redis_password"] : element(concat(random_password.assets_redis_password.*.result, tolist([""])), 0)
    ca_collection_redis_password                      = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["ca_collection_redis_password"] : element(concat(random_password.ca_collection_redis_password.*.result, tolist([""])), 0)
    ca_collection_dragonfly_password                  = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["ca_collection_dragonfly_password"] : element(concat(random_password.ca_collection_dragonfly_password.*.result, tolist([""])), 0)
    cas_redis_password                                = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["cas_redis_password"] : random_password.cas_redis_password.result
    cns_neo4j_password                                = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["cns_neo4j_password"] : random_password.cns_neo4j_password.result
    cns_neo4j_user_password                           = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["cns_neo4j_user_password"] : "neo4j/${random_password.cns_neo4j_password.result}"
    cts_redis_password                                = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["cts_redis_password"] : element(concat(random_password.cts_redis_password.*.result, tolist([""])), 0)
    cwp_redis_password                                = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["cwp_redis_password"] : element(concat(random_password.cwp_redis_password.*.result, tolist([""])), 0)
    dacs_postgres_password                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["dacs_postgres_password"] : element(concat(random_password.data_classification_settings_postgres_password.*.result, tolist([""])), 0)
    data_classification_settings_postgres_password    = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["data_classification_settings_postgres_password"] : random_password.data_classification_settings_postgres_password.result
    dspm_redis_password                               = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["dspm_redis_password"] : element(concat(random_password.dspm_redis_password.*.result, tolist([""])), 0)
    dss_redis_password                                = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["dss_redis_password"] : element(concat(random_password.dss_redis_password.*.result, tolist([""])), 0)
    engine_api_key                                    = element(concat(random_password.engine_api_key.*.result, tolist([""])), 0)
    fetcher_redis_password                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["fetcher_redis_password"] : element(concat(random_password.fetcher_redis_password.*.result, tolist([""])), 0)
    gonzo_redis_password                              = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["gonzo_redis_password"] : element(concat(random_password.gonzo_redis_password.*.result, tolist([""])), 0)
    http_tunnel_over_ws_password                      = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["http_tunnel_over_ws_password"] : random_password.http_tunnel_over_ws_password.result
    mongodb_keyfile                                   = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["mongodb_keyfile"] : random_bytes.mongodb_keyfile.base64
    mongodb_password_cwp                              = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["mongodb_password_cwp"] : element(concat(random_password.mongodb_password.*.result, tolist([""])), 0)
    mongodb_uri_cwp                                   = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["mongodb_uri_cwp"] : element(concat(["mongodb://root:${urlencode(random_password.mongodb_password.*.result[0])}@xdr-st-${var.lcaas}-cwp-mongodb.${local.st_namespace}.svc.cluster.local"], tolist([""])), 0)
    mysql_password                                    = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["mysql_password"] : random_password.mysql_password.result
    proxysql_password                                 = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["proxysql_password"] : random_password.proxysql_password.result
    mysql_uvem_password                               = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["mysql_uvem_password"] : random_password.mysql_uvem_password.result
    neo4j_password                                    = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["neo4j_password"] : random_password.neo4j_password.result
    postgres_password                                 = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["postgres_password"] : element(concat(random_password.postgres_password.*.result, tolist([""])), 0)
    publicapi_encryption_256key                       = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["publicapi_encryption_256key"] : random_id.publicapi_encryption_256key.hex
    publicapi_encryption_key                          = data.google_secret_manager_secret_version.publicapi_encryption_key.secret_data
    redis_password                                    = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["redis_password"] : element(concat(random_password.redis_password.*.result, tolist([""])), 0)
    scylla_admin_password                             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_admin_password"] : element(concat(random_password.scylla_admin_password.*.result, tolist([""])), 0)
    scylla_enrichment_admin_password                  = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_enrichment_admin_password"] : element(concat(random_password.scylla_enrichment_admin_password.*.result, tolist([""])), 0)
    scylla_enrichment_password                        = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_enrichment_password"] : element(concat(random_password.scylla_enrichment_password.*.result, tolist([""])), 0)
    scylla_password                                   = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_password"] : element(concat(random_password.scylla_password.*.result, tolist([""])), 0)
    scylla_xcloud_admin_password                      = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_xcloud_admin_password"] : element(concat(random_password.scylla_xcloud_admin_password.*.result, tolist([""])), 0)
    scylla_xcloud_password                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["scylla_xcloud_password"] : element(concat(random_password.scylla_xcloud_password.*.result, tolist([""])), 0)
    secret_identifier                                 = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["secret_identifier"] : random_password.application_hub_id.result
    signed_pickle_agent_api                           = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_agent_api"] : random_password.signed_pickle_agent_api.result
    signed_pickle_analytics_alerts_emitter            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_analytics_alerts_emitter"] : random_password.signed_pickle_analytics_alerts_emitter.result
    signed_pickle_analytics_decider                   = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_analytics_decider"] : random_password.signed_pickle_analytics_decider.result
    signed_pickle_analytics_detection_engine          = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_analytics_detection_engine"] : random_password.signed_pickle_analytics_detection_engine.result
    signed_pickle_analytics_profiles_orchestrator     = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_analytics_profiles_orchestrator"] : random_password.signed_pickle_analytics_profiles_orchestrator.result
    signed_pickle_dp_analytics_graph_detection_engine = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_dp_analytics_graph_detection_engine"] : random_password.signed_pickle_dp_analytics_graph_detection_engine.result
    signed_pickle_api                                 = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_api"] : random_password.signed_pickle_api.result
    signed_pickle_fetcher                             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_fetcher"] : random_password.signed_pickle_fetcher.result
    signed_pickle_frontend                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_frontend"] : random_password.signed_pickle_frontend.result
    signed_pickle_log_processor                       = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_log_processor"] : random_password.signed_pickle_log_processor.result
    signed_pickle_metrics                             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_metrics"] : random_password.signed_pickle_metrics.result
    signed_pickle_notifier                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_notifier"] : random_password.signed_pickle_notifier.result
    signed_pickle_overseer                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_overseer"] : random_password.signed_pickle_overseer.result
    signed_pickle_pz_schema_manager                   = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_pz_schema_manager"] : random_password.signed_pickle_pz_schema_manager.result
    signed_pickle_secdo_init                          = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_secdo_init"] : random_password.signed_pickle_secdo_init.result
    signed_pickle_task_processor                      = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_task_processor"] : random_password.signed_pickle_task_processor.result
    signed_pickle_xql_engine                          = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["signed_pickle_xql_engine"] : random_password.signed_pickle_xql_engine.result
    singlestore_password                              = element(concat(random_password.singlestore_password.*.result, tolist([""])), 0)
    task_scheduling_connection_password               = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["task_scheduling_connection_password"] : random_password.task_scheduling_connection_password.result
    temporal_postgres_password                        = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["temporal_postgres_password"] : random_password.temporal_postgres_password.result
    temporal_visibility_postgres_password             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["temporal_visibility_postgres_password"] : random_password.temporal_visibility_postgres_password.result
    xcloud_redis_password                             = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["xcloud_redis_password"] : element(concat(random_password.xcloud_redis_password.*.result, tolist([""])), 0)
    xsoar_handler_key                                 = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["xsoar_handler_key"] : random_bytes.xsoar_handler_key.base64
    xsoar_mysql_username                              = "xsoar"
    xsoar_mysql_password                              = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["xsoar_mysql_password"] : element(concat(random_password.xsoar_mysql_password.*.result, tolist([""])), 0)
    xsoar_redis_password                              = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["xsoar_redis_password"] : var.is_xsiam ? element(concat(random_password.xsoar_redis_password.*.result, tolist([""])), 0) : null
    elastic_password                                  = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["elastic_password"] : random_password.elastic_password.result
    argo_postgres_password                            = local.metro_v2 ? data.kubernetes_secret.mt-secrets[0].data["argo_postgres_password"] : random_password.argo_postgres_password.result
    argo_postgres_username                            = "argo"
    analytics-conf-salt-key                           = data.google_secret_manager_secret_version.analytics_conf_salt_key.secret_data
    xdr_auth_token                                    = var.xdr_auth_token
  }

  general_secrets = [
    {
      enabled   = true
      name      = "es-elastic-user"
      type      = "kubernetes.io/basic-auth"
      namespace = var.metro_all_in_one ? local.metro_namespace : local.st_namespace

      data = {
        roles    = "superuser"
        username = "elastic"
        password = local.secrets_refs["elastic_password"]
      }
    },
    {
      name      = "${var.lcaas}-secrets"
      namespace = local.st_namespace
      enabled   = true
      data = {
        elastic_password                    = local.secrets_refs["elastic_password"]
        neo4j_password                      = local.secrets_refs["neo4j_password"]
        mysql_password                      = local.secrets_refs["mysql_password"]
        proxysql_password                   = local.secrets_refs["proxysql_password"]
        mysql_uvem_password                 = local.secrets_refs["mysql_uvem_password"]
        xsoar_mysql_password                = local.secrets_refs["xsoar_mysql_password"]
        xsoar_mysql_username                = local.secrets_refs["xsoar_mysql_username"]
        report_encryption_key               = var.report_encryption_key
        tmsconf_mysql_password              = ""
        redis_password                      = local.secrets_refs["redis_password"]
        agent_api_redis_password            = local.secrets_refs["agent_api_redis_password"]
        fetcher_redis_password              = local.secrets_refs["fetcher_redis_password"]
        analytics_redis_password            = local.secrets_refs["analytics_redis_password"]
        gonzo_redis_password                = local.secrets_refs["gonzo_redis_password"]
        dss_redis_password                  = local.secrets_refs["dss_redis_password"]
        cwp_redis_password                  = local.secrets_refs["cwp_redis_password"]
        dspm_redis_password                 = local.secrets_refs["dspm_redis_password"]
        ca_collection_redis_password        = local.secrets_refs["ca_collection_redis_password"]
        ca_collection_dragonfly_password    = local.secrets_refs["ca_collection_dragonfly_password"]
        mongodb_password_cwp                = local.secrets_refs["mongodb_password_cwp"]
        mongodb_uri_cwp                     = local.secrets_refs["mongodb_uri_cwp"]
        mongodb_keyfile                     = local.secrets_refs["mongodb_keyfile"]
        postgres_password                   = local.secrets_refs["postgres_password"]
        platform_redis_password             = element(concat(random_password.platform_redis_password.*.result, tolist([""])), 0)
        publicapi_encryption_256key         = local.secrets_refs["publicapi_encryption_256key"]
        publicapi_encryption_key            = data.google_secret_manager_secret_version.publicapi_encryption_key.secret_data
        slackconf_hydra_redirect            = var.slackconf_hydra_redirect
        engine_api_key                      = local.secrets_refs["engine_api_key"]
        scylla_admin_password               = local.secrets_refs["scylla_admin_password"]
        scylla_password                     = local.secrets_refs["scylla_password"]
        scylla_xcloud_admin_password        = local.secrets_refs["scylla_xcloud_admin_password"]
        scylla_xcloud_password              = local.secrets_refs["scylla_xcloud_password"]
        scylla_enrichment_admin_password    = local.secrets_refs["scylla_enrichment_admin_password"]
        scylla_enrichment_password          = local.secrets_refs["scylla_enrichment_password"]
        singlestore_password                = local.secrets_refs["singlestore_password"]
        xcloud_redis_password               = local.secrets_refs["xcloud_redis_password"]
        assets_redis_password               = local.secrets_refs["assets_redis_password"]
        ca_collection_redis_password        = local.secrets_refs["ca_collection_redis_password"]
        ca_collection_dragonfly_password    = local.secrets_refs["ca_collection_dragonfly_password"]
        sendgrid_api_key                    = data.google_secret_manager_secret_version.sendgrid_api_key.secret_data
        mx_router_data_key                  = var.is_fedramp ? "" : data.google_secret_manager_secret_version.mailexchangerouter_sensitive_data_key_2[0].secret_data
        xsoar_redis_password                = local.secrets_refs["xsoar_redis_password"]
        http_tunnel_over_ws_password        = local.secrets_refs["http_tunnel_over_ws_password"]
        THIRDPARTYSAAS_COLLECTION_ENC_KEY   = var.collection_enc_key
        pandbconf_api_key                   = var.is_fedramp ? "" : data.google_secret_manager_secret_version.pandbconf_api_key_secret[0].secret_data
        task_scheduling_connection_password = local.secrets_refs["task_scheduling_connection_password"]
        ctsconf_sa_id                       = local.enable_cortex_platform && local.metro_v2 ? module.platform_workload_identity["cts"].google_service_unique_id : ""
        agentix_hub_mysql_username          = local.secrets_refs["agentix_hub_mysql_username"]
        agentix_hub_mysql_password          = local.secrets_refs["agentix_hub_mysql_password"]
      # TODO: Remove for master-3.17
      UvemMigrationS2Link_id           = local.enable_cortex_platform ? data.google_secret_manager_secret_version.singlestore_sa_access_id_secret[0].secret_data : null
      UvemMigrationS2Link_token        = local.enable_cortex_platform ? data.google_secret_manager_secret_version.singlestore_sa_access_token_secret[0].secret_data : null
      }
    },
    {
      name      = "${var.lcaas}-cloudsec-secrets"
      namespace = local.cloudsec_namespace
      enabled   = local.enable_cortex_platform
      data = {
        # root password
        CLOUDSEC_COMMON_POSTGRES_PASSWORD = local.secrets_refs["postgres_password"]
        # non-root password
        CLOUDSEC_RO_POSTGRES_PASSWORD  = local.secrets_refs["postgres_password"]
        CLOUDSEC_RW_POSTGRES_PASSWORD  = local.secrets_refs["postgres_password"]
        CLOUDSEC_COMMON_MYSQL_PASSWORD = local.secrets_refs["mysql_password"]
        CLOUDSEC_COMMON_PROXYSQL_PASSWORD = local.secrets_refs["proxysql_password"]
        "egress.crt"                   = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
        XDR_REDIS_PASSWORD             = local.secrets_refs["redis_password"]
      }
    },
    {
      name      = "api-only"
      enabled   = true
      namespace = local.st_namespace
      data = {
        inproducthelpconf_help_api_token = data.google_secret_manager_secret_version.inproducthelpconf_help_api_token.secret_data
      }
    },
    {
      name      = "api-certs"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "egress.crt"             = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
        "dss.pem"                = data.google_secret_manager_secret_version.dss_crt.secret_data
        "dss.key"                = data.google_secret_manager_secret_version.dss_key.secret_data
        "dss_server.crt"         = data.google_secret_manager_secret_version.dss_server_crt.secret_data
        "script_public.key"      = data.google_secret_manager_secret_version.scripts_public_key.secret_data
        "script_public_prod.key" = data.google_secret_manager_secret_version.scripts_public_key_prod.secret_data
        "ApnKey.p8"              = data.google_secret_manager_secret_version.apple_push_key.secret_data
      }
    },
    {
      name      = "aws-connector-secrets"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "aws_connector_key"               = var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform ? chomp(data.google_secret_manager_secret_version.aws_connector_key.secret_data) : ""
        "aws_connector_secret"            = var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform ? chomp(data.google_secret_manager_secret_version.aws_connector_secret.secret_data) : ""
        "aws_commercial_connector_key"    = var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform ? chomp(data.google_secret_manager_secret_version.aws_commercial_connector_key.secret_data) : ""
        "aws_commercial_connector_secret" = var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform ? chomp(data.google_secret_manager_secret_version.aws_commercial_connector_secret.secret_data) : ""
        "aws_fedramp_connector_key"       = (var.is_fedramp && (var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform)) ? chomp(data.google_secret_manager_secret_version.aws_fedramp_connector_key.secret_data) : ""
        "aws_fedramp_connector_secret"    = (var.is_fedramp && (var.tb_licenses > 0 || var.is_xpanse || local.enable_cortex_platform)) ? chomp(data.google_secret_manager_secret_version.aws_fedramp_connector_secret.secret_data) : ""
      }
    },
    {
      name      = "pandb-api-key"
      namespace = local.st_namespace
      enabled   = !var.is_fedramp
      data = {
        pandb_api_key = var.is_fedramp ? null : data.google_secret_manager_secret_version.pandb_api_key_2[0].secret_data
      }
    },
    {
      name      = "wf-certs"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "wf-verdict-service.crt.pem" = data.google_secret_manager_secret_version.wf_verdict_service_crt.secret_data
        "wf-verdict-service.key.pem" = data.google_secret_manager_secret_version.wf_verdict_service_key.secret_data
      }
    },
    {
      name      = "wf-certs-cwp"
      namespace = local.cwp_namespace
      enabled   = true
      data = {
        "wf-verdict-service.crt.pem" = data.google_secret_manager_secret_version.wf_verdict_service_crt.secret_data
        "wf-verdict-service.key.pem" = data.google_secret_manager_secret_version.wf_verdict_service_key.secret_data
      }
    },
    {
      name      = "firestore-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        FIRESTORE_ACCESS_KEY = var.firestore_access_service_key
      }
    },
    {
      name      = "harvester-passphrase-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        HARVESTER_PASSPHRASE = data.google_secret_manager_secret_version.harvester_shared_key.secret_data
      }
    },
    {
      name      = "whoisapi-key-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        WHOIS_API_KEY = chomp(data.google_secret_manager_secret_version.whoisapi_key.secret_data)
      }
    },
    {
      name      = "dss-certs"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "dss.pem"        = data.google_secret_manager_secret_version.dss_crt.secret_data
        "dss.key"        = data.google_secret_manager_secret_version.dss_key.secret_data
        "dss_server.crt" = data.google_secret_manager_secret_version.dss_server_crt.secret_data
      }
    },
    {
      name      = "engine-private-key"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "d2_server.key" = tls_private_key.engine_cert.private_key_pem
      }
    },
    {
      name      = "pyramid-token"
      namespace = var.metro_all_in_one ? local.metro_namespace : local.st_namespace
      enabled   = !local.metro_v2
      data = {
        "jwt_cert.pem" = data.google_secret_manager_secret_version.jwt_crt.secret_data
      }
    },
    {
      # the above secret being created in st NS in ST envs && mt NS in metro_all_in_one envs.
      # this secret is for st NS in metro_v2 envs.
      name      = "pyramid-token"
      namespace = local.st_namespace
      enabled   = local.metro_v2
      data = {
        "jwt_cert.pem" = data.google_secret_manager_secret_version.jwt_crt.secret_data
      }
    },
    {
      name      = "signed-pickle-agent-api-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_agent_api = local.secrets_refs["signed_pickle_agent_api"]
      }
    },
    {
      name      = "signed-pickle-analytics-alerts-emitter-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_analytics_alerts_emitter = local.secrets_refs["signed_pickle_analytics_alerts_emitter"]
      }
    },
    {
      name      = "signed-pickle-analytics-decider-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_analytics_decider = local.secrets_refs["signed_pickle_analytics_decider"]
      }
    },
    {
      name      = "signed-pickle-analytics-detection-engine-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_analytics_detection_engine = local.secrets_refs["signed_pickle_analytics_detection_engine"]
      }
    },
    {
      name      = "signed-pickle-analytics-profiles-orchestrator-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_analytics_profiles_orchestrator = local.secrets_refs["signed_pickle_analytics_profiles_orchestrator"]
      }
    },
    {
      name      = "signed-pickle-dp-analytics-graph-detection-engine-secret"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = true
      data      = { signed_pickle_dp_analytics_graph_detection_engine = random_password.signed_pickle_dp_analytics_graph_detection_engine.result }
    },
    {
      name      = "signed-pickle-api-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_api = local.secrets_refs["signed_pickle_api"]
      }
    },
    {
      name      = "signed-pickle-fetcher-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_fetcher = local.secrets_refs["signed_pickle_fetcher"]
      }
    },
    {
      name      = "signed-pickle-frontend-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_frontend = local.secrets_refs["signed_pickle_frontend"]
      }
    },
    {
      name      = "signed-pickle-log-processor-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_log_processor = local.secrets_refs["signed_pickle_log_processor"]
      }
    },
    {
      name      = "signed-pickle-metrics-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_metrics = local.secrets_refs["signed_pickle_metrics"]
      }
    },
    {
      name      = "signed-pickle-notifier-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_notifier = local.secrets_refs["signed_pickle_notifier"]
      }
    },
    {
      name      = "signed-pickle-overseer-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_overseer = local.secrets_refs["signed_pickle_overseer"]
      }
    },
    {
      name      = "signed-pickle-pz-schema-manager-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_pz_schema_manager = local.secrets_refs["signed_pickle_pz_schema_manager"]
      }
    },
    {
      name      = "signed-pickle-secdo-init-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_secdo_init = local.secrets_refs["signed_pickle_secdo_init"]
      }
    },
    {
      name      = "signed-pickle-task-processor-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_task_processor = local.secrets_refs["signed_pickle_task_processor"]
      }
    },
    {
      name      = "signed-pickle-xql-engine-secret"
      namespace = local.st_namespace
      enabled   = true
      data = {
        signed_pickle_xql_engine = local.secrets_refs["signed_pickle_xql_engine"]
      }
    },
    {
      name      = "analytics-conf-salt-key-secret"
      namespace = local.st_namespace
      enabled   = true
      data      = { analytics-conf-salt-key = data.google_secret_manager_secret_version.analytics_conf_salt_key.secret_data }
    },
    {
      enabled   = var.enable_asm
      name      = "xpanse-asset-feedback-credentials-secret"
      namespace = local.st_namespace
      data = {
        api-key = var.xpanse_asset_feedback_credentials_api_key
      }
    },
    {
      enabled   = var.enable_asm
      name      = "xpanse-tenant-credentials-client-id-secret"
      namespace = local.st_namespace
      data = {
        xpanse-tenant-credentials-client-id = var.xpanse_tenant_credentials_client_id
      }
    },
    {
      enabled   = var.enable_asm
      name      = "xpanse-tenant-credentials-client-secret-secret"
      namespace = local.st_namespace
      data = {
        xpanse-tenant-credentials-client-secret = var.xpanse_tenant_credentials_client_secret
      }
    },
    {
      enabled   = var.enable_xsoar_shared_components
      name      = "gcs-credentials"
      namespace = local.st_namespace
      data = {
        "gcs.client.default.credentials_file" = base64decode(lookup(var.service_account_key_output, "es-backup", ""))
      }
    },
    {
      enabled   = local.enable_xdr_agent_daemon
      name      = "xdr-agent-deployment"
      namespace = local.xdr_agent_namespace
      data = {
        "distribution-id" = data.google_secret_manager_secret_version.xdr_daemon_distributionid.secret_data
      }
    },
    {
      enabled   = local.enable_cortex_platform && (!var.is_metro_tenant || var.metro_all_in_one)
      name      = "temporal-postgress"
      namespace = local.st_namespace
      data = {
        "password" = local.secrets_refs["temporal_postgres_password"]
      }
    },
    {
      name      = "data-classification-settings-secrets"
      namespace = local.dspm_namespace
      enabled   = local.enable_cortex_platform
      data = {
        data_classification_settings_postgres_password = local.secrets_refs["data_classification_settings_postgres_password"]
      }
    },
    {
      enabled   = local.enable_cortex_platform && (!var.is_metro_tenant || var.metro_all_in_one)
      name      = "temporal-visibility-postgress"
      namespace = local.st_namespace
      data = {
        "password" = local.secrets_refs["temporal_visibility_postgres_password"]
      }
    },
    {
      enabled   = local.enable_xsoar_workers
      name      = "${var.lcaas}-xsoar-workers-gateway-secrets"
      namespace = local.st_namespace
      data = {
        xsoar_handler_key = local.secrets_refs["xsoar_handler_key"]
      }
    },
    {
      enabled   = local.enable_cortex_platform && !var.is_metro_tenant && !var.metro_all_in_one
      name      = var.is_metro_tenant ? "cts-secrets" : "${var.lcaas}-cts-secrets"
      namespace = local.cortex_cts_namespace
      data = {
        "egress.crt"       = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
        mysql_password     = local.secrets_refs["mysql_password"]
        proxysql_password     = local.secrets_refs["proxysql_password"]
        redis_password     = local.secrets_refs["redis_password"]
        cts_redis_password = local.secrets_refs["cts_redis_password"]
      }
    },
    {
      enabled   = local.enable_cortex_platform
      name      = "applications-secrets"
      namespace = local.cas_namespace
      data = {
        azure_repos_app_id         = data.google_secret_manager_secret_version.azure_repos_app_id.secret_data
        azure_repos_app_name       = data.google_secret_manager_secret_version.azure_repos_app_name.secret_data
        azure_repos_app_secret     = data.google_secret_manager_secret_version.azure_repos_app_secret.secret_data
        azure_repos_client_secret  = data.google_secret_manager_secret_version.azure_repos_client_secret.secret_data
        bitbucket_client_id        = data.google_secret_manager_secret_version.bitbucket_client_id.secret_data
        bitbucket_client_secret    = data.google_secret_manager_secret_version.bitbucket_client_secret.secret_data
        github_app_id              = data.google_secret_manager_secret_version.github_app_id.secret_data
        github_app_name            = data.google_secret_manager_secret_version.github_app_name.secret_data
        github_app_pem             = data.google_secret_manager_secret_version.github_app_pem.secret_data
        github_client_id           = data.google_secret_manager_secret_version.github_client_id.secret_data
        github_client_secret       = data.google_secret_manager_secret_version.github_client_secret.secret_data
        github_webhook_token       = data.google_secret_manager_secret_version.github_webhook_token.secret_data
        gitlab_client_id           = data.google_secret_manager_secret_version.gitlab_app_client_id.secret_data
        gitlab_client_secret       = data.google_secret_manager_secret_version.gitlab_app_secret.secret_data
        integration_encryption_key = random_id.integration_encryption_key.hex
      }
    },
    {
      enabled   = true
      name      = "${var.lcaas}-email-collector-secrets"
      namespace = local.st_namespace
      data = {
        email_client_id              = var.is_fedramp ? "" : data.google_secret_manager_secret_version.email_authorization_flow_client_id[0].secret_data
        email_client_secret          = var.is_fedramp ? "" : data.google_secret_manager_secret_version.email_authorization_flow_client_secret[0].secret_data
        email_regional_client_id     = var.is_fedramp ? "" : data.google_secret_manager_secret_version.email_authorization_flow_regional_client_id.secret_data
        email_regional_client_secret = var.is_fedramp ? "" : data.google_secret_manager_secret_version.email_authorization_flow_regional_client_secret.secret_data
      }
    },
    {
      name      = "${var.lcaas}-cwp-secrets"
      namespace = local.cwp_namespace
      enabled   = local.enable_cortex_platform
      data = {
        "egress.crt"             = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
        "apisec-uai-api-key"     = data.google_secret_manager_secret_version.apisec_uai_api_key.secret_data
        cwp_redis_password       = local.secrets_refs["cwp_redis_password"]
        redis_password           = local.secrets_refs["redis_password"]
        postgres_password        = local.secrets_refs["postgres_password"]
        mysql_password           = local.secrets_refs["mysql_password"]
        proxysql_password           = local.secrets_refs["proxysql_password"]
        AGENTCONF_ENCRYPTION_KEY = var.agentconfig_encryption_key
      }
    },
    {
      name      = "${var.lcaas}-cns-secrets"
      namespace = local.cwp_namespace
      enabled   = local.enable_cortex_platform
      data = {
        cwp_redis_password      = local.secrets_refs["cwp_redis_password"]
        postgres_password       = local.secrets_refs["postgres_password"]
        cns_neo4j_password      = local.secrets_refs["cns_neo4j_password"]
        cns_neo4j_user_password = "neo4j/${local.secrets_refs["cns_neo4j_password"]}"
      }
    },
    {
      name      = "${var.lcaas}-apisec-secrets"
      namespace = local.apisec_namespace
      enabled   = local.enable_cortex_platform
      data = {
        "apisec-uai-api-key" = data.google_secret_manager_secret_version.apisec_uai_api_key.secret_data
        "mongodb_password"   = local.secrets_refs["mongodb_password_cwp"]
      }
    },
    {
      name      = "${var.lcaas}-ciem-secrets"
      namespace = local.ciem_namespace
      enabled   = local.enable_cortex_platform
      data = {
        postgres_password = local.secrets_refs["postgres_password"]
	"egress.crt"      = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
      }
    },
    {
      name      = "${var.lcaas}-dspm-secrets"
      namespace = local.dspm_namespace
      enabled   = local.enable_cortex_platform
      data = {
        "init"                 = "init"
        "redis_password"       = local.secrets_refs["dspm_redis_password"]
        "mongodb_password_cwp" = local.secrets_refs["mongodb_password_cwp"]
        "mongodb_user"         = "root"
        "postgres_password"    = local.secrets_refs["postgres_password"]
      }
    },
    {
      name      = "${var.lcaas}-dspm-datasource-secrets"
      namespace = local.dspm_namespace
      enabled   = local.enable_cortex_platform
      data = {
        agentconfig_encryption_key = var.agentconfig_encryption_key
      }
    },
    {
      name      = "api-certs-cas"
      namespace = local.cas_namespace
      enabled   = true
      data = {
        "egress.crt" = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
      }
    },
    {
      name      = "cas-azure-repos-entra-id"
      namespace = module.create_namespace.ns[local.cas_namespace]
      enabled   = local.enable_cortex_platform
      data = {
        "azure-repos-entra-id-client-id"    = jsondecode(data.google_secret_manager_secret_version.cas_azure_repos_entra_id.secret_data)[var.viso_env]["azure-repos-entra-id-client-id"]
        "azure-repos-entra-id-secret-value" = jsondecode(data.google_secret_manager_secret_version.cas_azure_repos_entra_id.secret_data)[var.viso_env]["azure-repos-entra-id-secret-value"]
      }
    },
    {
      name      = "api-certs-dspm"
      namespace = local.dspm_namespace
      enabled   = true
      data = {
        "egress.crt" = data.google_secret_manager_secret_version.egress_proxy-ca.secret_data
      }
    },
    {
      name      = "infra-secrets"
      namespace = local.cas_namespace
      enabled   = local.enable_cortex_platform
      data = {
        neo4j_password       = local.secrets_refs["neo4j_password"]
        redis_host           = var.is_metro_tenant ? "xdr-st-${var.lcaas}-cas-redis" : "xdr-st-${var.lcaas}-redis"
        redis_port           = 6379
        redis_username       = "default"
        redis_password       = local.secrets_refs["cas_redis_password"]
        mongodb_password     = local.secrets_refs["mongodb_password_cwp"]
        mongodb_user         = "root"
        mongodb_keyfile      = local.secrets_refs["mongodb_keyfile"]
        hydra_encryption_key = data.google_secret_manager_secret_version.hydra_encryption_key[0].secret_data
      }
    },
    {
      name      = "xsoarconf-dev-prod-key"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "xsoarconf-dev-prod-key" = local.xsoar_ng_shared_key
      }
    },
    {
      name      = "iosconf"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "IosConf_auth_key_id" = data.google_secret_manager_secret_version.apple_push_key_id.secret_data
      }
    },
    {
      name      = "xdr-auth-token"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "xdr-auth-token" = var.xdr_auth_token
      }
    },
    {
      name      = "common-configmaps-secrets"
      enabled   = true
      namespace = local.st_namespace

      # Temporary workaround for new platform migration, please avoid adding additional secrets in the configmap
      data = {
        AGENTCONF_ENCRYPTION_KEY                 = var.agentconfig_encryption_key
        COMMUNICATION_SLACK_TOKEN                = local.gonzo_slack_token
        GONZO_SLACK_TOKEN                        = local.gonzo_slack_token
        WILDFIRE_APIKEY                          = var.wildfire_apikey
        XSOARCONF_AUTH_KEY                       = var.xdr_auth_token
        XSOARCONF_HTTP_COLLECTION                = var.xdr_http_collection_token
        XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY = var.xsoar_6_migration_token
        THIRDPARTYSAAS_COLLECTOR_SA_UNIQUE_ID    = module.harvester_workload_identity.google_service_unique_id
        "values.yaml" = yamlencode(
          {
            config = {
              AGENTCONF_ENCRYPTION_KEY                 = var.agentconfig_encryption_key
              COMMUNICATION_SLACK_TOKEN                = local.gonzo_slack_token
              GONZO_SLACK_TOKEN                        = local.gonzo_slack_token
              WILDFIRE_APIKEY                          = var.wildfire_apikey
              XSOARCONF_AUTH_KEY                       = var.xdr_auth_token
              XSOARCONF_HTTP_COLLECTION                = var.xdr_http_collection_token
              XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY = var.xsoar_6_migration_token
              THIRDPARTYSAAS_COLLECTOR_SA_UNIQUE_ID    = module.harvester_workload_identity.google_service_unique_id
            }
          }
        )
      }
    },
    {
      name      = "xsoar-secrets"
      enabled   = var.enable_xsoar_shared_components
      namespace = local.st_namespace
      data = {
        migration_token       = base64encode(var.xsoar_6_migration_token)
        hashed_xdr_http_token = var.hashed_xdr_http_token != null ? var.hashed_xdr_http_token : ""
        hashed_wildfire_key   = var.hashed_wildfire_key != null ? var.hashed_wildfire_key : ""
        hashed_monitoring_key = var.hashed_monitoring_key != null ? var.hashed_monitoring_key : ""

        "values.yaml" = yamlencode(
          {
            "demistoConf" = {
              "hashedMonitoringKey" = var.hashed_monitoring_key != null ? var.hashed_monitoring_key : ""
              "hashedWildfireKey"   = var.hashed_wildfire_key != null ? var.hashed_wildfire_key : ""
              "hashedXdrHttpToken"  = var.hashed_xdr_http_token != null ? var.hashed_xdr_http_token : ""
              "migrationToken"      = base64encode(var.xsoar_6_migration_token)
            }
          }
        )
      }
    },
    {
      name      = "app-hub-secrets"
      namespace = local.st_namespace
      enabled   = true
      data = {
        "secret_identifier" = local.secrets_refs["secret_identifier"]
      }
    },
    {
      name      = "singlestore-secrets"
      enabled   = local.enable_cortex_platform
      namespace = local.st_namespace
      data = {
        "adminHashedPassword" = local.enable_cortex_platform ? lookup(data.external.generate_singlestore_pass.0.result, "singlestore_hashed_password") : ""
        "license"             = data.google_secret_manager_secret_version.singlestore_license_key[0].secret_data
        "values.yaml" = yamlencode(
          {
            "adminHashedPassword" = local.enable_cortex_platform ? lookup(data.external.generate_singlestore_pass.0.result, "singlestore_hashed_password") : ""
            "license"             = data.google_secret_manager_secret_version.singlestore_license_key[0].secret_data
          }
        )
      }
    },
    {
      name      = "twistlock-secrets"
      enabled   = var.is_fedramp && !var.is_metro_tenant
      namespace = "twistlock"
      data = {
        "service-parameter"        = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_service_parameter[0].secret_data
        "defender-ca.pem"          = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_ca[0].secret_data
        "defender-client-cert.pem" = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_client_cert[0].secret_data
        "defender-client-key.pem"  = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_client_key[0].secret_data
        "admission-cert.pem"       = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_admission_key[0].secret_data
        "admission-key.pem"        = !var.is_fedramp ? "" : data.google_secret_manager_secret_version.twistlock_defenders_admission_cert[0].secret_data
      }
    },
    {
      name      = "${var.lcaas}-ddr-secrets"
      namespace = local.dspm_namespace
      enabled   = local.enable_cortex_platform
      data = {
        "init"                    = "init"
        "dacs_postgres_password"  = element(concat(random_password.data_classification_settings_postgres_password.*.result, tolist([""])), 0)
      }
    },
    {
      name      = "${var.lcaas}-wildfire-apikeys"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_email_artifacts_relay
      data = {
        "default" = var.wildfire_apikey
        "email_security" = var.email_security_wildfire_apikey
      }
    },
    {
      name      = "${var.lcaas}-neo4j-cluster-password"
      namespace = module.create_namespace.ns[local.st_namespace]
      enabled   = local.enable_neo4j_cluster
      data = {
	"NEO4J_AUTH" =  "neo4j/${random_password.neo4j_password.result}"
      }
    },
    {
      name      = "mt-secrets"
      namespace = local.metro_namespace
      enabled   = var.metro_all_in_one
      data      = local.secrets_refs
    }
  ]
}
