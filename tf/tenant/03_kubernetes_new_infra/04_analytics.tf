module "analytics_workload_identity" {
  source               = "../../modules/workload_identity"
  for_each = {
    for app in local.analytics_apps :
    app.app_name => app
    if lookup(app, "enabled", true) && contains(keys(app), "workload_identity")
  }
  providers = {
    google    = google
    google.mt = google.mt
    kubernetes = kubernetes
  }
  service_account_name  = each.value.service_account_name
  mt_dedicated_group    = lookup(each.value, "mt_dedicated_group", false)
  project_id            = var.project_id
  metro_host_project_id = var.metro_host_project_id
  wi_project_id         = local.workload_identity_project_id
  data                  = lookup(each.value, "workload_identity", {})
  namespace             = local.st_namespace
  viso_env              = var.viso_env
  create_kubernetes_sa  = false
  extra_namespaces      = var.metro_all_in_one ? [local.metro_namespace] : []
  lcaas                 = var.lcaas
}

module "create_analytics_apps" {
  source = "../../modules/app"
  for_each = {
    for app, app_value in local.analytics_apps :
    app => app_value
    if lookup(app_value, "enabled", true)
  }
  app_name             = each.value.app_name
  cron_job             = lookup(each.value, "cron_job", {})
  deployment           = lookup(each.value, "deployment", {})
  hpa                  = lookup(each.value, "hpa", {})
  vsg                  = lookup(each.value, "vsg", {})
  only_cron            = lookup(each.value, "only_cron", false)
  namespace            = local.st_namespace
  pool_tenant_creation = var.pool_tenant_creation
  service              = lookup(each.value, "service", {})
  service_account_name = each.value.service_account_name
  project_id           = var.project_id
  region               = var.region
  secrets              = lookup(each.value, "secrets", {})
}

locals {
  # these secrets are also shared with the "mega-processor" so don't use this in case you need secrets that are specifically for the alerts-emitter
  # these env vars are also shared with the "mega-processor" so don't use this in case you need secrets that are specifically for the alerts-emitter
  analytics_alerts_emitter_workload_identity = {
    bq_job_user = true
    subscriptions = [lookup(var.subscriptions_output, "alerts_emitter", null)]
    topics        = [
      lookup(var.topics_output, "lcaas_topic", null),
      lookup(var.topics_output, "alerts_fetcher", null),
      lookup(var.topics_output, "edr_topic", null),
      lookup(var.topics_output, "analytics-task-processor", null),
      lookup(var.topics_output, "email_security_alerts_topic", null),
    ]
    topics_mt = [
      [local.lf_topic, "xdr-log-forwarding-${var.viso_env}-01"]
    ]
    bq       = [
      [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
      [lookup(var.bq_output, "external_data"),"roles/bigquery.dataViewer"],
      [lookup(var.bq_output, "xdm_views"), "roles/bigquery.dataEditor"]
                ]
    buckets  = [
      [lookup(var.buckets_output, "ipl-edr-data", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
      [lookup(var.buckets_output, "analytics_data", null), "ReadAndWrite"]]
    kms_crypto_keys = concat([
      [local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"],
      [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyEncrypterDecrypter"]],
      (!var.is_metro_tenant || var.metro_all_in_one) ? [[local.email-bi-secure-key, "roles/cloudkms.cryptoKeyEncrypterDecrypter"]] : []
    )
    additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
  }

  analytics_apps = {
    analytics-alerts-emitter = {
      enabled              = local.analytics2_enabled || local.xpanse_analytics_enabled
      app_name             = "xdr-st-${var.lcaas}-analytics-alerts-emitter"
      service_account_name = "analytics-ae"
      mt_dedicated_group = true
      workload_identity = local.analytics_alerts_emitter_workload_identity
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-analytics_alerts_emitter"
      }
    }
    analytics-decider = {
      enabled              = local.analytics2_enabled || local.xpanse_analytics_enabled
      app_name             = "xdr-st-${var.lcaas}-analytics-decider"
      service_account_name = "analytics-d"
      mt_dedicated_group = true
      workload_identity = {
        multi_tenant_service = local.metro_v2
        metro_st_namespace   = local.metro_namespace
        bq_job_user = true
        subscriptions = [lookup(var.subscriptions_output, "analytics_task_processor_sub", null)]
        topics       = [
          lookup(var.topics_output, "analytics_detection_hits", null),
          lookup(var.topics_output, "edr_topic", null)
        ]
        bq      = [[lookup(var.bq_output, "analytics"), "roles/bigquery.dataEditor"]]
        buckets = [
          [lookup(var.buckets_output, "ipl-edr-data", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "ext_files_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
        ]
        kms_crypto_keys = [[local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"]]
	additional_project_roles = concat(
	  var.is_fedramp ? [] : ["roles/cloudprofiler.agent", "roles/aiplatform.customCodeServiceAgent"],
	  var.is_xdr || var.is_xsiam ? ["roles/aiplatform.user"] : []
	  )
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-decider"
      }
    }
    analytics-detection-engine = {
      enabled              = local.analytics2_enabled || local.xpanse_analytics_enabled
      app_name             = "xdr-st-${var.lcaas}-analytics-detection-engine"
      service_account_name = "analytics-de"
      mt_dedicated_group = true
      workload_identity = {
        bq_job_user = true
        subscriptions = [
          lookup(var.subscriptions_output, "edr_sub_matchingservice", null),
          lookup(var.subscriptions_output, "ext_matching_service", null)
        ]
        topics        = [
          lookup(var.topics_output, "analytics_detection_hits", null),
          lookup(var.topics_output, "edr_topic", null)
        ]
        bq            = [
          [lookup(var.bq_output, "analytics"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "external_data"),"roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "xdm_views"), "roles/bigquery.dataEditor"]
        ]

        buckets  = [
          [lookup(var.buckets_output, "ipl-edr-data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "analytics_data", null), "ReadAndWrite"],
          [lookup(var.buckets_output, "feature-data_bucket", null),"roles/storage.admin"],
          [lookup(var.buckets_output, "ext_logs_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "external-data", null), "AllowBucketsReadAccess"],
        ]
        kms_crypto_keys    = [[local.tenant_kms_keyring, "roles/cloudkms.publicKeyViewer"],
                              [local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"]
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-detection_engine"
      }
    }
    analytics-detection-engine-external-data = {
      enabled              = var.is_xsiam
      app_name             = "xdr-st-${var.lcaas}-analytics-detection-engine-external-data"
      service_account_name = "analytics-de"
    }
    analytics-profiles-orchestrator =   {
      enabled              = local.analytics2_enabled || local.xpanse_analytics_enabled
      app_name             = "xdr-st-${var.lcaas}-analytics-profiles-orchestrator"
      service_account_name = "analytics-po"
      mt_dedicated_group = true
      workload_identity = {
        multi_tenant_service = local.metro_v2
        metro_st_namespace   = local.metro_namespace
        bq_job_user = true
        bq_resource_viewer = true
        bq_data_viewer = true
        topics = [
          lookup(var.topics_output, "edr_topic", null),
          lookup(var.topics_output, "dlq-ext-topic", null),
          lookup(var.topics_output, "dss_sync_notifier_topic", null),
        ]
        bq = [
          [lookup(var.bq_output, "analytics"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "analytics_on_demand"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "analytics_profiles_views"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "analytics"), "roles/bigquery.dataOwner"]]
        buckets = [
          [lookup(var.buckets_output, "analytics_bq_export", null), "roles/storage.admin"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
        ]
        additional_project_roles = var.is_fedramp ? [] : ["roles/cloudprofiler.agent", "roles/aiplatform.customCodeServiceAgent"]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-profiles_orchestrator"
      }
    }
    analytics-task-processor = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-analytics-task-processor"
      service_account_name = "analytics-tp"
      mt_dedicated_group   = true
      workload_identity = {
        bq_job_user = true
        subscriptions = [lookup(var.subscriptions_output, "analytics_task_processor_sub", null)]
        topics       = [
          lookup(var.topics_output, "analytics_detection_hits", null),
          lookup(var.topics_output, "edr_topic", null)
        ]
        bq      = [[lookup(var.bq_output, "analytics"), "roles/bigquery.dataEditor"]]
        buckets = [
          [lookup(var.buckets_output, "ipl-edr-data", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "ext_files_bucket", null), "AllowBucketsReadAccess"],
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"],
        ]
        kms_crypto_keys = [[local.collection-security_kms_keyring, "roles/cloudkms.cryptoKeyDecrypter"]]
	    additional_project_roles = concat(
        var.is_fedramp ? [] : ["roles/cloudprofiler.agent", "roles/aiplatform.customCodeServiceAgent"],
        var.is_xdr || var.is_xsiam ? ["roles/aiplatform.user"] : []
	    )
      }
    }
    risk_score_cie_processor = {
      enabled              = (var.enable_pipeline || var.is_xpanse || local.enable_cortex_platform) && var.product_code != "agentix"
      app_name             = "xdr-st-${var.lcaas}-risk-score-cie-processor"
      service_account_name = "risk-score-cie-processor"
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-risk-score-cie-processor"
      }
      workload_identity = {
        subscriptions = ((var.is_xdr || var.is_xsiam) && var.enable_pipeline) ? [
          lookup(var.subscriptions_output, "risk_score_cie_updates", []),
        ] : []
      }
    }
    analytics-content-sync-cron-job = {
      enabled              = (var.enable_pipeline || var.is_xpanse) || contains(["c3", "x0", "x1", "x3", "x5", "ent_plus"], var.product_code)
      app_name             = "xdr-st-${var.lcaas}-analytics-content-sync-cron-job"
      service_account_name = "analytics-content-sync"
      mt_dedicated_group   = true
      workload_identity = {
        bq_job_user = true
        bq = [[lookup(var.bq_output, "analytics"), "roles/bigquery.dataEditor"]]

        buckets = [
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"]
        ]
      }
    }

    graph-detection-content-sync-cron-job = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-graph-detection-content-sync-cron-job"
      service_account_name = "dp-analytics-graph-de-sync"
      mt_dedicated_group = true
      workload_identity = {
        bq_job_user = true

        buckets = [
          [lookup(var.buckets_output, "feature-data_bucket", null), "AllowBucketsReadAccess"]
        ]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-dp_analytics_graph_detection_content_sync"
      }
    }

    dp-analytics-graph-detection-engine = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-analytics-graph-detection-engine"
      service_account_name = "dp-analytics-graph-de"
      mt_dedicated_group = true
      workload_identity = {
        bq_job_user = true
        topics        = [
          lookup(var.topics_output, "analytics_graph_detection_hits", null),
        ]
      }
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-dp_analytics_graph_detection_engine"
      }
    }

    dp-analytics-graph-emitter = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-analytics-graph-emitter"
      service_account_name = "dp-analytics-graph-emitter"
      mt_dedicated_group   = true
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-dp-analytics-graph-emitter"
      }
      workload_identity = {
        bq_job_user = true
        subscriptions = [lookup(var.subscriptions_output, "graph_emitter", null)]
        topics = [
          lookup(var.topics_output, "ap_issue_upsert", ""),
          lookup(var.topics_output, "ap_issue_create", ""),
          lookup(var.topics_output, "ap_issue_update", ""),
          lookup(var.topics_output, "ap_issue_ingest_feedback", ""),
          lookup(var.topics_output, "ap_issue_ingestion_errors", "")
        ]
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataEditor"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataEditor"]
        ]
      }
    }

    dp-analytics-graph-issue-mgr = {
      enabled              = local.enable_cortex_platform
      app_name             = "xdr-st-${var.lcaas}-dp-analytics-graph-issue-mgr"
      service_account_name = "dp-analytics-graph-issue-mgr"
      mt_dedicated_group   = true
      secrets = {
        enabled = var.is_metro_tenant
        mysql_username = "${var.metro_tenant_index}-dp-analytics-graph-issue-mgr"
      }
      workload_identity = {
        bq_job_user = true
        subscriptions = [lookup(var.subscriptions_output, "ap_issue_ingest_feedback_dp_analytics_graph_sub", null),
                         lookup(var.subscriptions_output, "ap_issue_ingestion_errors_dp_analytics_graph_sub", null)]
        bq = [
          [lookup(var.bq_output, "public_platform"), "roles/bigquery.dataViewer"],
          [lookup(var.bq_output, "ds"), "roles/bigquery.dataViewer"]
        ]
      }
    }
  }
}
