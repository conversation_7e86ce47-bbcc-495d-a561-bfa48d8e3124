module "create_namespace" {
  source          = "../../modules/namespace"
  namespaces      = var.is_metro_tenant ? local.metro_tenant_namespaces : local.namespaces
}

resource "kubernetes_network_policy_v1" "metro_tenant" {
  count = var.is_metro_tenant && !var.metro_all_in_one ? 1 : 0
  metadata {
    name      = "no-cross-talk"
    namespace = local.st_namespace
  }
  spec {
    policy_types = [
      "Ingress"
    ]
    pod_selector {}
    ingress {
      # allow ingress traffic from the GKE nodes to allow communication from the proxy-vm into k8s services (UI access etc)
      from {
        ip_block {
          cidr = "**********/20"
        }
      }
      # Explicitly allow internal NS connection
      from {
        pod_selector {}
      }
      # Block cross-tenant connection. Allow only monitoring NS to access pods
      from {
        namespace_selector {
          match_expressions {
            key = "kubernetes.io/metadata.name"
            operator = "In"
            values = [
              "monitoring",
              "cronus-operator",
              "kube-system",
              "xdr-mt",
            ]
          }
        }
      }
    }
  }
}

locals {
  namespaces = [
    {
    name = local.st_namespace
    }, {
    name = local.monitoring_namespace
    }, {
    name = "custom-metrics"
    }, {
    name = "twistlock"
    },{
    name = local.cert_manager_namespace
    },{
    name = "scylla-operator-system"
    },{
    name = local.scylla_operator_namespace
    },{
    name = "scylla"
    },{
    name = "scylla-iplen"
    },{
    name = "story-builder"
    },{
    name = "scylla-xcloud"
    },{
    name = local.xdr_agent_namespace
    },{
    name = "cortex-cts"
    },{
    name = local.cloudsec_namespace
    },{
    name = local.metrus_namespace
    },{
    name = local.cas_namespace
    },{
    name = local.pithos_namespace
    },{
    name = local.xsoar_jobs_namespace
    },{
    name = local.apisec_namespace
    },{
    name = local.ciem_namespace
    },{
    name = local.cwp_namespace
    },{
    name = local.dspm_namespace
    },{
    name = local.alyx_namespace // TODO: delete this, replaced by dp_ipl_alyx_namespace
    },{
    name = local.dp_ipl_alyx_namespace
    },{
    name = local.external_secrets_namespace
    }
  ]

  metro_tenant_namespaces = concat(
    [
      {name = local.st_namespace}
    ],
    !var.metro_all_in_one ? [] : [
      {name = local.alyx_namespace},
      {name = local.dp_ipl_alyx_namespace},
      {name = local.metrus_namespace},
      {name = local.pithos_namespace},
      {name = local.metro_namespace},
      {name = local.monitoring_namespace},
      {name = local.scylla_namespace},
      {name = local.scylla_iplen_namespace},
      {name = local.scylla_xcloud_namespace},
      {name = local.xdr_agent_namespace},
      {name = local.cert_manager_namespace},
      {name = local.scylla_operator_namespace},
      {name = local.external_secrets_namespace}
    ]
  )

  metrus_namespace = "metrus"
  pithos_namespace = "pithos"
  metro_namespace = "xdr-mt"
  scylla_namespace = "scylla"
  scylla_iplen_namespace = "scylla-iplen"
  scylla_xcloud_namespace = "scylla-xcloud"
  cert_manager_namespace = "cert-manager"
  xdr_agent_namespace = "xdr-agent-daemon"
  scylla_operator_namespace = "scylla-operator"
  external_secrets_namespace = "external-secrets"

  st_namespace = var.is_metro_tenant ? "xdr-st-${var.lcaas}" : "xdr-st"
  cortex_cts_namespace = var.is_metro_tenant ? local.metro_namespace : "cortex-cts"
  monitoring_namespace = "monitoring"
  cas_namespace = var.is_metro_tenant ? local.st_namespace : "cas"
  xsoar_jobs_namespace = var.is_metro_tenant ? local.st_namespace : "xsoar-jobs"
  cloudsec_namespace = var.is_metro_tenant ? local.st_namespace : "cloudsec"
  apisec_namespace = var.is_metro_tenant ? local.st_namespace : "apisec"
  ciem_namespace = var.is_metro_tenant ? local.st_namespace : "ciem"
  cwp_namespace = var.is_metro_tenant ? local.st_namespace : "cwp"
  dspm_namespace = var.is_metro_tenant ? local.st_namespace : "dspm"
  alyx_namespace = "alyx" // deprecated - replacd by dp_ipl_alyx_namespace, kept to avoid potential TF issues
  dp_ipl_alyx_namespace = "dp-ipl-alyx"
}