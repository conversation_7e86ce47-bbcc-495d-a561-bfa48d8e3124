variable "viso_new_platform" { default = false }
variable "big_tenant" { default = false }
variable "billing_account" { default = "0191EA-C0E00D-70713E" }
variable "enable_k8s_beta_apis" { default = false }
variable "bq_location" {}
variable "cloud_agents" { default = 0 }
variable "cold_retention" { default = false }
variable "consul_datacenter" {}
variable "consul_host" {}
variable "consul_token" { default = "" }
variable "disposable" { default = false }
variable "csp_id" { default = "unknown" }
variable "edr_agents" {}
variable "egress_enabled" { default = false }
variable "enable_dirsync" { default = false } # can be removed after full migration to new infra
variable "pool_tenant_activation" { default = false }
variable "enable_asm" {
  type    = bool
  default = false
}
variable "enable_byok" {
  type = bool
}
variable "enable_replicator" {}
variable "enable_assured_workloads" { default = false }
variable "enable_xcloud" { default = false }
variable "enc_key" {}
variable "epp_agents" {}
variable "epp_only" {}
variable "external_fqdn" {}
variable "external_crtx_fqdn" {}
variable "firestore_access_service_key" {}
variable "firestore_public_project_id" { default = "" } # can be removed after full migration to new infra
variable "forensics" { default = false }
variable "xdr_gb_licenses" { default = 0 }
variable "host_project" {}
variable "host_project_subnetwork_name" {}
variable "instance_name" {}
variable "lcaas" {}
variable "legacy" {}
variable "license_expiration" { default = "unknown" }
variable "license_region" { default = "" } # can be removed after full migration to new infra
variable "multi_project_postfix" {}
variable "mysql_backup_pvc" { default = false }
variable "metro_version" { default = "" }
variable "metro_host_project_id" { default = "" }
variable "metro_host_id" { default = "" }
variable "metro_tenant_index" {
  type    = number
  default = 0
}
variable "metro_all_in_one" {
  type = bool
  default = false
}
variable "is_metro_tenant" {
  type = bool
  default = false
}
variable "metro_host_zone" { default = "" }
variable "num_licenses" {}
variable "overrides" { default = {} }
variable "owner" { default = "unknown" }
variable "owner_group" { default = "unknown" }
variable "parent_project_id" { default = "" }
variable "pool_tenant_creation" { default = false }
variable "product_type" { default = "" }
variable "project_id" {}
variable "engine_project_id" {}
variable "app_hub_project_id" {}
variable "outpost_project_id" {}
variable "oci_id" {default = ""}
variable "oci_project_id" {default = ""}
variable "oci_pool_onboard" {default = false}
variable "oci_pool_upgrade" {default = false}
variable "enable_aws_outpost_all_regions" {}
variable "enable_azure_outpost_all_regions" {}
variable "enable_gcp_outpost_all_regions" {}
variable "enable_oci_outpost_all_regions" {}
variable "project_prefix" {}
variable "region" {}
variable "report_encryption_key" {}
variable "scale_xdr_licenses" {}
variable "support_account_name" { default = "" } # can be removed after full migration to new infra
variable "creation_date" { default = "unknown" }
variable "tenant_type" { default = "unknown" }
variable "viso_env" {}
variable "wildfire_apikey" {}
variable "email_security_wildfire_apikey" { default = "" }
variable "hashed_wildfire_key" { default = "dummy_string" }
variable "hashed_monitoring_key" { default = "dummy_string" }
variable "hashed_xdr_http_token" { default = "dummy_string" }
variable "xdr_auth_token" {}
variable "xdr_http_collection_token" {}
variable "xdr_env" {}
variable "xdr_id" {}
variable "xdr_licenses" {} # need to keep this here, otherwise viso TF runner removes them from the passed vars
variable "xsoar_mssp_child" { default = false }
variable "xsoar_mssp_master" { default = false }
variable "xsiam_mssp_master" { default = false }
variable "is_mssp_child_xdr_xsiam" { default = false } # for XDR/XSIAM
variable "xpanse_asset_feedback_credentials_api_key" {
  type    = string
  default = ""
}
variable "xpanse_tenant_credentials_client_id" {
  type    = string
  default = ""
}
variable "xpanse_tenant_credentials_client_secret" {
  type    = string
  default = ""
}
variable "zone" {}
variable "agent_analytics_topic_name" {
  default = "agent-analytics-topic"
}
variable "edr_retention_ds" {
  default = "edr_retention_ds"
}
variable "enable_scylla" {
  default = false
}
variable "enable_scylla_iplen" {
  default = false
}
variable "encrypt_fas_keyring_location" {
  default = "us-central1"
}
variable "encrypt_fas_keyring_name" {
  default = "tenants"
}
variable "regional_kubernetes" {
  default     = false
  description = "Set to true if we're going to make the k8s regional.  Only in Fedramp currently."
}
variable "saas_url" {
  default     = "http://************:18443/saas_proxy"
  description = "the internal ip:port of saas service from proxy vm"
}
variable "saas_platform_url" {
  default     = "http://************:18443/saas_proxy_platform"
  description = "the internal ip:port of saas platform service from proxy vm"
}
variable "uat_switcher_url" {
  default = "https://apps-qa-uat.qa.appsvc.paloaltonetworks.com"
}
variable "scylla_admin_username" {
  default = "root"
}
variable "upgrade_phase" {
  description = "Specifies the current upgrade phase for the tenant"
  type        = string
}
variable "xsoar_mssp_dev" {
  type    = bool
  default = false
}
variable "xsoar_6_sn" { default = "" }
variable "xsoar_6_env" { default = "" }
variable "xsoar_6_host" { default = "" }
variable "xsoar_6_migration_token" {
  default   = "admin"
  sensitive = true
}
variable "xsoar_6_mig_size" {
  default = "large"
}
variable "xsoar_6_standalone" {
  default = false
  type    = bool
}
variable "xsoar_6_byog_target_branch" {
  default = ""
  type    = string
}
variable "xsoar_6_byog_use_multi_branch" {
  default = false
  type    = bool
}
variable "xsoar_6_emails_cc" {
  default = ""
}
variable "xsoar_6_cutoff_date" {
  default = ""
}
variable "xsoar_6_cutoff_start_hour" {
  default = 0
  type    = number
}
variable "xsoar_6_cutoff_end_hour" {
  default = 0
  type    = number
}
variable "xsoar_6_account_id" { default = "" }
variable "is_xsoar_6_migration" { default = false }
variable "is_xsoar_onprem_migration" { default = false }
variable "xsiam_users" { default = 0 }
variable "xsiam_licenses" { default = 0 }
variable "enable_gke_metering" {
  type    = bool
  default = false
}
variable "metering_start_date" {
  description = "Format: YYYY-MM-DD"
  default     = ""
}
variable "enable_network_egress_metering" {
  type    = bool
  default = true
}
variable "enable_resource_consumption_metering" {
  type    = bool
  default = true
}
variable "parent_external_fqdn" { default = "" }
variable "uptime_otp_id" {
  default   = ""
  sensitive = true
}
variable "xpanse_aum_count" {
  default = 0
}

variable "app_images" {
  type = map(string)
}

variable "collection_enc_key" {
  description = " additional enc key for saas-collection deployment"
}

variable "product_code" {
  default     = "legacy"
  description = "The cortex platform product code (c1/c3/x0 etc)"
}

variable "cortex_platform" {
  default     = false
  description = "Whether this tenant is a cortex platform tenant or not"
}

variable "enable_cloud_posture" {
  type    = bool
  default = false
}

variable "enable_cloud_appsec" {
  type    = bool
  default = false
}

variable "enable_itdr" {
  type    = bool
  default = false
}

variable "is_xsoar_legacy_spec" {
  type    = bool
  default = false
}

variable "viso_version" {
  description = "Used for viso version checks such as in proxy vm image"
}

variable "apilot_project_id" {
  type    = string
  default = null
}

variable "enable_alyx" {
  description = "used to turn the alyx nodepool on"
  default = false
  type = bool
}

locals {
  gke_location               = (var.is_metro_tenant && !var.metro_all_in_one) ? var.metro_host_zone : local.regional_kubernetes ? var.region : var.zone
  backend_version            = split(":", lookup(var.app_images, "backend", null))[1]
  elastic_customer_migration = var.xsoar_6_account_id == "************"
  xsoar_6_mig_size           = lookup(var.overrides, "xsoar_6_mig_size", var.viso_env == "dev" ? "small" : var.xsoar_6_mig_size)
  xsoar_mig_specs = {
    micro = {
      node_pool_machine_type = "none"
      xsoar_mig_fuse_limit   = "0"
      xsoar_mig_cpu_request  = "0.5"
      xsoar_mig_mem_request  = "1Gi"
      xsoar_mig_cpu_limit    = "0.5"
      xsoar_mig_mem_limit    = "1Gi"
      xsoar_mig_pvc_size     = local.elastic_customer_migration ? 100 : 2000
    }
    small = {
      node_pool_machine_type = "e2-standard-8"
      xsoar_mig_fuse_limit   = "1"
      xsoar_mig_cpu_request  = "5"
      xsoar_mig_mem_request  = "25Gi"
      xsoar_mig_cpu_limit    = "7.5"
      xsoar_mig_mem_limit    = "27Gi"
      xsoar_mig_pvc_size     = local.elastic_customer_migration ? 100 : 1000
    }
    medium = {
      node_pool_machine_type = local.elastic_customer_migration ? "e2-standard-16" : "e2-highmem-16"
      xsoar_mig_fuse_limit   = "1"
      xsoar_mig_cpu_request  = local.elastic_customer_migration ? "15.5" : "14"
      xsoar_mig_mem_request  = local.elastic_customer_migration ? "60Gi" : "110Gi"
      xsoar_mig_cpu_limit    = local.elastic_customer_migration ? "15.5" : "15"
      xsoar_mig_mem_limit    = local.elastic_customer_migration ? "60Gi" : "120Gi"
      xsoar_mig_pvc_size     = local.elastic_customer_migration ? 100 : 1000
    }
    large = {
      node_pool_machine_type = "e2-standard-32"
      xsoar_mig_fuse_limit   = "1"
      xsoar_mig_cpu_request  = "30"
      xsoar_mig_mem_request  = "110Gi"
      xsoar_mig_cpu_limit    = "31"
      xsoar_mig_mem_limit    = "120Gi"
      xsoar_mig_pvc_size     = local.elastic_customer_migration ? 100 : 2000
    }
  }
  bq_location                       = var.bq_location
  product_type                      = lower(var.product_type)
  product_code                      = lower(var.product_code)
  is_xsiam                          = local.product_type == "xsiam" || local.enable_cortex_platform #TODO: remove cloud/cortex_platform condition
  is_cloud                          = local.product_type == "cloud"
  is_xsoar                          = local.product_type == "xsoar"
  is_xpanse                         = local.product_type == "xpanse"
  is_xdr                            = local.product_type == "xdr"
  customer_dev_tenant               = var.pool_tenant_creation ? false : strcontains(var.xdr_id, "dev") && !var.xsoar_mssp_child
  enable_xsoar_shared_components    = contains(["xsoar", "xsiam", "xpanse"], local.product_type) || local.enable_cortex_platform #TODO: remove cloud/cortex_platform condition
  enable_itdr                       = var.enable_itdr || contains(["c3", "x0", "x1", "x3", "x5"], local.product_code)
  enable_research_autopilot         = lookup(var.overrides, "enable_research_autopilot", false)
  cc_fqdn                           = var.pool_tenant_creation ? "" : "cc-${local.tenant_uuid}${local.record_postfix}"
  ch_fqdn                           = var.pool_tenant_creation ? "" : "ch-${local.tenant_uuid}${local.record_postfix}"
  dc_fqdn                           = var.pool_tenant_creation ? "" : "dc-${local.tenant_uuid}${local.record_postfix}"
  pro_agents                        = var.edr_agents + var.cloud_agents + var.xsiam_users
  total_agents                      = var.epp_agents + local.pro_agents
  megatron_xdr                      = lookup(var.overrides, "megatron_tenant", false) || lookup(var.overrides, "megatron_xdr", false)
  megatron_xsoar                    = lookup(var.overrides, "megatron_xsoar", false)
  monthly_xsiam_licenses            = ceil(var.xsiam_licenses * 30 / 1000)
  gb_licenses_in_monthly_tb         = ceil(var.xdr_gb_licenses * 30 / 1000)
  tb_licenses                       = var.scale_xdr_licenses + local.monthly_xsiam_licenses + local.gb_licenses_in_monthly_tb
  pro_tenant                        = local.pro_agents > 0 || local.tb_licenses > 0
  dml_scale_base                    = local.pro_agents + (800 * local.tb_licenses)
  enable_scylla                     = !(var.epp_only || local.is_xsoar)
  enable_cronus                     = local.enable_pipeline && lookup(var.overrides, "enable_cronus", false)
  enable_alyx                       = (!var.is_metro_tenant || var.metro_all_in_one) && local.enable_pipeline && lookup(var.overrides, "enable_alyx", false)
  scylla_standalone                 = lower(lookup(var.overrides, "scylla_standalone", false)) || local.prod_spec || var.viso_env != "dev" && !local.is_xsoar
  scylla_node_count_license         = local.is_xpanse || !local.scylla_standalone ? 1 : min(max(ceil(local.dml_scale_base / (local.enable_cronus ? 7000.0 : 1400.0)), 1), (local.enable_cronus ? 35 : 60))
  scylla_nodes_count                = lower(local.enable_scylla) ? lookup(var.overrides, "scylla_node_count", local.scylla_node_count_license) : 0
  cronus_deploy_idle_resources      = local.enable_cronus || lookup(var.overrides, "cronus_deploy_idle_resources", false)
  cronus_standalone                 = local.cronus_deploy_idle_resources && (lookup(var.overrides, "cronus_standalone", false) || var.viso_env != "dev" || local.prod_spec || var.is_metro_tenant)
  cronus_node_count                 = lookup(var.overrides, "cronus_node_count", local.is_xpanse || !local.cronus_standalone ? 1 : max(min(ceil(local.dml_scale_base / 2500), 150), 1))
  pithos_node_count                 = lookup(var.overrides, "pithos_node_count", 0)
  scylla_iplen_node_count           = lookup(var.overrides, "initial_scylla_iplen_node_count", min(max(local.cronus_node_count), 60))
  rocksdb_standalone                = lookup(var.overrides, "rocksdb_standalone", false) || local.megatron_xdr
  elasticsearch_standalone          = lower(lookup(var.overrides, "elasticsearch_standalone", false))
  enable_xcloud                     = lower(var.enable_xcloud) && !lower(var.epp_only) || local.is_xpanse || local.enable_cortex_platform
  enable_pipeline                   = !(local.is_xsoar || lower(var.epp_only) || local.is_xpanse)
  record_postfix                    = var.viso_env == "dev" ? "-dev" : ""
  redis_split                       = lookup(var.overrides, "redis_split", false)
  small_epp                         = var.epp_agents <= 1000 && lower(var.epp_only)
  product_tier                      = local.product_type != "xdr" ? local.product_type : local.small_epp ? "small_epp" : lower(var.epp_only) ? "epp" : var.xdr_licenses > 0 ? "xdr" : "edr"
  story_builder_replica_count       = var.viso_env == "dev" && !local.prod_spec ? 1 : local.is_xpanse || var.is_metro_tenant ? 1 : min(ceil((local.pro_agents + (800 * local.tb_licenses)) / 5000.0 + lookup(var.overrides, "storybuilder_replica_count_addition_override", 0)), 30)
  tenant_uuid                       = element(split(".", var.external_fqdn), 0)
  xcloud_aws_account_id             = var.viso_env == "dev" ? "************" : var.viso_env == "prod-fr" ? "************" : var.viso_env == "prod-gv" ? "**********" : "************"
  xcloud_standalone_deployment      = local.enable_xcloud && lower(lookup(var.overrides, "xcloud_standalone_deployment", false) || (local.prod_spec && var.viso_env == "dev") || (var.viso_env != "dev" && var.tenant_type != "internal") || !local.is_xpanse)
  is_fedramp                        = var.viso_env == "prod-fr" || var.viso_env == "prod-gv"
  app_switcher_url                  = var.viso_env == "dev" ? "https://apps-qa2.app-portal-qa.us.paloaltonetworks.com" : "https://apps.paloaltonetworks.com"
  appswitcher_js                    = var.viso_env == "dev" ? "https://d1z5jbpmqvye3f.cloudfront.net/app-switcher/index.js" : "https://d2aqaxyce54jsd.cloudfront.net/app-switcher/index.js"
  backend_image_name                = "cortex-xdr/backend"
  certs_project                     = local.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
  changed_verdicts_bucket           = local.is_fedramp ? "changed-verdicts-${var.viso_env}" : var.viso_env == "dev" ? "changed-verdicts-dev" : "changed-verdicts-prod"
  docker_image_gar_location         = "us-docker.pkg.dev/xdr-registry-${var.multi_project_postfix}-01"
  dumpster_list                     = ["prod-au", "prod-ca", "prod-ch", "prod-de", "prod-jp", "prod-in", "prod-pl", "prod-qt", "prod-sg", "prod-tw", "prod-fa", "prod-il", "prod-id", "prod-sa", "prod-es", "prod-pr", "prod-it", "prod-kr", "prod-za", "prod-br", "prod-dl"]
  dumpsterconf_activate_gcs_mode    = contains(local.dumpster_list, var.viso_env) ? "True" : "False"
  enable_analytics                  = var.viso_env != "dev"
  enable_twistlock                  = local.is_fedramp
  folder_id                         = var.enable_assured_workloads ? "508534267239" : local.is_qa_automation_tenant || local.is_perf_tenant ? lookup(local.qa_folder_id_map, var.viso_env) : lookup(local.folder_id_map, var.viso_env)
  use_crtx_domain                   = local.is_xpanse || local.is_xsoar
  gcp_xdr_dns_zone_name             = lookup(local.gcp_xdr_dns_zone_map, var.viso_env, "")
  gcp_crtx_dns_zone_name            = "crtx-${var.viso_env == "prod-fr" ? "federal" : var.viso_env != "dev" ? trimprefix(var.viso_env, "prod-") : var.project_prefix == "qa2-test" ? "qa2-uat-us" : "qa4-uat-us"}-paloaltonetworks-com"
  gcp_dns_project                   = "xdr-dns-${var.viso_env}-01"
  global_bioc_bucket                = local.is_fedramp ? "global-bioc-rules-${var.viso_env}" : var.viso_env == "dev" ? "global-bioc-rules-dev" : "global-bioc-rules-prod"
  global_bioc_project               = local.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
  global_content_bucket             = local.is_fedramp ? "global-content-profiles-policy-${var.viso_env}" : var.viso_env == "dev" ? "global-content-profiles-policy-dev" : "global-content-profiles-policy"
  global_content_storage_project_id = local.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
  global_script_bucket              = var.viso_env == "prod-fr" ? "global-script-bucket-fr" : var.viso_env == "prod-gv" ? "global-script-bucket-gv" : var.viso_env == "dev" ? "global-script-bucket-dev" : "global-script-bucket"
  group_env                         = var.viso_env == "dev" ? var.viso_env : replace(var.viso_env, "prod-", "")
  is_dev                            = var.viso_env != "dev"
  logout_url                        = var.viso_env == "dev" ? "https://logintest.paloaltonetworks.com/logout?returnurl=https://apps-qa2.app-portal-qa.us.paloaltonetworks.com/" : "https://login.paloaltonetworks.com/logout?returnurl=https://apps.paloaltonetworks.com"
  lrc_addr                          = lookup(local.lrc_addr_map, var.viso_env, local.lrc_addr_map["default"])
  msspconf_permissions_api_url      = var.viso_env == "dev" ? "https://app-compatibility-qa-uat.qa.appsvc.paloaltonetworks.com/xdr/api/v1/combined-service-mappings" : "https://app-compatibility.appsvc.paloaltonetworks.com/xdr/api/v1/combined-service-mappings"
  msspconf_permissions_by_csp_url   = var.viso_env == "dev" ? "https://app-compatibility-qa-uat.qa.appsvc.paloaltonetworks.com/xdr/api/v1/service-mappings" : "https://app-compatibility.appsvc.paloaltonetworks.com/xdr/api/v1/service-mappings"
  pan_content_rules_bucket          = local.is_fedramp ? "global-pan-content-rules-${var.viso_env}" : var.viso_env == "dev" ? "global-pan-content-rules-dev" : "global-pan-content-rules"
  pan_content_rules_project         = local.is_fedramp ? "xdr-mag-shared-${var.viso_env}-01" : "xdr-zeus"
  rbacconf_base_uri                 = var.viso_env == "dev" ? "https://rbac-qa.qa.appsvc.paloaltonetworks.com/auth/rbac" : "https://rbac.appsvc.paloaltonetworks.com/auth/rbac"
  regional_kubernetes               = lower(lookup(var.overrides, "regional_kubernetes", false))
  multi_zoned_nodepools             = lower(lookup(var.overrides, "multi_zoned_nodepools", false))
  router_sa_google_group            = var.viso_env == "prod-fr" ? "<EMAIL>" : var.viso_env == "prod-gv" ? "<EMAIL>" : "gcplocal-xdr-router-${local.group_env}@paloaltonetworks.com"
  autopilot_sa_google_group         = "gcplocal-xdr-wi-autopilot-${var.viso_env}@paloaltonetworks.com"
  slackconf_hydra_redirect          = lookup(local.slackconf_hydra_redirect_map, var.viso_env, local.slackconf_hydra_redirect_map["default"])
  streaming_api_sa                  = var.enable_assured_workloads ? "<EMAIL>" : lookup(local.streaming_api_sa_msp, var.viso_env, local.streaming_api_sa_msp["default"])
  timezone_offset                   = lookup(local.timezone_offsets, var.viso_env)
  twistlock_defender_cluster_id     = var.viso_env == "dev" ? "cac54348-8f1f-8143-0df0-c6a22413ba42" : var.viso_env == "prod-fr" ? "6e813423-cb67-1053-fce1-e5ea0f764f7f" : var.viso_env == "prod-gv" ? "********-72ca-d6bf-3dc0-3b95fa95c434" : ""
  twistlock_defender_ws_address     = var.viso_env == "dev" ? "wss://*************:8084" : var.viso_env == "prod-fr" ? "wss://************:8084" : var.viso_env == "prod-gv" ? "wss://************:8084" : ""
  traps_sa_bq_reader                = local.is_fedramp ? "terraform@xdr-mag-shared-${var.viso_env}-01.iam.gserviceaccount.com" : "<EMAIL>"
  vulnerability_assessment_bucket   = local.is_fedramp ? "global-va-storage-${var.viso_env}" : var.viso_env == "dev" ? "global-va-storage-dev" : "global-va-storage"
  xdr_analytics_project_name        = !local.is_fedramp ? (var.viso_env == "dev" ? "xdr-collection-qa-01" : "xdr-collection-${var.viso_env}-01") : ""
  USAGE_METERING_START_DATE         = var.metering_start_date != "" ? var.metering_start_date : formatdate("YYYY-MM-DD", timestamp())
  region_short_code                 = replace(var.viso_env, "prod-", "")
  wildfire_url                      = lookup(local.wildfire_url_map, var.viso_env, local.wildfire_url_map["default"])
  streaming_api_sa_msp = {
    default   = "stream-${local.region_short_code}-prd@cdl-prd1-${local.region_short_code}.iam.gserviceaccount.com"
    "dev"     = "<EMAIL>"
    "prod-eu" = "<EMAIL>"
    "prod-fa" = "<EMAIL>"
    "prod-fr" = "<EMAIL>"
    "prod-gv" = "<EMAIL>"
    "prod-qt" = "<EMAIL>"
    "prod-uk" = "<EMAIL>"
    "prod-us" = "<EMAIL>"
    "prod-pr" = "<EMAIL>"
    "prod-br" = "<EMAIL>"
  }
  timezone_offsets = {
    "dev"     = "-5"
    "prod-au" = "+10"
    "prod-ca" = "-4"
    "prod-ch" = "+1"
    "prod-de" = "+2"
    "prod-es" = "+1"
    "prod-eu" = "+2"
    "prod-fa" = "+2"
    "prod-fr" = "-5"
    "prod-gv" = "-5"
    "prod-id" = "+5"
    "prod-il" = "+2"
    "prod-in" = "+5:30"
    "prod-jp" = "+9"
    "prod-pl" = "+1"
    "prod-qt" = "+3"
    "prod-sa" = "+1"
    "prod-sg" = "+8"
    "prod-tw" = "+8"
    "prod-uk" = "+1"
    "prod-us" = "-5"
    "prod-pr" = "-5"
    "prod-it" = "+1"
    "prod-kr" = "+9"
    "prod-za" = "+2"
    "prod-br" = "-3"
    "prod-dl" = "+5:30"
  }
  folder_id_map = {
    "dev"     = "***********",
    "prod-au" = "************",
    "prod-ca" = "************",
    "prod-ch" = "619477985992",
    "prod-de" = "336605448487",
    "prod-es" = "941468858838",
    "prod-pr" = "405831375015",
    "prod-it" = "140819324289",
    "prod-kr" = "162099989826",
    "prod-eu" = "799405912974",
    "prod-fa" = "501804922369",
    "prod-fr" = "231806466472",
    "prod-id" = "972258531230",
    "prod-il" = "779035588301",
    "prod-gv" = "670828735148",
    "prod-in" = "365521865416",
    "prod-jp" = "601916846941",
    "prod-pl" = "879311924871",
    "prod-qt" = "441812112894",
    "prod-sa" = "663551199225",
    "prod-sg" = "411625970768",
    "prod-tw" = "310470762994",
    "prod-uk" = "85533968060",
    "prod-us" = "731022455913",
    "prod-za" = "811423393722",
    "prod-br" = "405206070301",
    "prod-dl" = "876267036423",
  }

  qa_folder_id_map = {
    "dev"     = "291888857048",
    "prod-au" = "578501903385",
    "prod-ca" = "941307450674",
    "prod-ch" = "136213985230",
    "prod-de" = "753757792410",
    "prod-es" = "844915987639",
    "prod-pr" = "497490463179",
    "prod-it" = "140106546971",
    "prod-kr" = "1007400196666",
    "prod-eu" = "370762531485",
    "prod-fa" = "1038292127785",
    "prod-fr" = "251368044901",
    "prod-id" = "645323361955",
    "prod-il" = "966436200612",
    "prod-gv" = "1048008086514",
    "prod-in" = "361996389838",
    "prod-jp" = "1002444337414",
    "prod-pl" = "887883912652",
    "prod-qt" = "633653976081",
    "prod-sa" = "650292833034",
    "prod-sg" = "125879730586",
    "prod-tw" = "441815193942",
    "prod-uk" = "847277567811",
    "prod-us" = "871501527408",
    "prod-za" = "457000126881",
    "prod-br" = "559333782892",
    "prod-dl" = "657119197265",
  }

  lrc_addr_map = {
    default   = "lrc-${local.region_short_code}.paloaltonetworks.com:443"
    "dev"     = "lrc.paloaltonetworks.com:443",
    "prod-es" = "lrc.es.paloaltonetworks.com:443",
    "prod-pr" = "lrc.pr.paloaltonetworks.com:443",
    "prod-it" = "lrc.it.paloaltonetworks.com:443",
    "prod-kr" = "lrc.kr.paloaltonetworks.com:443",
    "prod-za" = "lrc.za.paloaltonetworks.com:443",
    "prod-br" = "lrc.br.paloaltonetworks.com:443",
    "prod-dl" = "lrc.dl.paloaltonetworks.com:443",
    "prod-fa" = "lrc.fa.paloaltonetworks.com:443",
    "prod-il" = "lrc.il.paloaltonetworks.com:443",
    "prod-id" = "lrc.id.paloaltonetworks.com:443",
    "prod-sa" = "lrc.sa.paloaltonetworks.com:443",
    "prod-fr" = "lrc-fed.paloaltonetworks.com:443",
    "prod-br" = "lrc.br.paloaltonetworks.com:443",
    "prod-dl" = "lrc.dl.paloaltonetworks.com:443",
  }
  slackconf_hydra_redirect_map = {
    default   = "https://slck.xdr.${local.region_short_code}.paloaltonetworks.com/integ",
    "dev"     = "https://slck.xdr-qa2-uat.us.paloaltonetworks.com/integ",
    "prod-fr" = "https://slck-prod-fed.xdr.federal.paloaltonetworks.com/integ",
    "prod-gv" = "https://slck-prod-gv.xdr.gv.paloaltonetworks.com/integ",
  }
  gcp_xdr_dns_zone_map = {
    "prod-ch": "xdr-ch-paloaltonetworks-com",
    "prod-es": "xdr-es-paloaltonetworks-com",
    "prod-pr": "xdr-pr-paloaltonetworks-com",
    "prod-it": "xdr-it-paloaltonetworks-com",
    "prod-kr": "xdr-kr-paloaltonetworks-com",
    "prod-za": "xdr-za-paloaltonetworks-com",
    "prod-fa": "xdr-fa-paloaltonetworks-com",
    "prod-id": "xdr-id-paloaltonetworks-com",
    "prod-il": "xdr-il-paloaltonetworks-com",
    "prod-fr": "xdr-federal-paloaltonetworks-com",
    "prod-gv": "xdr-gv-paloaltonetworks-com",
    "prod-pl": "xdr-pl-paloaltonetworks-com",
    "prod-qt": "xdr-qt-paloaltonetworks-com",
    "prod-sa": "xdr-sa-paloaltonetworks-com",
    "prod-tw": "xdr-tw-paloaltonetworks-com",
    "prod-br": "xdr-br-paloaltonetworks-com",
    "prod-dl": "xdr-dl-paloaltonetworks-com",
  }
  wildfire_url_map = {
    default = "${local.region_short_code}.wildfire.paloaltonetworks.com"
    dev     = "wildfire.paloaltonetworks.com"
    prod-pr = "wildfire.paloaltonetworks.com"
    prod-fa = "fr.wildfire.paloaltonetworks.com"
    prod-fr = "wildfire.gov.paloaltonetworks.com"
    prod-gv = "us-central1.wildfire.il.gov.paloaltonetworks.com"
    prod-qt = "qa.wildfire.paloaltonetworks.com"
    prod-us = "wildfire.paloaltonetworks.com"
  }
  app_proxy_blue_green_target = lower(lookup(var.overrides, "app_proxy_blue_green_target", "blue"))
  is_perf_tenant     = tobool(lookup(var.overrides, "is_perf_tenant", false))
  is_qa_automation_tenant     = lookup(var.overrides, "qa_tenant", false)
  prod_spec                   = tobool(lookup(var.overrides, "prod_spec", false)) || local.is_perf_tenant
  jupyter_fqdn                = "jupyter-${var.external_fqdn}"
  observability_fqdn          = "observability-${var.external_fqdn}"
  api_split                   = lower(lookup(var.overrides, "api_split", false)) || local.megatron_xdr
  enable_cortex_platform      = lookup(var.overrides, "enable_cortex_platform", false)
  crtx_integration_url        = lookup(local.crtx_integration_url_redirect_map, var.viso_env, local.crtx_integration_url_redirect_map.default)
  crtx_integration_url_redirect_map = {
    "default" = "https://integration.crtx.${local.region_short_code}.paloaltonetworks.com",
    "dev"     = "https://integration.crtx-qa2-uat.us.paloaltonetworks.com",
  }
  viso_version = replace(regex("v(\\d+.\\d+)", var.viso_version)[0], ".", "")
}
