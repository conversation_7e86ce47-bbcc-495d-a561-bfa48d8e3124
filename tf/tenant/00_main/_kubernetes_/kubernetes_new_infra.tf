module "kubernetes" {
  source    = "../03_kubernetes_new_infra"
  providers = {
    google.mt = google.mt
  }
  viso_new_platform                            = var.viso_new_platform
  agentconfig_encryption_key                   = var.enc_key
  cronus_deploy_idle_resources                 = local.cronus_deploy_idle_resources
  backend_image_name                           = local.backend_image_name
  bq_output                                    = module.resources_and_permissions.bq_output
  default_byok_key_id                          = var.enable_byok ? module.resources_and_permissions.default_byok_key_id : ""
  sp_jwt_key_id                                = module.resources_and_permissions.sp_jwt_key_id
  buckets_output                               = module.resources_and_permissions.buckets_output
  secret_manager_secrets_output                = module.resources_and_permissions.secret_manager_secrets_output
  certs_project                                = local.certs_project
  cold_retention                               = lower(var.cold_retention)
  collection_enc_key                           = var.collection_enc_key
  docker_image_gar_location                    = local.docker_image_gar_location
  egress_enabled                               = lower(var.egress_enabled)
  enable_asm                                   = var.enable_asm
  enable_byok                                  = var.enable_byok
  enable_twistlock                             = lower(local.enable_twistlock)
  enable_xcloud                                = local.enable_xcloud
  enable_xsoar_shared_components               = local.enable_xsoar_shared_components
  encrypt_fas_keyring_location                 = var.encrypt_fas_keyring_location
  encrypt_fas_keyring_name                     = var.encrypt_fas_keyring_name
  enable_pipeline                              = local.enable_pipeline
  external_fqdn                                = local.use_crtx_domain ? var.external_crtx_fqdn : var.external_fqdn
  firestore_access_service_key                 = var.firestore_access_service_key
  forensics                                    = var.forensics
  host_project                                 = var.host_project
  is_perf_tenant                               = local.is_perf_tenant
  prod_spec                                    = local.prod_spec
  is_fedramp                                   = local.is_fedramp
  is_xpanse                                    = local.is_xpanse
  is_xsoar                                     = local.is_xsoar
  is_xdr                                       = local.is_xdr
  is_xsiam                                     = local.is_xsiam
  lcaas                                        = var.lcaas
  logout_url                                   = local.logout_url
  megatron_xdr                                 = local.megatron_xdr
  multi_project_postfix                        = var.multi_project_postfix
  overrides                                    = var.overrides
  parent_project_id                            = var.parent_project_id
  pool_tenant                                  = var.pool_tenant_creation || var.pool_tenant_activation
  pool_tenant_activation                       = var.pool_tenant_activation
  pool_tenant_creation                         = lower(var.pool_tenant_creation)
  product_type                                 = local.product_type
  product_code                                 = var.product_code
  product_tier                                 = local.product_tier
  project_id                                   = module.bootstrap.project_id
  project_number                               = module.bootstrap.project_number
  project_prefix                               = var.project_prefix
  rbacconf_base_uri                            = local.rbacconf_base_uri
  redis_split                                  = local.redis_split
  region                                       = var.region
  multi_zoned_nodepools                        = local.multi_zoned_nodepools
  report_encryption_key                        = var.report_encryption_key
  service_account_key_output                   = module.resources_and_permissions.service_account_key_output
  slackconf_hydra_redirect                     = local.slackconf_hydra_redirect
  subscriptions_output                         = merge(module.resources_and_permissions.subscriptions_output...)
  tenant_type                                  = var.tenant_type
  terraform_iam                                = module.bootstrap.terraform_iam
  topics_output                                = module.resources_and_permissions.topics_output
  twistlock_defender_cluster_id                = local.twistlock_defender_cluster_id
  twistlock_defender_ws_address                = local.twistlock_defender_ws_address
  viso_env                                     = var.viso_env
  hashed_wildfire_key                          = var.hashed_wildfire_key
  hashed_monitoring_key                        = var.hashed_monitoring_key
  hashed_xdr_http_token                        = var.hashed_xdr_http_token
  wildfire_apikey                              = var.wildfire_apikey
  email_security_wildfire_apikey               = var.email_security_wildfire_apikey
  xcloud_standalone_deployment                 = local.xcloud_standalone_deployment
  xdr_auth_token                               = var.xdr_auth_token
  xdr_http_collection_token                    = var.xdr_http_collection_token
  backend_version                              = local.backend_version
  xdr_env                                      = var.xdr_env
  xdr_id                                       = var.xdr_id
  tb_licenses                                  = local.tb_licenses
  xpanse_asset_feedback_credentials_api_key    = var.xpanse_asset_feedback_credentials_api_key
  xpanse_tenant_credentials_client_id          = var.xpanse_tenant_credentials_client_id
  xpanse_tenant_credentials_client_secret      = var.xpanse_tenant_credentials_client_secret
  zone                                         = var.zone
  customer_dev_tenant                          = local.customer_dev_tenant
  is_xsoar_6_migration                         = var.is_xsoar_6_migration
  is_xsoar_onprem_migration                    = var.is_xsoar_onprem_migration
  xsoar_6_sn                                   = var.xsoar_6_sn
  xsoar_6_account_id                           = var.xsoar_6_account_id
  xsoar_6_env                                  = var.xsoar_6_env
  xsoar_6_migration_token                      = var.xsoar_6_migration_token
  is_metro_tenant                              = var.is_metro_tenant
  metro_host_project_id                        = var.metro_host_project_id
  metro_host_id                                = var.metro_host_id
  metro_host_zone                              = var.metro_host_zone
  metro_tenant_index                           = var.metro_tenant_index
  metro_all_in_one                             = var.metro_all_in_one
  app_images                                   = var.app_images
  enable_cloud_posture                         = var.enable_cloud_posture
  enable_cloud_appsec                          = var.enable_cloud_appsec
  enable_assured_workloads                     = var.enable_assured_workloads
  enable_itdr                                  = local.enable_itdr
  gke_location                                 = local.gke_location
  regional_kubernetes                          = local.regional_kubernetes
  metro_version                                = var.metro_version
}
