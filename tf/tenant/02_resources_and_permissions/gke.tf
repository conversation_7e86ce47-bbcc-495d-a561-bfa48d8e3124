module "create_gke" {
  source                               = "../../modules/gke"
  for_each                             = local.clusters
  default_kms_key_id      = var.enable_byok ? module.create_kms_crypto_key.st_kms_key_id["default_byok_key"] : module.create_kms_crypto_key.st_kms_key_id["gke_application_layer"]
  cluster                 = each.key
  clusters_data           = each.value
  dynamic_node_pools      = each.value.dynamic_node_pools
  enable_byok             = var.enable_byok
  is_fedramp              = var.is_fedramp
  cronus_node_pool        = each.value.cronus_node_pool_spec
  alyx_node_pool          = each.value.alyx_node_pool_spec
  argo_node_pool          = each.value.argo_node_pool_spec
  argo_enhanced_node_pool = each.value.argo_enhanced_node_pool_spec
  scylla_node_pool        = each.value.scylla_node_pool
  scylla_xcloud_node_pool = each.value.scylla_xcloud_node_pool
  xsoar_node_pool         = each.value.xsoar_node_pool
  static_node_pool        = each.value.static_node_pool
  project_id              = var.project_id
  small_epp               = var.small_epp
  viso_env                = var.viso_env
  shielded_nodes          = true
  enable_secure_gke       = lookup(var.overrides, "enable_secure_gke", var.creation_date > local.enable_secure_gke_date)
  enable_gke_backup                    = false #var.viso_env == "prod-pr"
  network_policy_config                = local.enable_cortex_platform
  enable_dataplane_v2                  = lookup(var.overrides, "enable_gke_dataplane_v2", false) && contains(["dev", "prod-pr"], var.viso_env)
  enable_dataplane_v2_metrics          = lookup(var.overrides, "enable_gke_dataplane_v2_metrics", false) && contains(["dev", "prod-pr"], var.viso_env)
  enable_dataplane_v2_relay            = lookup(var.overrides, "enable_gke_dataplane_v2_relay", false) && contains(["dev", "prod-pr"], var.viso_env)
  enable_gke_metering                  = var.enable_gke_metering
  enable_k8s_beta_apis                 = lookup(var.overrides, "enable_k8s_beta_apis", false) || var.enable_k8s_beta_apis
  beta_apis                            = lookup(var.overrides, "beta_apis", ["networking.k8s.io/v1beta1/ipaddresses", "networking.k8s.io/v1beta1/servicecidrs"])
  enable_network_egress_metering       = var.enable_network_egress_metering
  enable_resource_consumption_metering = var.enable_resource_consumption_metering
  bigquery_destination_dataset_id      = "gke_metering_${var.lcaas}"
  enable_cloud_appsec                  = var.enable_cloud_appsec
  enable_cloud_posture                 = var.enable_cloud_posture
}


locals {
  enable_secure_gke_date = var.viso_env == "dev" ? 1731315189000 : var.is_fedramp ? 1740990287000 : 1740989869000 # Enable in production only for new tenants after Batch 4 of version 3.13.
  clusters = var.is_metro_tenant && !var.metro_all_in_one ? {} : {
    xdr = {
      name                  = "cluster-${var.lcaas}"
      initial_node_count    = local.initial_node_count
      autoscaling_profile   = local.autoscaling_profile
      node_autoprovisioning = local.node_autoprovisioning
      location              = var.gke_location
      project               = var.project_id
      min_master_version    = "1.33"
      node_locations        = local.node_locations
      network               = var.project_network
      subnetwork            = var.project_subnetwork
      cidr_blocks           = concat(local.global_authorized_networks, local.environment_networks[var.xdr_env], local.xsoar_ci_tests_networks, (lookup(var.overrides, "allow_xdr_gitlab_networks", false) || lookup(var.overrides, "qa_tenant", false)) ? local.xdr_gitlab_networks : [])
      machine_type          = local.default_instance_type
      service_account       = local.proxy_service_account_email
      cronus_node_pool_spec = [
        {
          enabled         = var.cronus_standalone
          key_name        = "cronus-nodepool"
          name            = "cronus-${local.gke_version}"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = var.cronus_node_count
          machine_type    = lookup(var.overrides, "cronus_node_pool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
#          max_pods_per_node = 16

          autoscaling = [
            {
              min_node_count = lookup(var.overrides, "cronus_node_pool_min_nodes", 1)
              max_node_count = lookup(var.overrides, "cronus_max_node_count", 150)
            }
          ]

          labels = {
            xdr-pool = "cronus"
          }

          taint = [
            {
              effect = "NO_SCHEDULE",
              key    = "xdr-pool",
              value  = "cronus",
            }
          ]
        }
      ]
      argo_node_pool_spec = [
        {
          enabled         = var.metro_all_in_one || var.enable_cloud_appsec || var.enable_cloud_posture
          key_name        = "argo-nodepool"
          name            = local.enable_max_pods_per_node ?  "argo-1-${local.gke_version}" : "argo-${local.gke_version}"
          node_disk_size  = lookup(var.overrides, "argo_disk_size", "50")
          disk_type       = lookup(var.overrides, "argo_disk_type", "pd-ssd")
          node_count      = lookup(var.overrides, "argo_node_count", 0)
          machine_type    = lookup(var.overrides, "argo_machine_type", "e2-standard-8")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          image_streaming = true
          name            = local.enable_max_pods_per_node ?  "argo-1-${local.gke_version}" : "argo-${local.gke_version}"
          max_pods_per_node = local.enable_max_pods_per_node ? 16 : null
          autoscaling = [
            {
              min_node_count = lookup(var.overrides, "argo_enhanced_min_nodes", 0)
              max_node_count = lookup(var.overrides, "argo_enhanced_max_nodes", local.dynamic_max_node_count)
            }
          ]

          labels = {
            xdr-pool = "argo-nodepool"
          }

          taint = [
            {
              effect = "NO_SCHEDULE",
              key    = "xdr-pool",
              value  = "argo-nodepool",
            }
          ]
        }
      ]
      argo_enhanced_node_pool_spec = [
        {
          enabled         = lookup(var.overrides, "enable_argo_enhanced", false)
          key_name        = "argo-enhanced-nodepool"
          name            = local.enable_max_pods_per_node ?  "argo-enhanced-1-${local.gke_version}" : "argo-enhanced-${local.gke_version}"
          node_disk_size  = lookup(var.overrides, "argo_enhanced_disk_size", "400")
          disk_type       = lookup(var.overrides, "argo_enhanced_disk_type", "hyperdisk-balanced")
          node_count      = lookup(var.overrides, "argo_enhanced_node_count", 1)
          machine_type    = lookup(var.overrides, "argo_enhanced_machine_type", "c4-standard-16")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          name            = local.enable_max_pods_per_node ?  "argo-enhanced-1-${local.gke_version}" : "argo-enhanced-${local.gke_version}"

          max_pods_per_node = local.enable_max_pods_per_node ? 16 : null
          autoscaling = [
            {
              min_node_count = lookup(var.overrides, "argo_enhanced_min_nodes", 1)
              max_node_count = lookup(var.overrides, "argo_enhanced_max_nodes", 200)
            }
          ]

          labels = {
            xdr-pool = "argo-enhanced"
          }

          taint = [
            {
              effect = "NO_SCHEDULE",
              key    = "xdr-pool",
              value  = "argo-enhanced",
            }
          ]
        }
      ]
      alyx_node_pool_spec = [        {
          enabled         = !var.is_xsoar && !var.small_epp
          name            = "alyx-${local.gke_version}"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          machine_type    = lookup(var.overrides, "alyx_node_pool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          max_pods_per_node = local.enable_max_pods_per_node ? 16 : null
          autoscaling = [
            {
              min_node_count = lookup(var.overrides, "alyx_node_pool_min_nodes", var.enable_alyx ? 1 : 0)
              max_node_count = 150
            }
          ]

          labels = {
            xdr-pool = "alyx"
          }

          taint = [
            {
              effect = "NO_SCHEDULE",
              key    = "xdr-pool",
              value  = "alyx",
            }
          ]
        }
      ]
      dynamic_node_pools = [
        {
        enabled         = !var.small_epp
        key_name        = "dynamic-nodepool"
        name            = local.enable_max_pods_per_node ? "wi-dynamic-nodepool-1-${local.gke_version}" : "wi-dynamic-nodepool-${local.gke_version}"
        node_disk_size  = "50"
        disk_type       = lookup(var.overrides, "dynamic_nodepool_disk_type", "pd-standard")
        node_count      = local.initial_dynamic_node_count
        machine_type    = lookup(var.overrides, "dynamic_nodepool_machine_type", var.small_epp ? "e2-standard-2" : "e2-standard-8")
        service_account = local.proxy_service_account_email
        cluster         = "cluster-${var.lcaas}"
        max_parallel_image_pulls = 5
        image_streaming = true
        max_pods_per_node = local.enable_max_pods_per_node ? 64 : null
        autoscaling = var.pool_tenant_creation ? [] : [{
          max_node_count = local.dynamic_max_node_count
          min_node_count = "1"
        }]
        upgrade_settings = [
          {
            max_surge = 10
            max_unavailable = 10
          }
        ]
        labels = { xdr-pool = "wi-dynamic" }
        },
        {
        enabled         = lookup(var.overrides, "additional_dynamic_nodepool", false)
        key_name        = "dynamic-nodepool-2"
        name            = local.enable_max_pods_per_node ? "wi-dynamic-nodepool-02-${local.gke_version}" : "wi-dynamic-nodepool-2-${local.gke_version}"
        node_disk_size  = "50"
        node_count      = local.initial_dynamic_node_count
        machine_type    = lookup(var.overrides, "additional_dynamic_nodepool_machine_type", "e2-standard-32")
        service_account = local.proxy_service_account_email
        cluster         = "cluster-${var.lcaas}"
        max_parallel_image_pulls = 5
        image_streaming = true
        max_pods_per_node = local.enable_max_pods_per_node ? 64 : null
        autoscaling = [{
          max_node_count = local.dynamic2_max_node_count
          min_node_count =  "1"
        }]
        upgrade_settings = [
          {
            max_surge = 10
            max_unavailable = 10
          }
        ]
        labels = { xdr-pool = "wi-dynamic" }
        },
        {
        enabled         = lookup(var.overrides, "additional_detection_engine_nodepool", false)
        key_name        = "detection-engine-nodepool"
        name            = local.enable_max_pods_per_node ? "wi-detection-engine-nodepool-1-${local.gke_version}" : "wi-detection-engine-nodepool-${local.gke_version}"
        node_disk_size  = "50"
        node_count      = local.initial_dynamic_node_count
        machine_type    = lookup(var.overrides, "additional_detection_engine_nodepool_machine_type", "e2-standard-16")
        service_account = local.proxy_service_account_email
        cluster         = "cluster-${var.lcaas}"
        max_pods_per_node = local.enable_max_pods_per_node ? 64 : null
        autoscaling = [{
          max_node_count = local.dynamic_max_node_count
          min_node_count = max(local.initial_dynamic_node_count, 1)
        }]
        labels = { xdr-pool = "wi-detection-engine" }
        taint  = [{ effect = "NO_SCHEDULE", key = "xdr-pool", value = "wi-detection-engine" }]
        }
      ]
      scylla_node_pool = [
        {
          enabled         = lower(!var.pool_tenant_creation && var.enable_scylla && var.scylla_standalone && var.scylla_blue_nodepool)
          name            = "scylladb-nodepool-${local.lcaas}-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = local.initial_scylla_node_count
          machine_type    = lookup(var.overrides, "scylla_blue_nodepool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla" }]
          }, {
          enabled         = lower(lookup(var.overrides, "scylla_migration", false))
          name            = "scylla-nodepool-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = local.initial_scylla_node_count
          machine_type    = var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylla-cluster" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-cluster" }]
          }, {
          enabled         = lower(var.scylla_green_nodepool)
          name            = "scylla-nodepool-${local.lcaas}-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = "0" # todo: local.initial_scylla_node_count
          machine_type    = lookup(var.overrides, "scylla_green_nodepool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla" }]
          }, {
          enabled         = lower(var.enable_scylla && var.enable_scylla_iplen)
          name            = "ipl-scylla-enrichment-${local.lcaas}-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = var.scylla_iplen_nodes_count
          machine_type    = lookup(var.overrides, "scylla_iplen_nodepool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb-iplen" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-iplen" }]
          }, {
          enabled         = lower(var.enable_scylla && var.enable_scylla_iplen && var.ipl_scylla_blue_nodepool)
          name            = var.ipl_scylla_blue_nodepool ? "iplen-scylla-blue-${local.lcaas}-1" : "ipl-scylla-enrichment-${local.lcaas}-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = 0
          machine_type    = lookup(var.overrides, "scylla_iplen_nodepool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb-iplen" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-iplen" }]
          }, {
          enabled         = lower(var.enable_scylla && var.enable_scylla_iplen && var.ipl_scylla_green_nodepool)
          name            = "iplen-scylla-green-${local.lcaas}-1"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = 0
          machine_type    = lookup(var.overrides, "scylla_iplen_nodepool_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb-iplen" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-iplen" }]
      }]
      scylla_xcloud_node_pool = [{
          enabled = lower(var.xcloud_standalone_deployment)
          name            = local.xcloud_node_name
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = local.initial_scylla_xcloud_node_count
          machine_type    = var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels = { xdr-pool = "scylladb-xcloud" }
          taint = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-xcloud" }]
        }, {
          enabled         = lower(var.xcloud_standalone_deployment && var.xcloud_scylla_blue_nodepool)
          name            = var.xcloud_scylla_blue_nodepool ? "xcloud-scylla-blue-${local.lcaas}-1" : local.xcloud_node_name
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = 0
          machine_type    = var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels          = { xdr-pool = "scylladb-xcloud" }
          taint           = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-xcloud" }]
        }, {
          enabled = lower(var.xcloud_standalone_deployment && var.xcloud_scylla_green_nodepool)
          name            = var.xcloud_scylla_green_nodepool ? "xcloud-scylla-green-${local.lcaas}-1" : local.xcloud_node_name
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = 0
          machine_type    = var.viso_env == "dev" && !var.prod_spec ? "e2-standard-4" : "e2-highmem-4"
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          node_metadata   = "GCE_METADATA"
          labels = { xdr-pool = "scylladb-xcloud" }
          taint = [{ effect = "NO_SCHEDULE", key = "role", value = "scylla-xcloud" }]
        }]
      xsoar_node_pool = [
        {
          enabled         = var.enable_xsoar_shared_components && var.viso_env != "dev" && var.is_xsoar_legacy_spec
          name            = local.enable_max_pods_per_node ? "xsoar-nodepool-1-${local.gke_version}" :  "xsoar-nodepool-${local.gke_version}"
          key_name        = "xsoar-nodepool"
          node_disk_size  = "20"
          disk_type       = "pd-ssd"
          node_count      = local.initial_xsoar_node_count
          machine_type    = lookup(var.overrides, "xsoar_machine_type", var.viso_env == "dev" && !var.prod_spec ? "e2-standard-2" : "e2-standard-8")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          max_pods_per_node = local.enable_max_pods_per_node ? 64 : null
          labels          = { xdr-pool = "xsoar" }
          taint           = [{ effect = "NO_SCHEDULE", key = "xdr-pool", value = "xsoar" }]
        },
        {
          enabled         = (var.is_xsoar_onprem_migration && var.xsoar_6_mig_size != "micro") || lookup(var.overrides, "enable_xsoar_migration_node", false)
          name            = "xsoar-migration-nodepool-1-${local.gke_version}"
          key_name        = "xsoar-migration-nodepool"
          node_disk_size  = lookup(var.overrides, "xsoar_migration_nodepool_disk_size", "20")
          disk_type       = "pd-ssd"
          node_count      = 1
          machine_type    = local.mig_node_machine_type
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          max_pods_per_node = local.enable_max_pods_per_node ? 64 : null
          labels          = { xdr-pool = "xsoar-migration" }
          taint           = [{ effect = "NO_SCHEDULE", key = "xdr-pool", value = "xsoar-migration" }]
      }]
      static_node_pool = [
        {
          enabled    = true
          key_name   = "static-nodepool"
          name       = "wi-static-nodepool-${local.gke_version}"
          node_count = lookup(var.overrides, "static_nodepool_node_count", local.initial_static_node_count)
          autoscaling = local.static_autoscaling
          machine_type = (lookup(var.overrides, "static_machine_type",
              # Platform production logic
              var.viso_env == "prod-pr" && var.tenant_type != "internal" ? "e2-standard-32" :

              # Pre-platform logic
              (local.mysql_dev_spec_enabled ? "e2-highmem-4" :
                           var.megatron_xdr ? "n2-highmem-32" :
                             var.big_tenant ? "e2-highmem-16" :
                              var.small_epp ? "e2-highmem-2" :
                                              "e2-highmem-8"
              )
            )
          )
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          max_parallel_image_pulls = 5
          image_streaming = true
          labels          = { xdr-pool = "wi-static" }
          taint           = [{ effect = "PREFER_NO_SCHEDULE", key = "xdr-pool", value = "wi-static" }]
          # will be PREFER_NO_SCHEDULE in version 3.16 due to this race condition:
          #  - singlestore and scylla clusters will get into Pending state because of the NoSchedule
          #    taint, but won't apply the new tolerations because the cluster is in Pending state.
          # after the new tolerations gets into effect in all prod, we can safely change to
          # NO_SCHEDULE in 3.17
        },
        {
          enabled         = var.rocksdb_standalone
          key_name        = "rocksdb-nodepool"
          name            = local.enable_max_pods_per_node ?  "rocksdb-nodepool-1-${local.gke_version}" : "rocksdb-nodepool-${local.gke_version}"
          node_count      = lookup(var.overrides, "rocksdb_nodepool_node_count", local.initial_rocksdb_node_count)
          machine_type    = lookup(var.overrides, "rocksdb_machine_type", "e2-standard-16")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          max_pods_per_node = local.enable_max_pods_per_node ? 32 : null
          autoscaling = [{
            max_node_count = lookup(var.overrides, "rocksdb_max_node_count", "10")
            min_node_count = "1"
          }]
          labels = { xdr-pool = "rocksdb" }
          taint  = [{ effect = "NO_SCHEDULE", key = "xdr-pool", value = "rocksdb" }]
        },
        {
          enabled         = var.elasticsearch_standalone
          name            = "elasticsearch-nodepool-${local.gke_version}"
          key_name        = "elasticsearch-nodepool"
          node_count      = 1
          machine_type    = lookup(var.overrides, "elasticsearch_machine_type", "e2-standard-16")
          service_account = local.proxy_service_account_email
          cluster         = "cluster-${var.lcaas}"
          labels          = { xdr-pool = "wi-elasticsearch" }
        },
        {
          enabled         = local.enable_cortex_platform && var.viso_env != "dev"
          key_name        = "singlestore-nodepool"
          name            = local.enable_max_pods_per_node ?  "singlestore-nodepool-1-${local.gke_version}" : "singlestore-nodepool-${local.gke_version}"
          machine_type    = (lookup(var.overrides, "static_machine_type",
              # Platform production logic - using same logic as static nodepool
              var.viso_env == "prod-pr" && var.tenant_type != "internal" ? "e2-standard-32" :
              # Pre-platform logic
              (local.mysql_dev_spec_enabled ? "e2-highmem-4" :
                           var.megatron_xdr ? "n2-highmem-32" :
                             var.big_tenant ? "e2-highmem-16" :
                              var.small_epp ? "e2-highmem-2" :
                                              "e2-highmem-8"
              )
            )
          )
          service_account   = local.proxy_service_account_email
          cluster           = "cluster-${var.lcaas}"
          max_pods_per_node = local.enable_max_pods_per_node ? 32 : null
          autoscaling = [
            {
              max_node_count = lookup(var.overrides, "singlestore_max_node_count", "10")
              min_node_count = "1"
            }
          ]
          labels = {
            xdr-pool = "singlestore"
          }
          taint = [
            {
              effect = "NO_SCHEDULE",
              key    = "xdr-pool",
              value  = "singlestore"
            }
          ]
        },
      ]
    }
  }
  is_static_autoscaling            = ! var.pool_tenant_creation
  scylla_node                      = var.pool_tenant_creation ? 0 : lower(var.enable_scylla) && !var.scylla_standalone ? 0 : lower(var.enable_scylla) ? var.scylla_nodes_count : 0
  initial_xsoar_node_count         = var.pool_tenant_creation ? 0 : 1
  initial_rocksdb_node_count       = var.rocksdb_standalone ? 1 : 0
  initial_dynamic_node_count       = var.pool_tenant_creation || lower(var.small_epp) ? 0 : local.enable_cortex_platform ? 3 : 1
  initial_scylla_node_count        = var.regional_kubernetes ? ceil(local.scylla_node / (var.kube_num_extra_zones + 1)) : local.scylla_node  # for regional clusters, this count is PER ZONE
  initial_scylla_xcloud_node_count = lower(var.enable_xcloud) ? 1 : 0
  xsoar_initial_static_node_count  = var.redis_split ? 3 : 2
  _initial_static_node_count = var.pool_tenant_creation ? 0 : (
    local.is_static_autoscaling ? 1
    : var.enable_xsoar_shared_components && (var.viso_env != "dev" || var.prod_spec) ? local.xsoar_initial_static_node_count
    : var.pro_agents > 20000 || (var.viso_env == "dev" && (var.mysql_prod_spec_override || var.prod_spec)) ? 2
    : 1
  ) + (var.redis_split ? 1 : 0) + (var.megatron_xsoar || (var.cronus_deploy_idle_resources && !var.cronus_standalone) ? 1 : 0)
  initial_static_node_count = var.regional_kubernetes && !local.is_static_autoscaling ? (
    ceil(local._initial_static_node_count / (var.kube_num_extra_zones + 1))  # for regional clusters, this count is PER ZONE
  ) : local._initial_static_node_count
  _max_static_node_count           = var.pool_tenant_creation ? 0 : local.enable_cortex_platform ? 6 : 3
  max_static_node_count            = var.regional_kubernetes ? ceil(local._max_static_node_count / (var.kube_num_extra_zones + 1)) : local._max_static_node_count  # for regional clusters, this count is PER ZONE
  initial_sum_node_count           = local.initial_static_node_count + local.initial_dynamic_node_count + local.initial_scylla_node_count
  initial_zonal_node_count         = var.regional_kubernetes ? local.initial_sum_node_count * (var.kube_num_extra_zones + 1) : local.initial_sum_node_count
  initial_node_count               = 11
  lcaas                            = length(var.lcaas) > 17 ? substr(var.lcaas, 0, 16) : var.lcaas
  xcloud_node_name                 = length("scylladb-xcloud-nodepool-${local.lcaas}-1") > 40 ? "scylladb-xcloud-nodepool-1" : "scylladb-xcloud-nodepool-${local.lcaas}-1"
  max_static_node_count_overriden  = lookup(var.overrides, "static_nodepool_node_count", local.max_static_node_count)
  static_autoscaling               = local.is_static_autoscaling ? [{
    max_node_count = local.max_static_node_count_overriden
    min_node_count = min(local.initial_static_node_count, local.max_static_node_count_overriden)
  }] : []
  dynamic_max_node_count = lookup(
    var.overrides,
    "dynamic_max_node_count",
    var.is_xsoar && var.viso_env == "dev" && !var.prod_spec ? 2
    : var.is_xsiam && var.viso_env == "dev" ? 20
    : var.viso_env == "dev" && !var.prod_spec ? 6
    : var.is_xsoar && var.megatron_xsoar ? 16
    : var.is_xsoar ? 12
    : min(
      max(
        # we want a minimum nodepool max to make sure oncall isn't alerted unnecessarily to increase this
        200,

        # node for each 350 agents or ~2.3 TB/Month
        ceil(var.dml_scale_base / 350) + 13
      ),

      # max out the nodepool at 400
      400
    )
  )
  dynamic2_max_node_count = lookup(
    var.overrides,
    "dynamic2_max_node_count",
    local.dynamic_max_node_count
  )
  environment_networks = {
    default = [],
    QA4-GCP = [
      {
        cidr_block   = "104.198.25.192/32"
        display_name = "orchestrator-dev-fw01"
      },
      {
        cidr_block   = "35.184.172.150/32"
        display_name = "orchestrator-dev-fw02"
      },
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-dev-nat-0"
      },
      {
        cidr_block   = "35.224.157.142/32"
        display_name = "orchestrator-dev-nat-1"
      },
      {
        cidr_block   = "34.66.50.48/32"
        display_name = "orchestrator-dev-nat-2"
      },
      {
        cidr_block   = "34.66.162.118/32"
        display_name = "orchestrator-dev-nat-3"
      },
      {
        cidr_block   = "35.239.112.25/32"
        display_name = "orchestrator-dev-nat-4"
      },
      {
        cidr_block   = "34.123.113.8/32"
        display_name = "xdr-remediation-dev-01-fw01"
      },
      {
        cidr_block   = "104.197.255.68/32"
        display_name = "xdr-remediation-dev-01-fw02"
      },
    ],
    QA2-GCP = [
      {
        cidr_block   = "104.198.25.192/32"
        display_name = "orchestrator-dev-fw01"
      },
      {
        cidr_block   = "35.184.172.150/32"
        display_name = "orchestrator-dev-fw02"
      },
      {
        cidr_block   = "34.69.217.32/32"
        display_name = "orchestrator-dev-nat-0"
      },
      {
        cidr_block   = "35.224.157.142/32"
        display_name = "orchestrator-dev-nat-1"
      },
      {
        cidr_block   = "34.66.50.48/32"
        display_name = "orchestrator-dev-nat-2"
      },
      {
        cidr_block   = "34.66.162.118/32"
        display_name = "orchestrator-dev-nat-3"
      },
      {
        cidr_block   = "35.239.112.25/32"
        display_name = "orchestrator-dev-nat-4"
      },
      {
        cidr_block   = "34.123.113.8/32"
        display_name = "xdr-remediation-dev-01-fw01"
      },
      {
        cidr_block   = "104.197.255.68/32"
        display_name = "xdr-remediation-dev-01-fw02"
      },
      {
        cidr_block   = "34.90.250.227/32"
        display_name = "xdr-qa-auto-dev-fw01"
      },
      {
        cidr_block   = "35.204.44.149/32"
        display_name = "xdr-qa-auto-dev-fw02"
      },
      {
        cidr_block   = "34.91.163.9/32"
        display_name = "xdr-gitlab-fw1"
      },
      {
        cidr_block   = "34.141.236.81/32"
        display_name = "xdr-gitlab-fw2"
      },
    ],
    PROD-US = [
      {
        cidr_block   = "104.198.71.154/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-us1"
      },
      {
        cidr_block   = "35.202.111.149/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-us2"
      },
      {
        cidr_block   = "34.122.135.75/32"
        display_name = "orchestrator-nat-prod-us3"
      },
      {
        cidr_block   = "35.222.70.185/32"
        display_name = "orchestrator-nat-prod-us4"
      },
      {
        cidr_block   = "34.66.117.27/32"
        display_name = "orchestrator-fw-prod-us1"
      },
      {
        cidr_block   = "34.71.188.91/32"
        display_name = "orchestrator-fw-prod-us2"
      },
      {
        cidr_block   = "35.222.151.94/32"
        display_name = "orchestrator-fw-prod-us3"
      },
      {
        cidr_block   = "104.154.234.216/32"
        display_name = "orchestrator-fw-prod-us4"
      },
    ],
    PROD-EU = [
      {
        cidr_block   = "35.204.123.213/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-eu1"
      },
      {
        cidr_block   = "34.90.85.150/32" // remove if XDR-20305 is finished
        display_name = "orchestrator-nat-prod-eu2"
      },
      {
        cidr_block   = "34.90.190.139/32"
        display_name = "orchestrator-nat-prod-eu3"
      },
      {
        cidr_block   = "34.34.41.237/32"
        display_name = "orchestrator-nat-prod-eu4"
      },
      {
        cidr_block   = "34.90.230.10/32"
        display_name = "orchestrator-fw-prod-eu1"
      },
      {
        cidr_block   = "34.91.45.138/32"
        display_name = "orchestrator-fw-prod-eu2"
      },
    ],
    PROD-UK = [
      {
        cidr_block   = "35.242.137.26/32"
        display_name = "orchestrator-fw-prod-uk1"
      },
      {
        cidr_block   = "34.89.83.194/32"
        display_name = "orchestrator-fw-prod-uk2"
      },
    ],
    PROD-SG = [
      {
        cidr_block   = "34.87.3.85/32"
        display_name = "orchestrator-fw-prod-sg1"
      },
      {
        cidr_block   = "34.87.44.205/32"
        display_name = "orchestrator-fw-prod-sg2"
      },
    ],
    PROD-JP = [
      {
        cidr_block   = "34.84.156.231/32"
        display_name = "orchestrator-fw-prod-jp1"
      },
      {
        cidr_block   = "34.84.47.120/32"
        display_name = "orchestrator-fw-prod-jp2"
      },
    ],
    PROD-CA = [
      {
        cidr_block   = "34.95.56.222/32"
        display_name = "orchestrator-fw-prod-ca1"
      },
      {
        cidr_block   = "34.95.6.224/32"
        display_name = "orchestrator-fw-prod-ca2"
      },
    ],
    PROD-CH = [
      {
        cidr_block   = "34.65.240.75/32"
        display_name = "orchestrator-fw-prod-ch1"
      },
      {
        cidr_block   = "34.65.3.57/32"
        display_name = "orchestrator-fw-prod-ch2"
      },
    ],
    PROD-AU = [
      {
        cidr_block   = "34.87.247.130/32"
        display_name = "orchestrator-fw-prod-au1"
      },
      {
        cidr_block   = "35.244.122.206/32"
        display_name = "orchestrator-fw-prod-au2"
      },
    ],
    PROD-DE = [
      {
        cidr_block   = "35.198.109.115/32"
        display_name = "orchestrator-fw-prod-de1"
      },
      {
        cidr_block   = "35.198.91.31/32"
        display_name = "orchestrator-fw-prod-de2"
      },
    ],
    PROD-IN = [
      {
        cidr_block   = "34.93.243.162/32"
        display_name = "orchestrator-fw-prod-in1"
      },
      {
        cidr_block   = "34.93.232.128/32"
        display_name = "orchestrator-fw-prod-in2"
      }
    ],
    PROD-PL = [
      {
        cidr_block   = "34.118.37.220/32"
        display_name = "orchestrator-fw-prod-pl1"
      },
      {
        cidr_block   = "34.118.31.166/32"
        display_name = "orchestrator-fw-prod-pl2"
      },
    ],
    PROD-QT = [
      {
        cidr_block   = "34.18.43.152/32"
        display_name = "orchestrator-fw-prod-qt1"
      },
      {
        cidr_block   = "34.18.19.202/32"
        display_name = "orchestrator-fw-prod-qt2"
      },
    ],
    PROD-TW = [
      {
        cidr_block   = "35.221.248.85/32"
        display_name = "orchestrator-fw-prod-tw1"
      },
      {
        cidr_block   = "34.80.10.47/32"
        display_name = "orchestrator-fw-prod-tw2"
      },
    ],
    PROD-FA = [
      {
        cidr_block   = "34.163.116.217/32"
        display_name = "orchestrator-fw-prod-fa1"
      },
      {
        cidr_block   = "34.163.127.73/32"
        display_name = "orchestrator-fw-prod-fa2"
      },
    ],
    PROD-IL = [
      {
        cidr_block   = "34.165.155.7/32"
        display_name = "orchestrator-fw-prod-il1"
      },
      {
        cidr_block   = "34.165.23.18/32"
        display_name = "orchestrator-fw-prod-il2"
      },
    ],
    PROD-ID = [
      {
        cidr_block   = "34.101.43.58/32"
        display_name = "orchestrator-fw-prod-id1"
      },
      {
        cidr_block   = "34.101.43.214/32"
        display_name = "orchestrator-fw-prod-id2"
      },
    ],
    PROD-SA = [
      {
        cidr_block   = "34.166.32.224/32"
        display_name = "orchestrator-fw-prod-sa1"
      },
      {
        cidr_block   = "34.166.61.154/32"
        display_name = "orchestrator-fw-prod-sa2"
      },
    ],
    PROD-ES = [
      {
        cidr_block   = "34.175.16.125/32"
        display_name = "orchestrator-fw-prod-es1"
      },
      {
        cidr_block   = "34.175.7.162/32"
        display_name = "orchestrator-fw-prod-es2"
      },
    ],
    PROD-PR = [
      {
        cidr_block   = "34.41.24.134/32"
        display_name = "orchestrator-fw-prod-pr1"
      },
      {
        cidr_block   = "34.135.191.119/32"
        display_name = "orchestrator-fw-prod-pr2"
      },
    ],
    PROD-IT = [
      {
        cidr_block   = "34.154.16.56/32"
        display_name = "orchestrator-fw-prod-it1"
      },
      {
        cidr_block   = "34.154.132.40/32"
        display_name = "orchestrator-fw-prod-it2"
      },
    ],
    PROD-KR = [
      {
        cidr_block = "34.64.85.33/32"
        display_name = "orchestrator-fw-prod-kr1"
      },
      {
        cidr_block = "34.47.68.85/32"
        display_name = "orchestrator-fw-prod-kr2"
      },
    ],
    PROD-ZA = [
      {
        cidr_block = "34.35.12.15/32"
        display_name = "orchestrator-fw-prod-za1"
      },
      {
        cidr_block = "34.35.30.50/32"
        display_name = "orchestrator-fw-prod-za2"
      },
    ],
    PROD-BR = [
      {
        cidr_block   = "34.95.206.191/32"
        display_name = "orchestrator-fw-prod-br1"
      },
      {
        cidr_block   = "34.95.218.141/32"
        display_name = "orchestrator-fw-prod-br2"
      },
    ],
    PROD-DL = [
      {
        cidr_block   = "34.131.74.221/32"
        display_name = "orchestrator-fw-prod-dl1"
      },
      {
        cidr_block   = "34.131.6.82/32"
        display_name = "orchestrator-fw-prod-dl2"
      },
    ],
    PROD-FR = [
      {
        cidr_block   = "34.71.25.114/32"
        display_name = "orchestrator-fw-prod-fr"
      },
      {
        cidr_block   = "35.224.29.195/32"
        display_name = "orchestrator-fw-prod-fr2"
      },
    ],
    PROD-GV = [
      {
        cidr_block   = "34.135.155.135/32"
        display_name = "orchestrator-fw-prod-gv"
      },
      {
        cidr_block   = "130.211.194.24/32"
        display_name = "orchestrator-fw-prod-gv2"
      },
    ],
  }
  content_tenants_list = ["9994609783268", "9991511958796", "9994089118139", "9997333835008", "9994443226862", "9997461765391", "9994671965229", "9991850898712", "9992101943618", "9992169942140", "9993230017659", "9997261671503", "9995539596782", "9997873681207"]
  xsoar_ci_tests_networks = contains(local.content_tenants_list, var.lcaas) ? [{
    cidr_block   = "34.122.40.62/32"
    display_name = "jarvis-xsoar-ci-1"
    }, {
    cidr_block   = "34.68.163.104/32"
    display_name = "jarvis-xsoar-ci-2"
    }, {
    cidr_block   = "34.123.47.11/32"
    display_name = "jarvis-xsoar-ci-3"
    }, {
    cidr_block   = "35.239.145.78/32"
    display_name = "jarvis-xsoar-ci-4"
    }, {
    cidr_block   = "34.147.80.217/32"
    display_name = "jarvis-xsoar-ci-europe" }] : [{
    cidr_block   = "193.110.180.2/32"
    display_name = "GP-ISR"
  }]
  xdr_gitlab_networks = [
    {
      cidr_block   = "34.91.163.9/32"
      display_name = "xdr-gitlab-fw1"
      }, {
      cidr_block   = "34.141.236.81/32"
      display_name = "xdr-gitlab-fw2"
    }
  ]
  global_authorized_networks = [
    {
      cidr_block   = "193.110.180.2/32"
      display_name = "GP-ISR"
    },
    {
      cidr_block   = "199.167.52.5/32"
      display_name = "GP-USA-CA"
    },
    {
      cidr_block   = "35.239.69.227/32"
      display_name = "jenkins-xdr-mgmt-us-fw1"
    },
    {
      cidr_block   = "35.184.108.54/32"
      display_name = "jenkins-xdr-mgmt-us-fw2"
    },
    {
      cidr_block   = "34.91.65.156/32"
      display_name = "jenkins-xdr-mgmt-eu-fw1"
    },
    {
      cidr_block   = "34.91.88.236/32"
      display_name = "jenkins-xdr-mgmt-eu-fw2"
    },
    {
      cidr_block   = "35.184.108.54/32"
      display_name = "Jenkins-FW2"
    },
    {
      cidr_block   = "35.232.169.130/32"
      display_name = "backend_mgmt1"
    },
    {
      cidr_block   = "35.226.23.214/32"
      display_name = "backend_mgmt2"
    },
    {
      cidr_block   = "74.217.90.10/32"
      display_name = "dc10_stg4"
    },
    {
      cidr_block   = "65.154.226.10/32"
      display_name = "DL1"
    },
    {
      cidr_block   = "154.59.126.10/32"
      display_name = "AM6"
    },
    {
      cidr_block   = "34.100.71.242/32"
      display_name = "GP-USA-NE"
    },
    {
      cidr_block   = "34.91.52.250/32"
      display_name = "xdr-jumpbox-fw1"
    },
    {
      cidr_block   = "34.91.57.245/32"
      display_name = "xdr-jumpbox-fw2"
    },
    {
      cidr_block   = "34.141.153.182/32"
      display_name = "xdr-secure-access-fw1"
    },
    {
      cidr_block   = "34.147.118.191/32"
      display_name = "xdr-secure-access-fw2"
    },
    {
      cidr_block   = "130.41.219.134/32"
      display_name = "PrismaAccess_Israel_gw_1"
    },
    {
      cidr_block   = "128.177.26.193/32"
      display_name = "GP-RESTON-GW"
    },
    {
      cidr_block   = "137.83.220.243/32"
      display_name = "PANGP-US-Northeast"
    },
    {
      cidr_block   = "137.83.249.90/32"
      display_name = "PANGP-US-Southeast"
    },
    {
      cidr_block   = "137.83.193.1/32"
      display_name = "PANGP-US-West"
    },
    {
      cidr_block   = "137.83.220.240/32"
      display_name = "PANGP-US-Northeast2"
    },
    {
      cidr_block   = "34.91.146.190/32"
      display_name = "jenkins-shared-runners-fw1"
    },
    {
      cidr_block   = "34.90.22.183/32"
      display_name = "jenkins-shared-runners-fw2"
    }
  ]
}

resource "terraform_data" "scale_gke_kube_dns" {
  depends_on = [module.create_gke]
  count      = var.pool_tenant_creation || (var.is_metro_tenant && !var.metro_all_in_one) ? 0 : 1
  triggers_replace = {
    enable_custom_kube_dns = timestamp() # timestamp() makes sure this is triggered on every run
  }
  provisioner "local-exec" {
    command = "python ${path.module}/files/scale_gke_kube_dns.py ${var.project_id} cluster-${var.lcaas} ${var.gke_location} ${local.enable_custom_kube_dns}"
  }
}
