variable "gke_location" {}
variable "backend_version" {}
variable "enable_k8s_beta_apis" {}
variable "bq_location" {}
variable "consul_datacenter" {}
variable "compute_zones_names" {}
variable "dml_scale_base" {}
variable "edr_retention_ds" { default = "edr_retention_ds" }
variable "egress_enabled" { default = false }
variable "product_code" {}
variable "enable_asm" {
  type    = bool
  default = false
}
variable "enable_byok" {
  type = bool
}
variable "enable_pipeline" { type = bool }
variable "enable_assured_workloads" { default = false }

variable "cronus_deploy_idle_resources" {
  type = bool
}
variable "cronus_standalone" {
  type = bool
}
variable "cronus_node_count" {
  type = number
}
variable "cold_retention" {}
variable "rocksdb_standalone" { default = false }
variable "enable_scylla" { default = false }
variable "enable_replicator" {}
variable "enable_xcloud" { default = false }
variable "encrypt_fas_keyring_location" { default = "us-central1" }
variable "encrypt_fas_keyring_name" { default = "tenants" }
variable "external_fqdn" {}
variable "app_proxy_blue_green_target" {
  type = string
}
variable "forensics" { default = false }
variable "jupyter_fqdn" {}
variable "observability_fqdn" {}
variable "host_project" {}
variable "host_project_subnetwork_name" {}
variable "prod_spec" {}
variable "is_xdr" { type = bool }
variable "is_xsiam" {}
variable "is_xsoar" {}
variable "is_xpanse" {}
variable "is_xsoar_6_migration" { default = false}
variable "is_xsoar_onprem_migration" { default = false}
variable "tb_licenses" {}
variable "research_automation_service_account" {
  description = "Email of the research automation service account"
  type        = string
  default     = "<EMAIL>"
}
variable "analytics_content_runner_sa" {
  default = "<EMAIL>"
  description = "SA email of analytics-content gitlab runner"
  type = string
}
variable "pro_agents" {}
variable "total_agents" {}
variable "pro_tenant" {}
variable "lcaas" {}
variable "multi_project_postfix" {}
variable "pool_tenant_creation" {}
variable "pool_tenant_activation" {}
variable "pool_tenant" {}
variable "project_id" {}
variable "project_network" {}
variable "project_number" {}
variable "project_prefix" {}
variable "project_subnetwork" {}
variable "region" {}
variable "machine_type" { default = "e2-medium" }
variable "regional_kubernetes" { default = false }
variable "megatron_xdr" {}
variable "megatron_xsoar" {}
variable "mysql_prod_spec_override" {default = false}
variable "overrides" { default = {}}
variable "router_sa_google_group" {}
variable "autopilot_sa_google_group" {}
variable "autopilot_ssh_group" { default = "<EMAIL>" }
variable "redis_split" {}
variable "terraform_iam" {}
variable "scylla_blue_nodepool" {}
variable "scylla_green_nodepool" {}
variable "ipl_scylla_blue_nodepool" { default = true }
variable "ipl_scylla_green_nodepool" { default = false }
variable "xcloud_scylla_blue_nodepool" { default = true }
variable "xcloud_scylla_green_nodepool" { default = false }
variable "enable_scylla_iplen" {}
variable "scylla_nodes_count" {}
variable "scylla_iplen_nodes_count" {}
variable "scylla_standalone" {}
variable "small_epp" {}
variable "streaming_api_sa" { default = "" }
variable "tenant_type" {}
variable "creation_date" {}
variable "traps_sa_bq_reader" { default = "<EMAIL>" }
variable "rde_anon2_bq_deploy" { default = "<EMAIL>" }
variable "viso_env" {}
variable "xcloud_standalone_deployment" { default = false }
variable "xdr_env" {}
variable "xdr_prometheus_loadbalancer_ip" { default = "***********" }
variable "xsoar_6_mig_size" {
  default = "large"
}
variable "xsoar_mig_specs" {}
variable "enable_xsoar_shared_components" {
  type    = bool
  default = false
}
variable "customer_dev_tenant" { default = false }
variable "zone" {}
variable "kube_num_extra_zones" {
  default = 1
}
variable "big_tenant" { default = false }

variable "metering_start_date" {}
variable "enable_gke_metering" {
  type = bool
  default = false
}
variable "enable_network_egress_metering" {
  type = bool
  default = true
}
variable "enable_resource_consumption_metering" {
  type = bool
  default = true
}
variable "is_fedramp" {}
variable "enable_custom_kube_dns" {
  type = bool
  default = true
}
variable "multi_zoned_nodepools" {
    type = bool
    default = false
}
variable "elasticsearch_standalone" {
  default = false
}
variable "enable_itdr" {
  type = bool
  default = false
}

variable "sa_key_exempt_tag_value" {
  type = string
  default = ""
}

variable "viso_version" {}
variable "enable_alyx" {}

locals {
  gke_version                      = "1-33"
  mig_node_machine_type            = lookup(var.xsoar_mig_specs,var.xsoar_6_mig_size).node_pool_machine_type
  artifact_service_account_email   = module.create_iam.service_account_email["artifact-pull"]
  default_instance_type            = "n1-standard-4"
  gcs_account                      = "service-${var.project_number}@gs-project-accounts.iam.gserviceaccount.com"
  zone_index                       = index(var.compute_zones_names, var.zone)
  extra_zones                      = [for i in range(min(length(var.compute_zones_names), var.kube_num_extra_zones + 1)): element(var.compute_zones_names, i + local.zone_index) if i + local.zone_index != local.zone_index]
  node_locations                   = var.regional_kubernetes ? concat([var.zone], local.extra_zones) : var.multi_zoned_nodepools ? local.extra_zones : []
  proxy_service_account_email      = module.create_iam.service_account_email["proxy-sa"]
  router_service_account_email     = module.create_iam.service_account_email["router-sa"]
  autopilot_service_account_email  = var.enable_research_autopilot ? module.create_iam.service_account_email["autopilot-sa"] : ""
  copolit_service_account_email    = var.viso_env == "dev" ? "<EMAIL>" : "<EMAIL>"
  rts_email                        = "kube-sa-rts"
  rts_env_suffix                   = var.viso_env == "dev" ? "-02" : "-01"
  tenant_uuid                      = element(split(".", var.external_fqdn), 0)
  gcp_groups_domain                = var.is_fedramp ? "fedramp-panw.com" : "paloaltonetworks.com"
  global_profiles_eu_regions       = ["prod-eu", "prod-de"]
  global_profiles_project_env      = contains(["dev","prod-fr", "prod-gv"], var.viso_env) ? var.viso_env : contains(local.global_profiles_eu_regions, var.viso_env) ? "prod-eu" : "prod-us"
  proxy_vm_family_version          = "316"
  GCP_BILLING_EXPORT_TABLE_FULL_PATH = lookup(var.gcp_billing_export_table_map, var.viso_env, "xdr-billing-prod-us-01.production.prod")
  autoscaling_profile              = var.small_epp  ? "BALANCED" : "OPTIMIZE_UTILIZATION"
  node_autoprovisioning            = false
  enable_custom_kube_dns           = lookup(var.overrides, "enable_custom_kube_dns", var.enable_custom_kube_dns)
  enable_cortex_platform           = lookup(var.overrides, "enable_cortex_platform", false)
  enable_primary_playbook_mirroring = lookup(var.overrides, "enable_primary_playbook_mirroring", false)
  enable_proxy_vm_ha               = lookup(var.overrides, "enable_proxy_vm_ha", false)
  mysql_dev_spec_enabled           = (var.viso_env == "dev" && var.is_xsoar &&
  !(var.small_epp || var.mysql_prod_spec_override || var.prod_spec)) # no overrides enabled
  xdr_frontend_loadbalancer_ip = "10.181.${var.metro_tenant_index}.50"
  sa_key_exempt_tag_value          = var.is_fedramp ? "tagValues/281484257087014" : "tagValues/281484423598331"
  enable_max_pods_per_node         = lookup(var.overrides, "enable_max_pods_per_node", false)
  enable_email_artifacts_relay     = lookup(var.overrides, "enable_email_artifacts_relay", false)
  enable_email_sku                 = !var.is_metro_tenant && local.enable_email_artifacts_relay && (var.is_xdr || var.is_xsiam)
  enable_spanner                   = true
}

variable "gcp_billing_export_table_map" {
  type        = map(string)
  description = "Map of billing export table names to their full GCP path"
  default     = {
    dev     = "xdr-billing-prod-us-01.production.dev_qa_beta"
    prod-fr = "xdr-billing-prod-us-01.production.prod_fr"

  }
}


variable "uptime_otp_id" {
  default = ""
  sensitive   = true
}

variable "is_metro_tenant" {}
variable "metro_host_project_id" { default = "" }
variable "metro_host_id" { default = "" }
variable "metro_version" { default = ""}
variable "metro_tenant_index" {
  type = number
  default = 0
}
variable "metro_all_in_one" {
  type = bool
  default = false
}

variable "enable_cloud_posture" {
  type = bool
  default = false
}

variable "enable_cloud_appsec" {
  type = bool
  default = false
}

variable "enable_research_autopilot" {
  type = bool
  default = false
}

variable "is_xsoar_legacy_spec" {
  type = bool
  default = false
}