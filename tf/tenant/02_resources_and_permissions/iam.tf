module "create_iam" {
  source                  = "../../modules/iam"
  project_id              = var.project_id
  service_accounts        = local.service_accounts
  project_iam_members     = local.project_iam_members
  custom_roles            = local.custom_roles
  service_accounts_no_prevent_destroy = local.service_accounts_no_prevent_destroy
}

locals {
  service_accounts = {
    bq-reader = {
      account_id = "bq-reader-${var.lcaas}"
    }
    bq-writer = {
      account_id = "bq-writer-${var.lcaas}"
    }
    proxy-sa = {
      account_id = "proxy-sa-${var.lcaas}"
    }
    router-sa = {
      account_id = "router-sa-${var.lcaas}"
    }
    autopilot-sa = {
      enabled = var.enable_research_autopilot
      account_id =  "autopilot-sa-${var.lcaas}"
    }
    artifact-pull = {
      account_id = "pull-image"
    }
    prisma-console = {
      account_id = "prisma-console"
    }
    xsoar-onprem-sa = {
      enabled = var.is_xsoar_6_migration && var.is_xsoar_onprem_migration
      account_id =  "xsoar-onprem"
    }
  }
  service_accounts_no_prevent_destroy = {
      event-forwarding-viewer = {
      enabled = var.egress_enabled
      account_id = "event-forwarding-viewer"
    }
  }
  research_automation_roles = (lookup(var.overrides, "research_tenant", false) && var.viso_env == "dev") ? {
    research_bigquery_viewer = {
      role   = "roles/bigquery.dataViewer"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    research_bigquery_metadata = {
      role   = "roles/bigquery.metadataViewer"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    research_bigquery_dataEditor = {
      role   = "roles/bigquery.dataEditor"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    research_kubernetes_admin = {
      role   = "roles/container.admin"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    research_pubsub_editor = {
      role   = "roles/pubsub.editor"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    research_storage_admin = {
      role   = "roles/storage.admin"
      member = "serviceAccount:${var.research_automation_service_account}"
    }
    analytics_content_runner_registry_writer = {
      role   = "roles/artifactregistry.writer"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_bq_date_editor = {
      role   = "roles/bigquery.dataEditor"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_bq_resource_viewer = {
      role   = "roles/bigquery.resourceViewer"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_browser = {
      role   = "roles/browser"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_cloudsupport = {
      role   = "roles/cloudsupport.techSupportEditor"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_container_admin = {
      role   = "roles/container.admin"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_datastore_user = {
      role   = "roles/datastore.user"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_sa_token_creator = {
      role   = "roles/iam.serviceAccountTokenCreator"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_pubsub_admin = {
      role   = "roles/pubsub.admin"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_spanner_db_user = {
      role   = "roles/spanner.databaseUser"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_storage_admin = {
      role   = "roles/storage.admin"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
    analytics_content_runner_viewer = {
      role   = "roles/viewer"
      member = "serviceAccount:${var.analytics_content_runner_sa}"
    }
  } : {}
  project_iam_members = merge({
    agent_gatway_services_use = {
      role   = "projects/${var.project_id}/roles/ServicesUsage"
      member = "serviceAccount:kube-sa-agent-gateway@xdr-agent-gateway-${var.viso_env}-01.iam.gserviceaccount.com"
    }
    artifact_download = {
      role   = "projects/${var.project_id}/roles/ArtifactRegistryDowloadCr"
      member = "serviceAccount:${local.artifact_service_account_email}"
    }
    harvester_services_use = {
      role   = "projects/${var.project_id}/roles/ServicesUsage"
      member = "serviceAccount:kube-sa-harvester@xdr-harvester-${var.viso_env}-01.iam.gserviceaccount.com"
    }
    proxy_sa_iam_logs = {
      role   = "roles/logging.logWriter"
      member = "serviceAccount:${local.proxy_service_account_email}"
    }
    proxy_sa_iam_metrics = {
      role   = "roles/monitoring.metricWriter"
      member = "serviceAccount:${local.proxy_service_account_email}"
    }
    proxy_sa_iam_metrics_viewer = {
      role   = "roles/monitoring.viewer"
      member = "serviceAccount:${local.proxy_service_account_email}"
    }
    router_sa_log_writer_role = {
      role   = "roles/logging.logWriter"
      member = "serviceAccount:${local.router_service_account_email}"
    }
    router_sa_monitoring_writer_role = {
      role   = "roles/monitoring.metricWriter"
      member = "serviceAccount:${local.router_service_account_email}"
    }
    router_sa_describe_role = {
      role   = "projects/${var.project_id}/roles/AllowInstancesGet"
      member = "serviceAccount:${local.router_service_account_email}"
    }
    autopilot_sa_bq_job_role = var.enable_research_autopilot ? {
      role   = "roles/bigquery.jobUser"
      member = "serviceAccount:${local.autopilot_service_account_email}"
    } : {}
    autopilot_sa_iap_tunnels = var.enable_research_autopilot ? {
      role   = "roles/iap.tunnelResourceAccessor"
      member = "group:${var.autopilot_ssh_group}"
    } : {}
    autopilot_sa_vertex_ai_user_role = var.enable_research_autopilot ? {
      role   = "roles/aiplatform.user"
      member = "serviceAccount:${local.autopilot_service_account_email}"
    } : {}
    autopilot_sa_set_metadata = var.enable_research_autopilot ? {
      role   = "projects/${var.project_id}/roles/${local.custom_roles.allow_compute_instances_setMetadata.role_id}"
      member = "group:${var.autopilot_ssh_group}"
      condition = {
        title = "instance-autopilot-set-metadata-restriction"
        description = "Grants the ability to update metadata on a specific VM. This is a required permission for adding SSH keys to a VM instance for login"
        expression = "resource.name.startsWith('projects/${var.project_id}/zones/${var.zone}/instances/autopilot-${var.lcaas}-')"
      }
    } : null
    autopilot_sa_compute_role = var.enable_research_autopilot ? {
      role   = "roles/compute.viewer"
      member = "serviceAccount:${local.autopilot_service_account_email}"
    } : {}
    autopilot_instance_delete = var.enable_research_autopilot ? {
      role   = "projects/${var.project_id}/roles/autopilot_instance_delete"
      member = "serviceAccount:${local.autopilot_service_account_email}"
      condition = {
        title       = "instance-autopilot-restriction"
        description = "Grants delete access to autopilot instances only, allowing them to self-destruct once the TTL is reached."
        expression  = "resource.name.startsWith('projects/${var.project_id}/zones/${var.zone}/instances/autopilot-${var.lcaas}-')"
      }
    } : null
    mt_copolit_ai_view_role = ! var.is_fedramp ? {
      role   = "roles/aiplatform.viewer"
      member = "serviceAccount:${local.copolit_service_account_email}"
    } : {}
    mt_copolit_ai_user_role = ! var.is_fedramp ? {
      role   = "roles/aiplatform.user"
      member = "serviceAccount:${local.copolit_service_account_email}"
    } : {}
    gateway_byok_admin = var.enable_byok && var.pool_tenant_creation ? {
      role   = "roles/cloudkms.admin"
      member = "serviceAccount:backend@xdr-gateway-${var.multi_project_postfix}-01.iam.gserviceaccount.com"
    } : {}
  }, local.research_automation_roles)
  custom_roles = {
    allow_router_instances_get = {
      role_id     = "AllowInstancesGet"
      title       = "allow router instances get"
      description = "allow router instances get"
      permissions = ["compute.instances.get"]
    }
    create_bucket_permissions = {
      role_id     = "CreateBucketsAccess"
      title       = "Allow to engine tf user create access"
      description = "Allow to engine tf user create access"
      permissions = ["storage.buckets.get", "storage.buckets.setIamPolicy", "storage.buckets.getIamPolicy", "storage.buckets.list"]
    }
    add_bucket_read_and_write_permissions_role = {
      role_id     = "AllowBucketsReadAndWriteAccess"
      title       = "Allow buckets read and write access"
      description = "Allow buckets read and write access"
      permissions = ["storage.buckets.get", "storage.objects.create", "storage.objects.get", "storage.objects.list"]
    }
    add_bucket_read_permissions_role = {
      role_id     = "AllowBucketsReadAccess"
      title       = "Allow buckets read access"
      description = "Allow buckets read access"
      permissions = ["storage.objects.get", "storage.objects.list", "storage.buckets.get"]
    }
    write_and_delete_bucket_object_permissions_role = {
      role_id     = "AllowBucketsWriteAndDeleteObject"
      title       = "Allow buckets write and delete object"
      description = "Allow buckets write and delete object"
      permissions = ["storage.objects.create", "storage.objects.delete", "storage.objects.list", "storage.buckets.get"]
    }
    delete_bucket_object_permissions_role = {
      role_id     = "AllowBucketsDeleteObject"
      title       = "Allow buckets delete object"
      description = "Allow buckets delete object"
      permissions = ["storage.objects.delete", "storage.objects.list", "storage.buckets.get"]
    }
    allow_bucket_read_write_no_list = {
      role_id     = "AllowBucketReadWriteNoList"
      title       = "Allow Bucket read and write access without list"
      description = "Allow Bucket read and write access without list"
      permissions = ["storage.objects.get", "storage.objects.create", "storage.objects.delete"]
    }
    artifact_registry_download_and_upload_role = {
      role_id     = "AllowArtifactRegistryDownloadAndUpload"
      title       = "Allow Artifact Registry Download And Upload"
      description = "Allow Artifact Registry Download And Upload"
      permissions = ["artifactregistry.repositories.downloadArtifacts", "artifactregistry.repositories.uploadArtifacts"]
    }
    artifact_registry_dowload_cr = {
      role_id     = "ArtifactRegistryDowloadCr"
      title       = "Allow Artifact Registry Download"
      description = "Allow Artifact Registry Download"
      permissions = ["artifactregistry.repositories.downloadArtifacts"]
    }
    gcs_signed_url_generator = {
      role_id     = "GCSSignedUrlBlobGenerator"
      title       = "GCSSignedUrlBlobGenerator"
      description = "Grants permissions to generate signed URLs for GCS blobs."
      permissions = ["iam.serviceAccounts.signBlob"]
    }
    pull_image_cr = {
      role_id     = "PullImageCr"
      title       = "Allow Pull Image"
      description = "Allow Pull Image"
      permissions = ["iam.serviceAccountKeys.create", "iam.serviceAccountKeys.delete" , "iam.serviceAccountKeys.get"]
    }
    services_usage_role = {
      role_id = "ServicesUsage"
      title = "Allow service usage for go libreries"
      description = "Allow service usage for go libreries"
      permissions = ["serviceusage.services.use"]
    }
    list_bigquery_jobs_role = {
      role_id = "ListBigqueryJobs"
      title = "Allow listing bigquery jobs"
      description = "Allow listing bigquery jobs"
      permissions = ["bigquery.jobs.listAll"]
    }
    vertex_ai_custom_role = {
      role_id = "VertexAIEmbedderRole"
      title = "Vertex AI Embedder"
      description = "Allow to get, list, and use predict method on models"
      permissions = ["aiplatform.endpoints.get","aiplatform.endpoints.list","aiplatform.endpoints.predict"]
    }
    rde_anon2_deploy_role = {
      role_id     = "RDEAnon2TenantDataRole"
      title       = "RDE Anon2.0 Tenant Data BigQuery Role"
      description = "Allow to deploy Anonymization 2.0 views without access to data"
      permissions = ["bigquery.tables.get", "bigquery.tables.getData", "bigquery.datasets.get", "bigquery.datasets.update"]
    }
    rde_anon2_read_role = {
      role_id     = "RDEAnon2TenantViewRole"
      title       = "RDE Anon2.0 Tenant View BigQuery Role"
      description = "Allow to Update Anonymization 2.0 views without access to data"
      permissions = ["bigquery.tables.get", "bigquery.tables.getData", "bigquery.datasets.get", "bigquery.tables.update","bigquery.tables.update"]
    }
    autopilot_bq_role = {
      enabled     = var.enable_research_autopilot
      role_id     = "autopilotBigQuery"
      title       = "Autopilot readonly BigQuery Role"
      description = "Allow read datasets"
      permissions = ["bigquery.tables.get", "bigquery.tables.getData", "bigquery.datasets.get"]
    }
    create_and_attach_subs = {
      role_id     = lookup(var.overrides, "create_and_attach_subs_role_id", "CreateAndAttachSubscriptions")
      title       = "Create and attaches subscriptions"
      description = "Enables the creation and attachment of subscriptions"
      permissions = ["pubsub.subscriptions.create", "pubsub.topics.attachSubscription", "pubsub.subscriptions.get", "pubsub.subscriptions.update", "pubsub.subscriptions.delete", "pubsub.subscriptions.list", "pubsub.subscriptions.getIamPolicy", "pubsub.subscriptions.setIamPolicy", "pubsub.topics.get", "pubsub.topics.list"]
    }
    create_bigquery_tables = {
      role_id     = lookup(var.overrides, "role_id_create_bigquery_tables", "CreateBqTables")
      title       = "Create BQ Tables"
      description = "Allow the creation of Big Query Tables"
      permissions = ["bigquery.tables.create"]
    }
    allow_compute_instances_setMetadata =  {
      enabled     = var.enable_research_autopilot
      role_id     = "AllowComputeInstancesSetMetadata"
      title       = "Allow to set instances metadata"
      description = "Allow to set instances metadata"
      permissions = ["compute.instances.setMetadata"]
    }
    autopilot_instance_delete = {
      enabled     = var.enable_research_autopilot
      role_id     = "autopilot_instance_delete"
      title       = "Autopilot Instance Delete"
      description = "Custom role allowing autopilot to delete instance"
      permissions = ["compute.instances.delete"]
    }
    get_topics_iam_policy = {
      role_id     = "GetTopicsIamPolicies"
      title       = "allow read topics/subs/snapshots/schemas iam policies"
      description = "allow read topics/subs/snapshots/schemas iam policies"
      permissions = ["pubsub.topics.getIamPolicy",
	"pubsub.subscriptions.getIamPolicy",
	"pubsub.schemas.getIamPolicy",
	"pubsub.snapshots.getIamPolicy"]
    }
    spanner_autoscaler_state_role = {
      role_id     = lookup(var.overrides, "spanner_autoscaler_state_role", "SpannerAutoscalerStateRole")
      title       = "Allow Spanner Autoscaler to manage Cloud Spanner state DB"
      description = "Allow Spanner Autoscaler to manage Cloud Spanner state DB"
      permissions = [
        "spanner.databases.read",
        "spanner.databases.write",
        "spanner.databases.get",
        "spanner.databases.list",
        "spanner.databases.beginOrRollbackReadWriteTransaction",
        "spanner.databases.beginReadOnlyTransaction",
        "spanner.sessions.create",
        "spanner.sessions.delete",
        "spanner.sessions.get",
        "spanner.sessions.list"
      ]
    }
    spanner_autoscaler_role = {
      role_id     = lookup(var.overrides, "spanner_autoscaler_role", "SpannerAutoscalerRole")
      title       = "Allow Spanner Autoscaler to manager cloud spanner"
      description = "Allow Spanner Autoscaler to manager cloud spanner"
      permissions = [
        "spanner.instances.get",
        "spanner.instances.update",
        "spanner.instances.list"
      ]
    }
    spanner_autoscaler_operations_role = {
      role_id     = lookup(var.overrides, "spanner_autoscaler_operations_role", "SpannerAutoscalerOperationsRole")
      title       = "Allow Spanner Autoscaler to manager cloud spanner operations"
      description = "Allow Spanner Autoscaler to manager cloud spanner operations"
      permissions = [
        "spanner.instanceOperations.get",
        "spanner.instanceOperations.list"
      ]
    }
  }
  rde_anon2_bigquery_access = var.is_fedramp ? [] : [
    lookup(module.create_bq.bq_output, "aispm"),
    lookup(module.create_bq.bq_output, "analytics"),
    lookup(module.create_bq.bq_output, "analytics_on_demand"),
    lookup(module.create_bq.bq_output, "analytics_profiles_views"),
    lookup(module.create_bq.bq_output, "apisec_analytics"),
    lookup(module.create_bq.bq_output, "application"),
    lookup(module.create_bq.bq_output, "asset_inventory"),
    lookup(module.create_bq.bq_output, "cas"),
    lookup(module.create_bq.bq_output, "cas_detection_rules"),
    lookup(module.create_bq.bq_output, "cas_policies"),
    lookup(module.create_bq.bq_output, "ciem"),
    lookup(module.create_bq.bq_output, "ciem_permissions_raw"),
    lookup(module.create_bq.bq_output, "classification_mgmt"),
    lookup(module.create_bq.bq_output, "cloudsec"),
    lookup(module.create_bq.bq_output, "ds"),
    # ds_${var.lcaas}_view is already managed
    lookup(module.create_bq.bq_output, "dspm"),
    lookup(module.create_bq.bq_output, "enums"),
    lookup(module.create_bq.bq_output, "external_data"),
    lookup(module.create_bq.bq_output, "forensics"),
    lookup(module.create_bq.bq_output, "itdr"),
    lookup(module.create_bq.bq_output, "lookup"),
    lookup(module.create_bq.bq_output, "public_platform"),
    lookup(module.create_bq.bq_output, "supply_chain_tools"),
  ]
  autopilot_bigquery_access = var.enable_research_autopilot ? [
    lookup(module.create_bq.bq_output, "ds"),
    lookup(module.create_bq.bq_output, "analytics"),
  ] : []
}

resource "terraform_data" "bind_proxy_service_account_to_group" {

  triggers_replace = {
    proxy_sa = local.proxy_service_account_email
    google_group = "gcplocal-xdr-wi-kuna-gcr-${var.viso_env}@${local.gcp_groups_domain}"
  }

  provisioner "local-exec" {
    command = "python /xdr/devops/general_scripts/add_user_to_group.py ${local.proxy_service_account_email} gcplocal-xdr-wi-kuna-gcr-${var.viso_env}@${local.gcp_groups_domain}"
  }
}

resource "terraform_data" "bind_router_service_account_to_group" {

  triggers_replace = {
    proxy_sa = local.router_service_account_email
    google_group = var.router_sa_google_group
  }

  provisioner "local-exec" {
    command = "python /xdr/devops/general_scripts/add_user_to_group.py ${local.router_service_account_email} ${var.router_sa_google_group}"
  }
}

resource "terraform_data" "bind_default_service_account_to_router_group" {

  triggers_replace = {
    proxy_sa = local.router_service_account_email
    google_group = var.router_sa_google_group
  }

  provisioner "local-exec" {
    command = "python /xdr/devops/general_scripts/add_user_to_group.py ${var.project_number}@cloudservices.gserviceaccount.com ${var.router_sa_google_group}"
  }
}

resource "terraform_data" "bind_autopilot_service_account_to_group" {
  count = var.enable_research_autopilot ? 1 : 0

  triggers_replace = {
    autopilot_sa = local.autopilot_service_account_email
    google_group = var.autopilot_sa_google_group
  }

  provisioner "local-exec" {
    command = "python /xdr/devops/general_scripts/add_user_to_group.py ${local.autopilot_service_account_email} ${var.autopilot_sa_google_group}"
  }
}

resource "google_project_iam_member" "metro__prom_project_iam_member" {
  count    = var.is_metro_tenant && !var.metro_all_in_one ? 1 : 0
  project  = var.project_id
  role     = "roles/monitoring.viewer"
  member   = "serviceAccount:prometheus@${var.metro_host_project_id}.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "metro__proxy_sa_monitoring_viewer" {
  count    = var.is_metro_tenant && !var.metro_all_in_one ? 1 : 0
  project  = var.project_id
  role     = "roles/monitoring.viewer"
  member   = "serviceAccount:proxy-sa-${var.metro_host_id}@${var.metro_host_project_id}.iam.gserviceaccount.com"
}

resource "google_bigquery_dataset_access" "rde_anon2_bigquery_permissions" {
  depends_on = [
    module.create_iam,
    module.create_bq,
  ]
  for_each      = toset(local.rde_anon2_bigquery_access)
  project       = var.project_id
  dataset_id    = each.key
  role          = "projects/${var.project_id}/roles/RDEAnon2TenantDataRole"
  user_by_email = var.rde_anon2_bq_deploy
}

resource "google_bigquery_dataset_access" "autopilot_bigquery_permissions" {
  depends_on = [
    module.create_iam,
    module.create_bq,
  ]
  for_each      = var.enable_research_autopilot ? toset(local.autopilot_bigquery_access) : toset([])
  project       = var.project_id
  dataset_id    = each.key
  role          = "projects/${var.project_id}/roles/autopilotBigQuery"
  user_by_email = local.autopilot_service_account_email
}

resource "google_tags_tag_binding" "pull_image_sa_key_exemption_binding" {
  provider = google.mt
  depends_on = [
    module.create_iam
  ]
  tag_value = local.sa_key_exempt_tag_value
  parent    = "//iam.googleapis.com/projects/${var.project_number}/serviceAccounts/${module.create_iam.service_account_unique_id["artifact-pull"]}"
}

resource "google_tags_tag_binding" "prisma_console_sa_key_exemption_binding" {
  provider = google.mt
  depends_on = [
    module.create_iam
  ]
  tag_value = local.sa_key_exempt_tag_value
  parent    = "//iam.googleapis.com/projects/${var.project_number}/serviceAccounts/${module.create_iam.service_account_unique_id["prisma-console"]}"
}

resource "google_tags_tag_binding" "event_viewer_sa_key_exemption_binding" {
  count = var.egress_enabled ? 1 : 0
  provider = google.mt
  depends_on = [
    module.create_iam
  ]
  tag_value = local.sa_key_exempt_tag_value
  parent    = "//iam.googleapis.com/projects/${var.project_number}/serviceAccounts/${module.create_iam.service_account_no_prevent_destroy_unique_id["event-forwarding-viewer"]}"
}

resource "google_tags_tag_binding" "xsoar_onprem_sa_key_exemption_binding" {
  count = var.is_xsoar_6_migration && var.is_xsoar_onprem_migration ? 1 : 0
  provider = google.mt
  depends_on = [
    module.create_iam
  ]
  tag_value = local.sa_key_exempt_tag_value
  parent    = "//iam.googleapis.com/projects/${var.project_number}/serviceAccounts/${module.create_iam.service_account_unique_id["xsoar-onprem-sa"]}"
}

resource "google_service_account_iam_binding" "autopilot_sa_binding" {
  count              = var.enable_research_autopilot ? 1 : 0
  service_account_id = "projects/${var.project_id}/serviceAccounts/${local.service_accounts.autopilot-sa.account_id}@${var.project_id}.iam.gserviceaccount.com"
  role               = "roles/iam.serviceAccountUser"
  members = [
    "group:${var.autopilot_sa_google_group}",
    "group:${var.autopilot_ssh_group}"
  ]
  depends_on = [
    module.create_iam
  ]
}
