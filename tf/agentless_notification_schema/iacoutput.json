{"type": "object", "required": ["operation", "template_id", "template_version", "provisioning_method", "account_id", "credentials", "resources_data"], "properties": {"operation": {"type": "string", "enum": ["create_outpost"]}, "template_id": {"type": "string", "examples": ["AWS_MANAGED_OUTPOST", "GCP_MANAGED_OUTPOST", "AZURE_MANAGED_OUTPOST", "62ae0668-94eb-461d-9db8-fa62cedeb89f"]}, "template_version": {"type": "string", "examples": ["1.0.0"]}, "provisioning_method": {"type": "string", "enum": ["TF", "CF", "ARM", "GCDM"]}, "account_id": {"type": "string", "description": "Account/Project/Subscription ID", "examples": [************]}, "account_name": {"type": "string", "description": "Account/Project/Subscription friendly name from CSP", "examples": ["staging-account"]}, "account_number": {"type": "string", "description": "Account number - required for GCP Outposts only", "examples": ["*************"]}, "organization_id": {"type": "string", "description": "tenant_id for Azure, organization_id for GCP. Not relevant for AWS and managed outpost in general. Given from UI"}, "credentials": {"oneOf": [{"type": "object", "required": ["outpost_role_arn"], "properties": {"outpost_role_arn": {"type": "string", "examples": ["arn:aws:iam::************:role/cortex-saas-role"]}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["outpost_service_account_email"], "properties": {"outpost_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["client_id"], "properties": {"client_id": {"type": "string", "format": "guid", "description": "App Registraion App (Client) ID", "examples": ["b165bb10-155b-4b10-830b-4585ce031756"]}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["outpost_tenancy_ocid", "outpost_user_ocid", "outpost_group_ocid", "outpost_secret_id", "outpost_user_public_key_pem", "outpost_user_public_key_fingerprint", "outpost_region_name"], "properties": {"outpost_tenancy_ocid": {"type": "string", "description": "OCI Tenancy OCID", "examples": ["ocid1.user.oc1..example"]}, "outpost_user_ocid": {"type": "string", "description": "OCI Identity User OCID", "examples": ["ocid1.user.oc1..example"]}, "outpost_group_ocid": {"type": "string", "description": "OCI Identity Group OCID", "examples": ["ocid1.user.oc1..example"]}, "outpost_secret_id": {"type": "string", "description": "OCI Identity User Private Key Secret ID", "examples": ["ocid1.secret.oc1..example/versions/latest"]}, "outpost_user_public_key_pem": {"type": "string", "description": "Public Key in PEM format", "examples": ["-----B<PERSON>IN PUBLIC KEY-----\nMIIBIjANBgkqh...==\n-----END PUBLIC KEY-----"]}, "outpost_user_public_key_fingerprint": {"type": "string", "description": "Fingerprint of the public key", "examples": ["af:3b:59:2f:3e:1c:cb:39:7a:8f:ef:b7:35:8b:9f"]}, "outpost_region_name": {"type": "string", "description": "OCI tenancy home region key", "examples": ["us-phoenix-1"]}}}]}, "resources_data": {"oneOf": [{"type": "object", "required": ["global_resources", "regions"], "properties": {"global_resources": {"type": "object", "required": ["ads_scanner_ec2_instance_profile_arn", "ads_scanner_role_arn", "dspm_scanner_ec2_instance_profile_arn", "dspm_scanner_role_arn", "onboarding_cf_templates_bucket_arn", "proxy_vm_role_arn", "proxy_vm_ec2_instance_profile_arn", "registry_scanner_ec2_instance_profile_arn", "registry_scanner_role_arn", "scanner_ec2_instance_profile_arn", "scanner_role_arn", "scanner_of_serverless_ec2_instance_profile_arn", "scanner_of_serverless_role_arn"], "properties": {"ads_scanner_ec2_instance_profile_arn": {"$ref": "#/definitions/instanceProfileArn"}, "ads_scanner_role_arn": {"$ref": "#/definitions/roleArn"}, "dspm_scanner_ec2_instance_profile_arn": {"$ref": "#/definitions/instanceProfileArn"}, "dspm_scanner_role_arn": {"$ref": "#/definitions/roleArn"}, "onboarding_cf_templates_bucket_arn": {"type": "string", "examples": ["arn:aws:s3:::cortex-templates-bucket-qa2-test-9999999999"]}, "registry_scanner_ec2_instance_profile_arn": {"$ref": "#/definitions/instanceProfileArn"}, "registry_scanner_role_arn": {"$ref": "#/definitions/roleArn"}, "scanner_ec2_instance_profile_arn": {"$ref": "#/definitions/instanceProfileArn"}, "scanner_role_arn": {"$ref": "#/definitions/roleArn"}, "scanner_of_serverless_ec2_instance_profile_arn": {"$ref": "#/definitions/instanceProfileArn"}, "scanner_of_serverless_role_arn": {"$ref": "#/definitions/roleArn"}}}, "regions": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["name", "regional_resources", "zones"], "properties": {"name": {"type": "string", "examples": ["us-east-1", "us-east-2"]}, "regional_resources": {"type": "object", "required": ["ec2_ads_scanner_security_group_id", "ec2_aws_lambda_endpoint_security_group_id", "ec2_dspm_security_group_id", "ec2_egress_proxy_security_group_id", "ec2_registry_scanner_security_group_id", "ec2_scanner_security_group_id", "ec2_scanner_of_serverless_security_group_id", "ec2_aws_ssm_security_group_id", "ec2_sts_endpoint_security_group_id", "ec2_aws_ecr_api_endpoint_security_group_id", "ec2_aws_ecr_dkr_endpoint_security_group_id", "ec2_aws_monitoring_endpoint_security_group_id", "ec2_cortex_scanner_security_group_id", "ec2_cortex_interface_endpoint_security_group_id", "s3_artifact_bucket_name", "s3_bucket_name", "sqs_queue_url", "ec2_vpc_id"], "properties": {"s3_bucket_name": {"type": "string", "pattern": "^[a-z0-9.-]{3,63}$", "examples": ["cortex-outpost-bucket-67868768"]}, "s3_artifact_bucket_name": {"type": "string", "pattern": "^[a-z0-9.-]{3,63}$", "examples": ["cortex-outpost-artifact-bucket-67868768"]}, "sqs_queue_url": {"type": "string", "pattern": "^https://sqs.[a-z0-9-]+.amazonaws.com/[0-9]{12}/[a-zA-Z0-9_-]+", "examples": ["https://sqs.us-east-1.amazonaws.com/************/cortexOutpostSQSQueue"]}, "ec2_vpc_id": {"type": "string", "pattern": "^vpc-[0-9a-f]{17}$", "examples": ["vpc-1a2b3c4d"]}, "ec2_cortex_interface_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_cortex_scanner_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_ads_scanner_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_aws_lambda_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_monitoring_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_scanner_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_scanner_of_serverless_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_ssm_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_sts_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_aws_ecr_api_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_aws_ecr_dkr_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_aws_monitoring_endpoint_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_registry_scanner_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_dspm_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}, "ec2_egress_proxy_security_group_id": {"type": "string", "pattern": "^sg-[0-9a-f]{17}$", "examples": ["sg-02ce123456e7893c7"]}}}, "zones": {"type": "array", "minItems": 2, "items": {"type": "object", "required": ["name", "zonal_resources"], "properties": {"name": {"type": "string", "examples": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f", "us-east-2a", "us-east-2b", "us-east-2c"]}, "id": {"type": "string", "examples": ["use1-az1", "use1-az2", "use1-az3", "use1-az4", "use1-az5", "use1-az6", "use2-az1", "use2-az2", "use2-az3"]}, "zonal_resources": {"type": "object", "required": ["ec2_subnet_id"], "properties": {"ec2_subnet_id": {"type": "string", "pattern": "^subnet-[0-9a-f]{17}$", "examples": ["subnet-0d42d2235s3a2531d"]}}}}}}}}}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["global_resources", "regions"], "properties": {"global_resources": {"type": "object", "required": ["pubsub_subscription_id", "scanner_service_account_email", "ads_scanner_service_account_email", "dspm_scanner_service_account_email", "dspm_scanner_service_account_unique_id", "registry_scanner_service_account_email", "scanner_of_serverless_service_account_email", "proxy_service_account_email", "compute_vpc_id"], "properties": {"pubsub_subscription_id": {"type": "string", "pattern": "^projects/[a-z][a-z0-9-]{4,28}[a-z0-9]/subscriptions/[a-zA-Z][a-zA-Z0-9-_.~+%]{2,254}$", "examples": ["projects/cortex-outpost-project-1313213/subscriptions/cortex-outpost-pubsub-subscription"]}, "scanner_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "ads_scanner_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "dspm_scanner_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "dspm_scanner_service_account_unique_id": {"type": "string", "pattern": "^[0-9]{21}$", "examples": ["113992473192774186381"]}, "registry_scanner_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "scanner_of_serverless_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "proxy_service_account_email": {"type": "string", "pattern": "^[a-z]([-a-z0-9]{4,28}[a-z0-9])@[a-z][a-z0-9-]{4,28}[a-z0-9].iam.gserviceaccount.com$", "examples": ["<EMAIL>"]}, "compute_vpc_id": {"type": "string", "pattern": "^projects/[a-z][a-z0-9-]{4,28}[a-z0-9]/global/networks/[a-z]([-a-z0-9]{0,61}[a-z0-9])$", "examples": ["projects/cortex-outpost-project-1313213/global/networks/cortex-outpost-vpc"]}}}, "regions": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["name", "regional_resources"], "properties": {"name": {"type": "string", "examples": ["us-east1", "us-east4"]}, "regional_resources": {"type": "object", "required": ["storage_bucket_name", "artifact_storage_bucket_name", "compute_subnet_id"], "properties": {"storage_bucket_name": {"type": "string", "pattern": "^[a-z0-9][a-z0-9_-]{1,61}[a-z0-9]$", "examples": ["cortex-outpost-storage-bucket-us-east1"]}, "artifact_storage_bucket_name": {"type": "string", "pattern": "^[a-z0-9][a-z0-9_-]{1,61}[a-z0-9]$", "examples": ["cortex-outpost-artifact-storage-bucket-us-east1"]}, "compute_subnet_id": {"type": "string", "pattern": "^projects/[a-z][a-z0-9-]{4,28}[a-z0-9]/regions/[a-z]{2,12}-[a-z]{4,10}\\d{1,2}/subnetworks/[a-z][-a-z0-9]{0,61}[a-z0-9]$", "examples": ["projects/cortex-outpost-project-1313213/regions/us-east1/subnetworks/cortex-outpost-subnet"]}}}}}}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["global_resources", "regions"], "properties": {"global_resources": {"type": "object", "required": ["resource_group_name", "scanner_user_assigned_identity_id", "scanner_user_assigned_identity_client_id", "scanner_user_assigned_identity_principal_id", "ads_scanner_user_assigned_identity_id", "ads_scanner_user_assigned_identity_client_id", "ads_scanner_user_assigned_identity_principal_id", "dspm_scanner_user_assigned_identity_id", "dspm_scanner_user_assigned_identity_client_id", "dspm_scanner_user_assigned_identity_principal_id", "proxy_user_assigned_identity_client_id", "proxy_user_assigned_identity_id_principal_id", "proxy_user_assigned_identity_id", "registry_scanner_user_assigned_identity_id", "registry_scanner_user_assigned_identity_client_id", "registry_scanner_user_assigned_identity_principal_id", "serverless_user_assigned_identity_id", "serverless_user_assigned_identity_client_id", "serverless_user_assigned_identity_principal_id", "dspm_o365_app_registration_client_id"], "properties": {"resource_group_name": {"type": "string", "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}[a-zA-Z0-9]$", "examples": ["cortex-resource-group"]}, "scanner_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-scanner"]}, "scanner_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["7dc01dc6-2c84-41fa-96d2-98bc1b66b3bd"]}, "scanner_user_assigned_identity_principal_id": {"type": "string", "format": "guid", "examples": ["7dc01dc6-2c84-41fa-96d2-98bc1b66b3bd"]}, "ads_scanner_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-scanner"]}, "ads_scanner_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["7dc01dc6-2c84-41fa-96d2-98bc1b66b3bd"]}, "ads_scanner_user_assigned_identity_principal_id": {"type": "string", "format": "guid", "examples": ["7dc01dc6-2c84-41fa-96d2-98bc1b66b3bd"]}, "dspm_scanner_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-dspm-scanner"]}, "dspm_scanner_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "dspm_scanner_user_assigned_identity_principal_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "proxy_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "proxy_user_assigned_identity_id_principal_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "proxy_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-dspm-scanner"]}, "registry_scanner_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-dspm-scanner"]}, "registry_scanner_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "registry_scanner_user_assigned_identity_principal_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "serverless_user_assigned_identity_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.ManagedIdentity/userAssignedIdentities/[a-zA-Z0-9][a-zA-Z0-9_-]{0,23}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.ManagedIdentity/userAssignedIdentities/cortex-serverless-scanner"]}, "serverless_user_assigned_identity_client_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "serverless_user_assigned_identity_principal_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}, "dspm_o365_app_registration_client_id": {"type": "string", "format": "guid", "examples": ["5b370ecf-cdd7-46a7-b6cb-0f20d1f3cf66"]}}}, "regions": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["name", "regional_resources"], "properties": {"name": {"type": "string", "examples": ["eastus", "southcentralus"]}, "regional_resources": {"type": "object", "required": ["storage_account_name", "storage_container_name", "artifact_storage_container_name", "storage_queue_name", "network_security_group_id", "private_endpoint_asg_id", "ads_asg_id", "dspm_asg_id", "registry_asg_id", "proxy_asg_id", "serverless_asg_id", "subnet_id", "virtual_network_id", "key_vault_id"], "properties": {"storage_account_name": {"type": "string", "pattern": "^[a-z0-9]{3,24}$", "examples": ["crtxazpjpe7y5d3gedndygkw"]}, "storage_container_name": {"type": "string", "pattern": "^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$", "examples": ["cortex-storage-container-eastus"]}, "artifact_storage_container_name": {"type": "string", "pattern": "^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$", "examples": ["cortex-artifact-storage-container-eastus"]}, "storage_queue_name": {"type": "string", "pattern": "^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$", "examples": ["cortex-storage-queue-eastus"]}, "network_security_group_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/networkSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/networkSecurityGroups/cortex-scanner-security-group-eastus"]}, "private_endpoint_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "ads_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "dspm_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "registry_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "proxy_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "serverless_asg_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/applicationSecurityGroups/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/applicationSecurityGroups/cortex-scanner-application-security-group-eastus"]}, "subnet_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/virtualNetworks/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]/subnets/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/virtualNetworks/cortex-vpc-eastus/subnets/cortex-subnet-eastus"]}, "virtual_network_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.Network/virtualNetworks/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.Network/virtualNetworks/cortex-vpc-eastus"]}, "key_vault_id": {"type": "string", "pattern": "^/subscriptions/[a-f0-9-]{36}/resourceGroups/[a-zA-Z0-9][a-zA-Z0-9-_]{1,61}/providers/Microsoft\\.KeyVault/vaults/[a-zA-Z0-9][a-zA-Z0-9\\._-]{0,78}[a-zA-Z0-9_]$", "examples": ["/subscriptions/3ee44654-9e52-41a0-82ca-f5d5956452d6/resourceGroups/cortex-resource-group/providers/Microsoft.KeyVault/vaults/cortex-key-vault"]}}}}}}}, "$schema": "http://json-schema.org/schema#"}, {"type": "object", "required": ["global_resources", "regions"], "properties": {"global_resources": {"type": "object", "required": ["object_storage_namespace", "registry_scanner_dynamic_group_tag_value", "serverless_scanner_dynamic_group_tag_value", "ads_scanner_dynamic_group_tag_value", "dspm_scanner_dynamic_group_tag_value", "proxy_dynamic_group_tag_value", "registry_scanner_dynamic_group_ocid", "serverless_scanner_dynamic_group_ocid", "ads_scanner_dynamic_group_ocid", "dspm_scanner_dynamic_group_ocid", "proxy_dynamic_group_ocid"], "properties": {"object_storage_namespace": {"type": "string"}, "registry_scanner_dynamic_group_tag_value": {"type": "string"}, "serverless_scanner_dynamic_group_tag_value": {"type": "string"}, "ads_scanner_dynamic_group_tag_value": {"type": "string"}, "dspm_scanner_dynamic_group_tag_value": {"type": "string"}, "proxy_dynamic_group_tag_value": {"type": "string"}, "registry_scanner_dynamic_group_ocid": {"type": "string"}, "serverless_scanner_dynamic_group_ocid": {"type": "string"}, "ads_scanner_dynamic_group_ocid": {"type": "string"}, "dspm_scanner_dynamic_group_ocid": {"type": "string"}, "proxy_dynamic_group_ocid": {"type": "string"}}}, "regions": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["name", "regional_resources"], "properties": {"name": {"type": "string", "examples": ["af-johannesburg-1", "ap-melbourne-1"]}, "regional_resources": {"type": "object", "required": ["communication_object_storage_bucket_name", "artifact_object_storage_bucket_name", "communication_stream_endpoint", "communication_stream_id", "scanners_network_security_group_id", "scanners_subnet_id", "availability_domains", "egress_network_security_group_id", "reserved_public_ip", "kms_vault_id", "kms_key_id"], "properties": {"communication_object_storage_bucket_name": {"type": "string", "examples": ["communication-qa2-test-9992788762347"]}, "artifact_object_storage_bucket_name": {"type": "string", "examples": ["artifact-qa2-test-9992788762347"]}, "communication_stream_endpoint": {"type": "string", "format": "uri"}, "communication_stream_id": {"type": "string", "examples": ["ocid1.stream.oc1.phx.amaaaaaafc5kvwqapck7x3iuvffnuwfip4ukk3wo3slcronmzbkf5i2njyrq"]}, "scanners_network_security_group_id": {"type": "string"}, "scanners_subnet_id": {"type": "string"}, "availability_domains": {"type": "array", "description": "Availability domains for each region", "items": {"type": "string"}}, "egress_network_security_group_id": {"type": "string", "description": "The proxy egress NSG ID"}, "reserved_public_ip": {"type": "string", "description": "The reserved public IP to be used on the NAT Gateway"}, "kms_vault_id": {"type": "string", "examples": ["ocid1.vault.oc1.af-johannesburg-1.hvuid3bwaaa5c.abvg4ljr6nx3557eo5k27znjtcrhapnmg3yia3obqfs7tsylqrkqnte7taua"]}, "kms_key_id": {"type": "string", "examples": ["ocid1.key.oc1.af-johannesburg-1.hvuid3bwaaa5c.abvg4ljrqgsak5kkmup52m4x7hje67c54kcrg345dvyw4qj3gkasfbgtxdzq"]}}}}}}}, "$schema": "http://json-schema.org/schema#"}]}}, "$schema": "http://json-schema.org/schema#", "definitions": {"instanceProfileArn": {"type": "string", "pattern": "^arn:aws:iam::\\d{12}:instance-profile/[a-zA-Z0-9-_]+$", "examples": ["arn:aws:iam::************:instance-profile/CortexOutpostADSScannerEC2InstanceProfile", "arn:aws:iam::************:instance-profile/CortexOutpostDSPMScannerEC2InstanceProfile"]}, "roleArn": {"type": "string", "pattern": "^arn:aws:iam::\\d{12}:role/[a-zA-Z0-9-_]+$", "examples": ["arn:aws:iam::************:role/ads-scanner-role", "arn:aws:iam::************:role/dspm-scanner-role"]}}}