package api

import (
	"context"
	"fmt"
	"log"
	"os"
	"path"
	"strings"
	"sync"
	"time"

	"go.uber.org/multierr"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"

	fluxmeta "github.com/fluxcd/pkg/apis/meta"
	fluxchartutil "github.com/fluxcd/pkg/chartutil"
	"github.com/go-logr/logr"
	"github.com/homeport/dyff/pkg/dyff"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/cmd/cli/cliutils/dyffwrapper"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/cmd/cli/cliutils/k8s"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/cmd/cli/shared"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/chart"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/logger"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/models"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/types"
	"helm.sh/helm/v3/cmd/helm/require"
	"helm.sh/helm/v3/pkg/chartutil"
	"helm.sh/helm/v3/pkg/engine"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/cli-runtime/pkg/genericclioptions"
	"k8s.io/cli-runtime/pkg/resource"
	"sigs.k8s.io/controller-runtime/pkg/client"
	ctrllog "sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	humanOutput string = "human"
	jsonOutput  string = "json"
)

var (
	diffAll             bool
	diffChartName       string
	diffOutput          string
	diffOutputFile      string
	diffProjectType     string
	diffReportRulePaths []string
	requestedNamespace  string
)

var DiffCmd = &cobra.Command{
	Use:   "diff [chart-name]",
	Short: "find and print the diff between rendered and cluster",
	Long:  `..`,
	Args:  require.MaximumNArgs(1),

	Run: func(cmd *cobra.Command, args []string) {
		if len(args) == 1 {
			diffChartName = args[0]
		}

		if err := Diff(); err != nil {
			log.Fatalf("error: %v", err)
		}
	},
}

func init() {
	DiffCmd.Flags().StringVarP(&diffOutput, "output", "o", "human",
		"report output format i.e: human|json")
	DiffCmd.Flags().StringVarP(&diffOutputFile, "output-file", "f", "diff.json", "report output file, default: diff.json")
	DiffCmd.Flags().StringVarP(&requestedNamespace, "namespace", "n", "", "get objects in a single namespace. defaults to all namespaces (and to the ST namespace for metro tenants)")
	DiffCmd.Flags().BoolVarP(&diffAll, "all", "A", false, "diff all projects charts (it overrides -p|--project-type)")
	DiffCmd.Flags().StringVarP(&diffProjectType, "project-type", "p", "tenant",
		"specify chart project type context, i.e: tenant|engine|app-hub")

	DiffCmd.Flags().StringSliceVarP(
		&diffReportRulePaths,
		"rule-paths",
		"r",
		[]string{dyffwrapper.DefaultRulePath},
		fmt.Sprintf("specify report rules path to apply, default: %s", dyffwrapper.DefaultRulePath),
	)
	DiffCmd.MarkFlagRequired("tenant-path")
}

func Diff() error {

	customerData, err := chart.NewCustomerData(shared.TenantPath, shared.LoadBaseRuntimeFamilies)
	if err != nil {
		return err
	}

	projectType := models.ProjectType(diffProjectType)
	var id string
	var gcpConfig *models.GCPConfig
	isMetroHost := customerData.MetroConfig != nil
	if isMetroHost {
		id = customerData.MetroConfig.Metro.Id
		gcpConfig = models.GetMetroGCPConfigForProjectType(customerData.MetroConfig)
		requestedNamespace = customerData.MetroConfig.Globals["metro_namespace"].(string)
	} else {
		id = customerData.CustomerConfig.Tenant.LcaasID
		if gcpConfig, err = models.GetGCPConfigForProjectType(projectType, customerData.CustomerConfig); err != nil {
			return err
		}
	}

	if isMetroHost {
		switch projectType {
		case "metro":
			err = diff(customerData, gcpConfig, id)
		default:
			if customerData.MetroConfig.AllInOne {
				if e := diff(customerData, gcpConfig, id); e != nil {
					err = multierr.Append(err, fmt.Errorf("metro host helm diff error: %s", e))
				}
				for lcaas_id, cd := range customerData.ResidentsCustomerData {
					requestedNamespace = cd.CustomerConfig.Globals["st_namespace"].(string)
					fmt.Printf("# lcaas_id=%s helm diff\n", lcaas_id)
					if e := diff(cd, gcpConfig, id); e != nil {
						err = multierr.Append(err, fmt.Errorf("lcaas_id=%s helm diff error: %s", lcaas_id, e))
					}
				}
			}
		}
	} else {
		err = diff(customerData, gcpConfig, id)
	}
	return err
}

func diff(customerData *chart.CustomerData, gcpConfig *models.GCPConfig, id string) error {
	log := logger.InitLogger()
	var customerConfigData types.CustomerData
	var err error
	if customerData.MetroConfig == nil {
		customerConfigData, err = customerData.CustomerConfig.CustomerConfigData()
	} else {
		customerConfigData, err = customerData.MetroConfig.ToMap()
	}
	if err != nil {
		return err
	}

	reportRuleEngine, err := dyffwrapper.NewRuleEngine(diffReportRulePaths, customerConfigData)
	if err != nil {
		return err
	}

	var report *dyff.Report
	switch {
	case diffAll && diffChartName != "":
		return fmt.Errorf("conflicts, --all|-A cannot be compared with a single chart, given: %s", diffChartName)
	case diffAll:
		if requestedNamespace == "" {
			log.Infof("diff will be generated for all charts in all project types")
		}

		var projectsCharts *chart.ProjectsCharts
		if projectsCharts, err = chart.NewProjectsCharts(shared.ChartsRootPath, customerData); err != nil {
			break
		}

		report, err = allChartsDiffReport(gcpConfig, projectsCharts, customerData, requestedNamespace)
	case diffChartName != "":
		report, err = chartDiffReport(diffChartName, customerData, gcpConfig)
	default:
		log.Infof("diff will be generated for all charts in %s project type", diffProjectType)
		projectType := models.ProjectType(diffProjectType)

		var projectsCharts *chart.ProjectsCharts
		if projectsCharts, err = chart.NewSingleProjectCharts(projectType, shared.ChartsRootPath, customerData); err != nil {
			break
		}

		report, err = allChartsDiffReport(gcpConfig, projectsCharts, customerData, requestedNamespace)
	}

	if err != nil {
		return err
	}

	switch diffOutput {
	case humanOutput:
		err = printHumanReadableReport(id, report, reportRuleEngine)
	case jsonOutput:
		err = saveJsonReport(id, report, reportRuleEngine, diffOutputFile)
	}

	if err != nil {
		return err
	}

	return nil
}

func saveJsonReport(id string, report *dyff.Report, reportRuleEngine *dyffwrapper.RuleEngine, path string) error {
	reportFile, err := os.Create(path)
	if err != nil {
		return err
	}

	defer func() {
		if err := reportFile.Close(); err != nil {
			log.Fatal(err)
		}
	}()

	jsonReport, err := dyffwrapper.NewJsonReport(id, report, reportRuleEngine)
	if err != nil {
		return err
	}

	return jsonReport.WriteReport(reportFile)
}

func printHumanReadableReport(id string, report *dyff.Report, reportRuleEngine *dyffwrapper.RuleEngine) error {
	humanRepot, err := dyffwrapper.NewHumanReport(id, report, reportRuleEngine)
	if err != nil {
		return err
	}

	return humanRepot.WriteReport(log.Writer())
}

func chartDiffReport(chartName string, customerData *chart.CustomerData, gcpConfig *models.GCPConfig) (*dyff.Report, error) {
	var err error
	log := logger.InitLogger()
	projectType := models.ProjectType(diffProjectType)

	log.Infof("diff will be generated for chart %s in %s project type", chartName, diffProjectType)

	var restClientGetter genericclioptions.RESTClientGetter
	if restClientGetter, err = k8s.GetGoogleRESTClientGetter(gcpConfig); err != nil {
		return nil, err
	}

	capabilities, err := k8s.GetClusterCapabilities(restClientGetter)
	if err != nil {
		return nil, err
	}

	builder := k8s.NewKubeBuilder(restClientGetter).Flatten()

	chartPath := path.Join(shared.ChartsRootPath, projectType.String(), chartName)
	chartObject, err := chart.NewChart(chartPath, projectType, customerData)
	if err != nil {
		return nil, err
	}

	templates, releaseNamespace, err := RenderChartTemplates(projectType, chartObject, capabilities, customerData, restClientGetter)
	if err != nil {
		return nil, err
	}

	for name, template := range templates {
		reader := strings.NewReader(template)
		builder = builder.Stream(reader, name)
	}

	liveInput := dyffwrapper.NewInputFile("live")
	mergedInput := dyffwrapper.NewInputFile("merged")

	visitorFunc := func(info *resource.Info, err error) error {
		rawObject := info.Object.DeepCopyObject()

		if info.Namespace == "" && info.Namespaced() {
			if info.Namespace, err = k8s.SetNamespaceIfOmitted(rawObject, releaseNamespace); err != nil {
				return errors.Wrapf(err, "add object metadata.namespace or .Release.Namespace")
			}
		}

		objectName := fmt.Sprintf("%s/%s", projectType, k8s.GetObjectName(rawObject))

		kubeHelper := k8s.NewKubeHelper(info.Client, info.Mapping)

		liveObject, err := kubeHelper.FetchLiveObject(info)
		if err != nil {
			return err
		}

		diffObject, err := kubeHelper.FetchDiffObject(liveObject, rawObject)
		if err != nil {
			return err
		}

		if err = liveInput.AppendObject(objectName, diffObject.Live); err != nil {
			return err
		}

		if err = mergedInput.AppendObject(objectName, diffObject.Merged); err != nil {
			return err
		}

		return nil
	}

	if err = builder.Do().Visit(visitorFunc); err != nil {
		return nil, err
	}

	report, err := dyff.CompareInputFiles(liveInput.InputFile, mergedInput.InputFile, dyff.IgnoreOrderChanges(false), dyff.IgnoreWhitespaceChanges(true), dyff.KubernetesEntityDetection(false))
	if err != nil {
		return nil, err
	}

	return &report, nil
}

func RenderChartTemplates(projectType models.ProjectType, chartObject *chart.ChartImpl, capabilities *chartutil.Capabilities, customerData *chart.CustomerData, restClientGetter genericclioptions.RESTClientGetter) (map[string]string, string, error) {
	// Initialize controller-runtime logger to suppress warning
	ctrllog.SetLogger(logr.Discard())

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()

	helmChart := chartObject.GetChart()

	fluxHelmRelease, err := customerData.GetFluxRelease(projectType, chartObject)
	if err != nil {
		return nil, "", err
	}

	restConfig, err := restClientGetter.ToRESTConfig()
	if err != nil {
		return nil, "", err
	}

	releaseNamespace := fluxHelmRelease.GetReleaseNamespace()

	var valuesRefs []fluxmeta.ValuesReference
	for _, source := range fluxHelmRelease.Spec.ValuesFrom {
		valuesRefs = append(
			valuesRefs,
			fluxmeta.ValuesReference{
				Kind:       source.Kind,
				Name:       source.Name,
				ValuesKey:  source.ValuesKey,
				TargetPath: source.TargetPath,
				Optional:   source.Optional,
			},
		)
	}

	kubeClient, err := client.NewWithWatch(restConfig, client.Options{})
	if err != nil {
		return nil, "", err
	}

	valuesOverrides, err := fluxchartutil.ChartValuesFromReferences(
		ctx, logr.Discard(), kubeClient, releaseNamespace, fluxHelmRelease.GetValues(), valuesRefs...,
	)

	if err != nil {
		return nil, "", err
	}

	if err := chartutil.ProcessDependenciesWithMerge(helmChart, valuesOverrides); err != nil {
		return nil, "", err
	}

	chartValues, err := chartutil.MergeValues(helmChart, valuesOverrides)
	if err != nil {
		return nil, "", err
	}

	releaseValues := chartutil.Values{
		"Values":       chartValues,
		"Chart":        helmChart.Metadata,
		"Capabilities": capabilities,
		"Release": chartutil.Values{
			"Name":      fluxHelmRelease.GetReleaseName(),
			"Namespace": releaseNamespace,
			"Service":   "Helm",
		},
	}

	templates, err := engine.RenderWithClient(helmChart, releaseValues, restConfig)
	if err != nil {
		return nil, "", err
	}

	for _, crd := range helmChart.CRDObjects() {
		templates[crd.Filename] = string(crd.File.Data)
	}

	for name, template := range templates {
		templates[name] = strings.TrimSpace(template)
	}

	return templates, releaseNamespace, nil
}

func allChartsDiffReport(gcpConfig *models.GCPConfig, projectsCharts *chart.ProjectsCharts, customerData *chart.CustomerData, requestedNamespace string) (*dyff.Report, error) {
	if err := projectsCharts.LoadCharts(); err != nil {
		return nil, err
	}

	projectsDiffHelper, err := NewDiffHelper(gcpConfig, projectsCharts, customerData)
	if err != nil {
		return nil, err
	}

	if err = projectsDiffHelper.AsyncRenderAllChartsRawObjectsByName(); err != nil {
		return nil, err
	}

	if err = projectsDiffHelper.AsyncFetchAllClustersDiffAndOrphanObjectsByName(requestedNamespace); err != nil {
		return nil, err
	}

	liveInput := dyffwrapper.NewInputFile("live")
	mergedInput := dyffwrapper.NewInputFile("merged")

	for objectName, diffObject := range projectsDiffHelper.GetDiffObjectsByName() {
		if err := liveInput.AppendObject(objectName, diffObject.Live); err != nil {
			return nil, err
		}

		if err := mergedInput.AppendObject(objectName, diffObject.Merged); err != nil {
			return nil, err
		}
	}

	rawObjectNames := projectsDiffHelper.GetRawObjectNames()
	managedObjectNames := projectsDiffHelper.GetManagedObjectNames()
	orphanedObjectNames := projectsDiffHelper.GetOrphanedObjectNames()

	if err = liveInput.AppendObject(dyffwrapper.ManagedObjectNamesPath, managedObjectNames); err != nil {
		return nil, err
	}

	if err = mergedInput.AppendObject(dyffwrapper.ManagedObjectNamesPath, rawObjectNames); err != nil {
		return nil, err
	}

	if err = liveInput.AppendObject(dyffwrapper.OrphanedObjectNamesPath, orphanedObjectNames); err != nil {
		return nil, err
	}

	if err = mergedInput.AppendObject(dyffwrapper.OrphanedObjectNamesPath, []string{}); err != nil {
		return nil, err
	}

	report, err := dyff.CompareInputFiles(liveInput.InputFile, mergedInput.InputFile, dyff.IgnoreOrderChanges(false), dyff.IgnoreWhitespaceChanges(true))
	if err != nil {
		return nil, err
	}

	return &report, nil
}

type ProjectsDiffHelper struct {
	rawObjectsByName              *sync.Map
	diffObjectsByName             *sync.Map
	managedObjectsByName          *sync.Map
	orphanedObjectsByName         *sync.Map
	customerData                  *chart.CustomerData
	projectsCharts                *chart.ProjectsCharts
	restClientGetterByProductType map[models.ProjectType]genericclioptions.RESTClientGetter
}

func NewDiffHelper(gcpConfig *models.GCPConfig, projectsCharts *chart.ProjectsCharts, customerData *chart.CustomerData) (*ProjectsDiffHelper, error) {
	var err error

	helper := &ProjectsDiffHelper{
		customerData:                  customerData,
		projectsCharts:                projectsCharts,
		rawObjectsByName:              new(sync.Map),
		diffObjectsByName:             new(sync.Map),
		managedObjectsByName:          new(sync.Map),
		orphanedObjectsByName:         new(sync.Map),
		restClientGetterByProductType: make(map[models.ProjectType]genericclioptions.RESTClientGetter),
	}

	for projectType := range projectsCharts.GetCharts() {
		var gcpConfig *models.GCPConfig
		if customerData.MetroConfig == nil {
			gcpConfig = &models.GCPConfig{
				Region:         customerData.CustomerConfig.Region.GCPRegion,
				IsRegionalDisk: customerData.CustomerConfig.InfraFf.IsEnableMultiZonedNodepools,
			}
			if gcpConfig.Project, gcpConfig.Zone, err = models.GetProjectIDAndZoneForProjectType(projectType, customerData.CustomerConfig); err != nil {
				return nil, err
			}
		} else {
			gcpConfig = models.GetMetroGCPConfigForProjectType(customerData.MetroConfig)
		}

		if helper.restClientGetterByProductType[projectType], err = k8s.GetGoogleRESTClientGetter(gcpConfig); err != nil {
			return nil, err
		}
	}

	return helper, nil
}

func (helper *ProjectsDiffHelper) GetDiffObjectsByName() map[string]*k8s.DiffObject {
	diffObjectByName := make(map[string]*k8s.DiffObject, 0)

	helper.diffObjectsByName.Range(func(key, value any) bool {
		objectName := key.(string)
		diffObject := value.(*k8s.DiffObject)
		diffObjectByName[objectName] = diffObject
		return true
	})

	return diffObjectByName
}

func (helper *ProjectsDiffHelper) GetRawObjectNames() []string {
	return helper.getObjectNames(helper.rawObjectsByName)
}

func (helper *ProjectsDiffHelper) GetManagedObjectNames() []string {
	return helper.getObjectNames(helper.managedObjectsByName)
}

func (helper *ProjectsDiffHelper) GetOrphanedObjectNames() []string {
	return helper.getObjectNames(helper.orphanedObjectsByName)
}

func (helper *ProjectsDiffHelper) getObjectNames(objectsMap *sync.Map) []string {
	objectNames := make([]string, 0)

	objectsMap.Range(func(key, value any) bool {
		objectName := key.(string)
		objectNames = append(objectNames, objectName)
		return true
	})

	return objectNames
}

func (helper *ProjectsDiffHelper) AsyncRenderAllChartsRawObjectsByName() error {
	var group errgroup.Group

	for projectType, projectCharts := range helper.projectsCharts.GetCharts() {
		restClientGetter := helper.restClientGetterByProductType[projectType]

		capabilities, err := k8s.GetClusterCapabilities(restClientGetter)
		if err != nil {
			return err
		}

		for _, chartObject := range projectCharts.Charts() {
			func(chartObject *chart.ChartImpl) {
				group.Go(
					func() error {
						return helper.asyncRenderChartTemplates(projectType, chartObject, capabilities, restClientGetter)
					},
				)
			}(chartObject)
		}

		if err := group.Wait(); err != nil {
			return err
		}
	}

	return nil
}

func (helper *ProjectsDiffHelper) asyncRenderChartTemplates(projectType models.ProjectType, chartObject *chart.ChartImpl, capabilities *chartutil.Capabilities, restClientGetter genericclioptions.RESTClientGetter) error {
	if chartObject.GetChart().Metadata.Type == "library" {
		return nil
	}

	templates, releaseNamespace, err := RenderChartTemplates(projectType, chartObject, capabilities, helper.customerData, restClientGetter)
	if err != nil {
		return err
	}

	rawBuilder := k8s.NewKubeBuilder(restClientGetter).Flatten()

	for name, template := range templates {
		reader := strings.NewReader(template)
		rawBuilder = rawBuilder.Stream(reader, name)
	}

	visitorFunc := func(info *resource.Info, err error) error {
		if err != nil {
			return err
		}

		if info.Namespace == "" && info.Namespaced() {
			if info.Namespace, err = k8s.SetNamespaceIfOmitted(info.Object, releaseNamespace); err != nil {
				return errors.Wrapf(err, "add object metadata.namespace or .Release.Namespace")
			}
		}

		objectName := fmt.Sprintf("%s/%s", projectType, k8s.GetObjectName(info.Object))
		helper.rawObjectsByName.Store(objectName, info.Object)

		return nil
	}

	if err = rawBuilder.Do().Visit(visitorFunc); err != nil {
		return err
	}

	return nil
}

func (helper *ProjectsDiffHelper) AsyncFetchAllClustersDiffAndOrphanObjectsByName(namespace string) error {

	for projectType, restClientGetter := range helper.restClientGetterByProductType {
		projectTypeName := projectType.String()

		orphanedVisitorFunc := func(info *resource.Info, err error) error {
			if err != nil {
				return err
			}

			if namespace != "" && info.Namespace != namespace {
				return nil
			}

			objects, err := meta.ExtractList(info.Object)
			if err != nil {
				return err
			}

			gvk := info.Mapping.GroupVersionKind

			for _, liveObject := range objects {
				liveObject.GetObjectKind().SetGroupVersionKind(gvk)
				objectName := fmt.Sprintf("%s/%s", projectTypeName, k8s.GetObjectName(liveObject))

				if secret, ok := liveObject.(*corev1.Secret); ok {
					if secret.Type == "helm.sh/release.v1" {
						continue
					}
				}

				if value, exist := helper.rawObjectsByName.LoadAndDelete(objectName); exist {
					rawObject := value.(runtime.Object)
					kubeHelper := k8s.NewKubeHelper(info.Client, info.Mapping)

					diffObject, err := kubeHelper.FetchDiffObject(liveObject, rawObject)
					if err != nil {
						return err
					}

					helper.diffObjectsByName.Store(objectName, diffObject)
				}

				if k8s.IsManagedObject(liveObject) {
					helper.orphanedObjectsByName.Store(objectName, liveObject)
				}
			}

			return nil
		}

		managedVisitorFunc := func(info *resource.Info, err error) error {
			if err != nil {
				return err
			}
			if namespace != "" && info.Namespace != namespace {
				return nil
			}

			objects, err := meta.ExtractList(info.Object)
			if err != nil {
				return err
			}

			gvk := info.Mapping.GroupVersionKind

			for _, liveObject := range objects {
				for _, refName := range k8s.ExtractObjectRefs(liveObject) {
					refName = fmt.Sprintf("%s/%s", projectTypeName, refName)
					if refObject, exist := helper.orphanedObjectsByName.LoadAndDelete(refName); exist {
						helper.managedObjectsByName.Store(refName, refObject)
					}
				}

				liveObject.GetObjectKind().SetGroupVersionKind(gvk)
				objectName := fmt.Sprintf("%s/%s", projectTypeName, k8s.GetObjectName(liveObject))

				if value, exist := helper.rawObjectsByName.LoadAndDelete(objectName); exist {
					rawObject := value.(runtime.Object)
					kubeHelper := k8s.NewKubeHelper(info.Client, info.Mapping)

					diffObject, err := kubeHelper.FetchDiffObject(liveObject, rawObject)
					if err != nil {
						return err
					}

					helper.diffObjectsByName.Store(objectName, diffObject)
					continue
				}

				if k8s.IsManagedObject(liveObject) {
					helper.managedObjectsByName.Store(objectName, liveObject)
				}
			}

			return nil
		}

		if err := k8s.AsyncFetchAllObjectsByResourceTypes(namespace, k8s.ReferencedResourceTypes, restClientGetter, orphanedVisitorFunc); err != nil {
			return err
		}

		if err := k8s.AsyncFetchAllObjectsByResourceTypes(namespace, k8s.ManagedResourcesTypes, restClientGetter, managedVisitorFunc); err != nil {
			return err
		}
	}

	return nil
}
