_component_name: "'dspm-x-files-skinner'"
_component_image: "'dspm-x-files-skinner'"

fullnameOverride: "local._component_name"
namespaceOverride: "globals.metro_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    # TODO:careful
    cpu: '"200m"'
    memory: '"2Gi"'
  limits:
    cpu: '"3"'
    memory: '"2Gi"'

# Env vars that are required by SDKs. Keep at minumum.
env:
  AWS_DEFAULT_REGION: "'eu-central-1'"

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    TEMPORAL_URL: "globals.temporal_frontend_address"
    CORTEX_BASE_PATH: "'http://' + globals.env.CORTEX_PLATFORM_URI"
    EGRESSPROXY_URL: "'************:'+ globals.egress_proxy_port"
    EGRESSPROXY_CA_PATH: "'/etc/certs/egress.crt'"
    EGRESS_PROXY_HOST: "'************'"
    EGRESS_PROXY_PORT: "globals.egress_proxy_port"
    TEMPORAL_QUEUE_NAME: "'x_files_queue'"
    TEMPORAL_NAMESPACE_X_FILES: "'dspm-x-files'"
    TEMPORAL_TLS: false
    WORKERS: 5
    AWS_CA_BUNDLE: "'/etc/certs/egress.crt'"
    IS_METRO: true
    CLOUDONBOARDING_AZURE_STORAGE_ALLOWED_IPS: "'**************,*************'"
    CORTEX_PLATFORM_LOCAL_SERVICE_AUDIENCE: "'cortex.platform.local'"
    CORTEX_PLATFORM_SERVICE_AUDIENCE: "'cortex.platform'"
    CORTEX_PLATFORM_URI: "'http://' + globals.env.CORTEX_PLATFORM_URI"
    # CORTEX_PROJECT_ID: "metro.project_id"
    DIG_INFRA_CLOUD: "'GCP'"
    DIG_INFRA_PRODUCT: "'CORTEX'"
    DSPM_DT_DEBUG: false
    REDIS_HOST: "globals.dspm_redis_address"
    REDIS_PORT: 6379
    # DB_URL: "'mongodb://root:<EMAIL>'"
    DB_URL:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'mongodb_uri_cwp'"
          optional: false

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
        
        "dataFrom": [
          .tenant.lcaas_id + "-configmap",
          .tenant.lcaas_id + "-configmap-feature-flags",
          .tenant.lcaas_id + "-configmap-dspm-x-files",
          .tenant.lcaas_id + "-configmap-dspm-x-files-feature-flags"
        ],

        "data": {
          "LCAAS_ID" : .tenant.lcaas_id,
          "X_FILES_BUCKET" : .tenant.project_id + "-dspm-listing-data",
          "DB_NAME": "dspm-x-files-" + .tenant.lcaas_id
        }
      }
    ))

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: "'********'"
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: "globals.env.TEMPORAL_URL"
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: "'********'"
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: "globals.env.TEMPORAL_URL"
      - name: "'TEMPORAL_NAMESPACE'"
        value: "'dspm-x-files'"

volumes:
  - name: '"api-certs-dspm"'
    secret:
      defaultMode: 186
      optional: false
      secretName: globals.metro_secrets

volumeMounts:
  - name: "'api-certs-dspm'"
    mountPath: "'/etc/certs/egress.crt'"
    mountPropagation: "'None'"
    readOnly: true
    subPath: "'egress.crt'"

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true

debug:
  globals: globals
