apiVersion: batch/v1
kind: CronJob
metadata:
  {{- with .Values.cronjob.annotations }}
  annotations: {{ toYaml . | nindent 4 }}
    {{- include "common.sre.panw.annotations" $ | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" $ | nindent 4 }}
    app: {{ printf "%s" .Values.fullnameOverride }}
    {{- with .Values.cronjob.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ printf "%s" .Values.fullnameOverride }}
  namespace: {{ include "common.namespace" . }}
spec:
  schedule: {{ .Values.cronjob.schedule | quote }}
  {{- with .Values.cronjob.concurrencyPolicy }}
  concurrencyPolicy: {{ . }}
  {{- end }}
  {{- with .Values.cronjob.successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ . | int }}
  {{- end }}
  {{- with .Values.cronjob.startingDeadlineSeconds }}
  startingDeadlineSeconds: {{ . | int }}
  {{- end }}
  jobTemplate:
    metadata:
      annotations:
        {{- include "common.sre.panw.annotations" $ | nindent 8 }}
      labels:
        {{- include "common.sre.panw.labels" $ | nindent 8 }}
        cronjob: {{ printf "%s" .Values.fullnameOverride }}
    spec:
      {{- with .Values.cronjob.activeDeadlineSeconds }}
      activeDeadlineSeconds: {{ . | int }}
      {{- end }}
      {{- if hasKey .Values.cronjob "backoffLimit" }}
      backoffLimit: {{ .Values.cronjob.backoffLimit | int }}
      {{- end }}
      {{- with .Values.cronjob.completions }}
      completions: {{ . | int }}
      {{- end }}
      {{- with .Values.cronjob.parallelism }}
      parallelism: {{ . | int }}
      {{- end }}
      {{- if hasKey .Values.cronjob "ttlSecondsAfterFinished" }}
      ttlSecondsAfterFinished: {{ .Values.cronjob.ttlSecondsAfterFinished | int }}
      {{- end }}
      {{- if .Values.cronjob.suspend }}
      suspend: true
      {{- end }}
      template:
        metadata:
          {{- with .Values.podAnnotations }}
          annotations: {{ toYaml . | nindent 12 }}
            {{- include "common.sre.panw.annotations" $ | nindent 12 }}
          {{- end }}
          labels:
            {{- include "common.sre.panw.labels" $ | nindent 12 }}
            app: {{ printf "%s" .Values.appName }}
            app.kubernetes.io/name: {{ printf "%s" .Values.appName }}
            {{- with .Values.podLabels }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
        spec:
          containers:
            - name: {{ .Values.appName }}
              command:
                - ./spec-gate-job
              image: {{ printf "%s/%s:%s" .Values.image.registry .Values.image.repository .Values.image.tag }}
              imagePullPolicy: {{ default "IfNotPresent" .Values.image.pullPolicy }}
              {{- with .Values.ports }}
              ports: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.env }}
              env: {{ (include "common.renderEnv" .) | nindent 16 }}
              {{- end }}
              {{- with .Values.envFrom }}
              envFrom: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.startupProbe }}
              startupProbe: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.livenessProbe }}
              livenessProbe: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.readinessProbe }}
              readinessProbe: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.resources }}
              resources: {{ toYaml . | nindent 16 }}
              {{- end }}
              {{- with .Values.extraVolumeMounts }}
              volumeMounts: {{ toYaml . | nindent 16 }}
              {{- end }}
          automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
          {{- with .Values.priorityClassName }}
          priorityClassName: {{ . }}
          {{- end }}
          {{- with .Values.nodeSelector }}
          nodeSelector: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.affinity }}
          affinity: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.tolerations }}
          tolerations: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- range $constraint := . }}
            - {{ toYaml $constraint | nindent 4 | trim }}
              {{- if not $constraint.labelSelector }}
              labelSelector:
                matchLabels: {{ include "chart.selectorLabels" $ | nindent 8 }}
              {{- end }}
            {{- end }}
          {{- end }}
          {{- with .Values.restartPolicy }}
          restartPolicy: {{ . }}
          {{- end }}
          {{- with .Values.podSecurityContext }}
          securityContext: {{ toYaml . | nindent 12 }}
          {{- end }}
          serviceAccountName: {{ include "chart.serviceAccountName" . }}
          {{- with .Values.terminationGracePeriodSeconds }}
          terminationGracePeriodSeconds: {{ . | int }}
          {{- end }}
          {{- with .Values.extraVolumes }}
          volumes: {{ toYaml . | nindent 12 }}
          {{- end }}