apiVersion: v2
name: apisec-spec-gate-cron-job
description: A Helm chart for Kubernetes
type: application
version: 0.1.0
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../../common/common"
  - alias: mtConfig
    name: mt-config
    version: "*.*.*"
    repository: "file://../../common/mt-config"
  - alias: stConfig
    name: st-config
    version: "*.*.*"
    repository: "file://../../common/st-config"
appVersion: "0.1.0"
annotations:
  "panw.com/deploy-eval": "true"
  owner.panw/group: apisec
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/metro/apisec-spec-gate-cron-job
  owner.panw/team: phoenix
  owner.panw/team-slack-handle: '@waas-phoenix'
  owner.panw/people-slack-handle-team-lead: '@msharvit'
  owner.panw/people-slack-handle-owners-group: '@yhever'