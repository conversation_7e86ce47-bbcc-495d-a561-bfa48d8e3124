envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: []
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}
env:
  MONGODB_USER_NAME: "root"
  MONGODB_DATABASE_NAME: "APISec-{TENANT_ID}"
  GENERIC_IS_METRO: "true"

terminationGracePeriodSeconds: 30
automountServiceAccountToken: true

cronjob:
  annotations:
  labels: {}
  backoffLimit: 5
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  activeDeadlineSeconds: "3600"
  successfulJobsHistoryLimit: 4
  ttlSecondsAfterFinished: 1800
  parallelism: 1
  completions: 1
  suspend: false

restartPolicy: "OnFailure"

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: apisec
  team: nexus
podLabels:
  group: apisec
  team: nexus

serviceAccount:
  name: "apisec-spec-gate-cron-job"
  create: true
  automountServiceAccountToken: false

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8081
    scheme: HTTP
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 5

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /ready
    port: 8081
    scheme: "HTTP"
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /startup
    port: 8081
    scheme: "HTTP"
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

ports:
- containerPort: 8081
  protocol: TCP

nodeSelector:
  xdr-pool: "wi-dynamic"

podAnnotations: