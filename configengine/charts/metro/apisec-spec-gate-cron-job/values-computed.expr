_component_name: "'apisec-spec-service'"
_app_name: "'apisec-spec-gate'"
_cronjob_name: "local._app_name + '-cron-job'"

appName: local._app_name
fullnameOverride: local._cronjob_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._cronjob_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

env:
  GENERIC_use_legacy_config: false

envFrom:
  - configMapRef:
      name: '"configmap-apisec"'
      optional: false

# global configs to apply for /config/config.yaml
mtConfig:
  fullnameOverride: 'local._cronjob_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    GCP_PROJECT_ID: metro.project_id
    TENANT_ID: metro.id
    MONGODB_PASSWORD:
        valueFrom:
          secretKeyRef:
            name: globals.metro_secrets
            key: "'mongodb_password_cwp'"
            optional: false
  dataFrom:
    - '"common-config"'

# per tenant configs to apply for /tenants/tenantX.yaml
stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          "common-config",
          .tenant.lcaas_id + "-configmap-apisec"
        ],

        "data": {
          "GCP_PROJECT_ID": .tenant.project_id,
          "MONGODB_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .globals.tenant_secrets,
                "key": "mongodb_password_cwp",
                "optional": false
              }
            }
          },
          "TENANT_ID": .tenant.lcaas_id
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true

resources:
  limits:
    cpu: '"250m"'
    memory: '"2Gi"'
  requests:
    cpu: '"50m"'
    memory: '"1Gi"'