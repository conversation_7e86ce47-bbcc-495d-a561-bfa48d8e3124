apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: apisec-spec-cron-job
  namespace: "{{ .globals.metro_namespace }}"
spec:
  chart:
    spec:
      chart: metro/apisec-spec-cron-job
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
      interval: "{{ .globals.flux.chart_pull_interval }}"
  driftDetection:
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    disableWait: true
    createNamespace: true
    remediation:
      retries: -1
  interval: 1m0s
  targetNamespace: "{{ .globals.metro_namespace }}"
  rollback:
    disableWait: true
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
  values: {}