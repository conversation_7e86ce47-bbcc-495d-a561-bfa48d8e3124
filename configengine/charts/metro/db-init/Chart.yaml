apiVersion: v2
name: db-init
description: A Helm chart for managing DB via service binary
appVersion: "v0.1.0"
type: application
version: 0.1.0
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../../common/common"
  - alias: mtConfig
    name: mt-config
    version: "*.*.*"
    repository: "file://../../common/mt-config"
  - alias: stConfig
    name: st-config
    version: "*.*.*"
    repository: "file://../../common/st-config"
annotations:
  panw.com/deploy-eval: "true"
  owner.panw/group: platform
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/db-init
  owner.panw/team: platform
  owner.panw/team-slack-handle: '@platform-infra-owners'
  owner.panw/people-slack-handle-team-lead: '@gacohen'
  owner.panw/people-slack-handle-owners-group: '@ibialer'
