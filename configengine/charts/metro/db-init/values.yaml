envFrom: []
image: {}
affinity: {}
tolerations:
- key: xdr-pool
  operator: Equal
  value: "wi-static"
nodeSelector:
  xdr-pool: "wi-static"

extraVolumes: {}

priorityClassName: ""
topologySpreadConstraints: {}
automountServiceAccountToken: false

replicas: 1
hpa:
  enabled: false

terminationGracePeriodSeconds: 30

serviceAccount:
  create: true
  name: db-init-pod
  automountServiceAccountToken: false
  annotations: {}

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: platform
  team: platform
podLabels:
  group: platform
  team: platform
podAnnotations: {}

livenessProbe: {}
startupProbe: {}
readinessProbe: {}

podSecurityContext:
  fsGroup: 888
  runAsUser: 999
  runAsNonRoot: true

resources:
  requests:
    cpu: "10m"
    memory: "32Mi"
  limits:
    cpu: "50m"
    memory: "128Mi"

deployment:
  strategy:
    type: Recreate

service:
  enabled: false
  annotations: {}
