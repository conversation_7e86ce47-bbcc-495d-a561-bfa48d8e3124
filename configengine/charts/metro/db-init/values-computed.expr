_component_name: "'db-init'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  LOG_FORMAT: '"json"'

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    RECONCILE_POSTGRES_ENABLED: "'true'"
    PG_ADMIN_HOST: globals.env.PGHOST
    PG_ADMIN_PORT: "'5432'"
    PG_ADMIN_USER: "'root'"
    PG_ADMIN_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'postgres_password'"
          optional: false
    RECONCILE_MYSQL_ENABLED: "'true'"
    MYSQL_ADMIN_HOST: globals.env.MYSQL_MAIN_URI
    MYSQL_ADMIN_PORT: "'3306'"
    MYSQL_ADMIN_USER: "'root'"
    MYSQL_ADMIN_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'mysql_password'"
          optional: false

    RECONCILE_ELASTIC_ENABLED: "'true'"
    ELASTIC_ADMIN_HOST: '"https://" + globals.env.ELASTICSEARCH_URI'
    ELASTIC_ADMIN_PORT: "'9200'"
    ELASTIC_ADMIN_USER: "'elastic'"
    ELASTIC_INSECURE_SKIP_VERIFY: "'true'"
    ELASTIC_ADMIN_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'elastic_password'"
          optional: false

  dataFrom:
    - '"common-config"'

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {

          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",

          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          .tenant.lcaas_id + "-postgres",
          .tenant.lcaas_id + "-postgres-ps",
          .tenant.lcaas_id + "-mysql",
          .tenant.lcaas_id + "-mysql-ps",
          .tenant.lcaas_id + "-elastic",
          .tenant.lcaas_id + "-elastic-ps"
        ],
        "data": {
          "TENANT_ID": .tenant.lcaas_id,
          "TENANT_INDEX": .tenant.metro_tenant_index
        }
      }

    ))

volumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
  - name: '"mt-secrets"'
    mountPath: '"/secrets"'
    readOnly: true
  - name: '"mt-secrets"'
    mountPath: '"/etc/cert/egress.crt"'
    mountPropagation: '"None"'
    readOnly: true
    subPath: '"egress.crt"'


volumes:
  - name: '"config"'
    secret:
      secretName: 'local._component_name + "-config"'
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local._component_name + "-config"'
  - name: '"mt-secrets"'
    secret:
      defaultMode: 440
      optional: false
      secretName: globals.metro_secrets
