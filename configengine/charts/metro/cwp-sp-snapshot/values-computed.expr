_component_name: '"cwp-sp-snapshot"'

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '"50m"'
    memory: '"256Mi"'
  limits:
    cpu: '"1"'
    memory: '"1Gi"'

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  dataFrom:
    - '"common-config"'
    - '"cwp-global-config"'

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "data": {
          "SP_SNAPSHOT_POSTGRES_DB_NAME" : "cwp_" + .tenant.lcaas_id,
          "SP_SNAPSHOT_POSTGRES_DB_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres",
                "key": "POSTGRES_CWP_USERNAME",
                "optional": false
              }
            }
          },
          "POSTGRES_DB_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres-ps",
                "key": "POSTGRES_CWP_PASSWORD",
                "optional": false
              }
            }
          },
          "UAI_AGGREGATED_ASSETS_VIEW": .tenant.project_id + ".public_platform_" + .tenant.lcaas_id + ".aggregated_assets_view",
          "GCPCONF_PROJECT_ID": .tenant.project_id,
          "LOGGINGSERVICE_LCAAS_TENANT": .tenant.lcaas_id,
          "GENERIC_PRODUCT_TYPE": lower(.license.product_type),
        },

        "dataFrom": [
          .tenant.lcaas_id + "-configmap-cwp",
          .tenant.lcaas_id + "-configmap-cwp-feature-flags",
        ],
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true