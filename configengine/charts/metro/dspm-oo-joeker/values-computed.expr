_component_name: "'dspm-oo-joeker'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: 'true ? "100m" : "100m"'
    memory: 'true ? "512Mi" : "512Mi"'
  limits:
    cpu: 'true ? "300m" : "2"'
    memory: 'true ? "768Mi" : "768Mi"'

env:
  MAIN_FILE: '"joeker/main.py"'

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  # raw env vars & secret references
  data:
    MAIN_FILE: '"joeker/main.py"'
    SIS_INTERNAL_URL: '"http://classi-sis." + local.namespaceOverride + ".svc.cluster.local:8000"'
    JOSE_INTERNAL_URL: '"http://dspm-crespo-jose-svc." + local.namespaceOverride + ".svc.cluster.local"'
    BC_DISTRIBUTOR_BASE_URL: '"http://" + local.namespaceOverride + "-cwp-sp-bc-distributor-svc." + local.namespaceOverride + ".svc.cluster.local:8080"'
    POST_PROCESS_UNMANAGED_SNS_ARN: '""'
    POST_PROCESS_SIA_SNS_ARN: '""'
    CRESPO_PREPROCESS_SNS_ARN: '""'
    REPORTS_BUCKET: '""'
    REPORTS_BUCKET_REGION: '""'
    MONITORING_REPORTS_BUCKET: '"tmp"'
    MONITORING_REPORTS_BUCKET_REGION: '"tmp"'
    AZURE_CLIENT_ID: '"dummy"'
    GCP_PROJECT_ID: '"dspm-it-333611"'
    GCP_CREDENTIALS_PATH: '"/run/secrets/gcp_cred/cred.json"'
    DIG_ENV: '"dummy"'
    SERVICE_ACCOUNT: '"tmp"'
    AWS_STS_REGIONAL_ENDPOINTS: '"regional"'
    LISTING_DATA_BUCKET_REGION: '"dspm-it-333611"'
    PORT: '"8650"'
    DB_URL:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'mongodb_uri_cwp'"
          optional: false
    
  
  # values from references
  dataFrom:
    - '"common-config"'

# per tenant configs to apply for /tenants/tenantX.yaml
stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
        
        "dataFrom": [
          .tenant.lcaas_id + "-configmap",
          .tenant.lcaas_id + "-configmap-feature-flags",
          .tenant.lcaas_id + "-configmap-dspm",
          .tenant.lcaas_id + "-configmap-dspm-feature-flags",
        ],

        "data": {
          "LCAAS_ID" : .tenant.lcaas_id,
          "DB_NAME": "dspm-outpost-orchestrator-" + .tenant.lcaas_id,
          "LISTING_DATA_BUCKET": .tenant.project_id + "-dspm-listing-data",
          "PAYLOAD_BUCKET": .tenant.project_id + "-dspm_crespo_payload",
          "QUEUE_ID": "projects/" + .tenant.project_id + "/subscriptions/joeker-sub-" + .tenant.lcaas_id,

        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
  
debug:
  globals: globals