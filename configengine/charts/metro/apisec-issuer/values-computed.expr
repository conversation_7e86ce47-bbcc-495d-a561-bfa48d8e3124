_component_name: "'apisec-issuer'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

env:
  GENERIC_use_legacy_config: false

envFrom:
  - configMapRef:
      name: '"configmap-apisec"'
      optional: false

hpa:
  enabled: false

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    GCP_PROJECT_ID: metro.project_id
    TENANT_ID: metro.id
    APISEC_DSPM_CLASSIFICATION_SETTINGS_URL: '"http://dspm-dacs-dashboard-svc." + globals.metro_namespace + ".svc.cluster.local"'
  dataFrom:
    - '"common-config"'


stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          "common-config"
        ],

        "data": {
          "GCP_PROJECT_ID": .tenant.project_id,
          "TENANT_ID": .tenant.lcaas_id,
          "LOGGINGSERVICE_LCAAS_TENANT": .tenant.lcaas_id,
          "GENERIC_PRODUCT_TYPE": lower(.license.product_type)
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true

resources:
  limits:
    cpu: '"2"'
    memory: '"2Gi"'
  requests:
    cpu: '"50m"'
    memory: '"512Mi"'