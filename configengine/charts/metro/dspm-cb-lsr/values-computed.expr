_component_name: "'dspm-cb-lsr'"
_component_image: "'dspm-cb-listener'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  create: true
  name: local._component_name
  automountServiceAccountToken: false
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

replicas: "true ? 1 : 10"

# TODO: condition for dev resources in metro?
resources:
  requests:
    cpu: 'true ? "250m" : "250m"'
    memory: 'true ? "256Mi" : "256Mi"'
  limits:
    cpu: 'true ? "3" : "3"'
    memory: 'true ? "1Gi" : "2Gi"'

hpa:
  maxReplicas: "true ? 4 : 5"

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    DIG_INFRA_PRODUCT: '"CORTEX"'
    DIG_INFRA_CLOUD: '"GCP"'
    PROMETHEUS_PORT: 11011
    TEMPORAL_URL: 'globals.temporal_frontend_address'
    TEMPORAL_NAMESPACE_CB: '"dspm-cb"'

# per tenant configs to apply for /tenants/tenantX.yaml
stConfig:
  # TODO add feature flags config maps?
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
    
        "dataFrom": [],

        "data": {
          "COLUMN_LISTING_DATA_QUEUE_ID": "projects/" + .tenant.project_id + "/subscriptions/dspm-cb-column-listing-data-" + .tenant.lcaas_id + "-sub",
          "QUEUE_ID": "projects/" + .tenant.project_id + "/subscriptions/dspm-cb-listing-data-" + .tenant.lcaas_id + "-sub"
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true