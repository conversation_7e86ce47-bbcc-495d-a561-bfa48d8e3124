_component_name: "'dspm-x-files-momento'"
_component_image_momento: "'dspm-x-files-momento'"
_component_image_cb_worker: "'dspm-cb-listener'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  momento:
    component: "get(images.components, local._component_image_momento)"
    family: "get(images.families, local._images.momento.component.family)"
  cb_worker:
    component: "get(images.components, local._component_image_cb_worker)"
    family: "get(images.families, local._images.cb_worker.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.momento.component.tag ?? local._images.momento.family.tag"
  repository: "local._images.momento.component.repository ?? local._images.momento.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

replicas: "true ? 1 : 10"

# Configure containers with computed values
containers:
  dspm-x-files-momento:
    image:
      registry: "globals.gcr"
      tag: "local._images.momento.component.tag ?? local._images.momento.family.tag"
      repository: "local._images.momento.component.repository ?? local._images.momento.family.repository"
    resources:
      requests:
        cpu: 'true ? "150m" : "150m"'
        memory: 'true ? "1Gi" : "1Gi"'
      limits:
        cpu: 'true ? "3" : "3"'
        memory: 'true ? "2Gi" : "2Gi"'
    extraVolumeMounts:
      - name: '"config"'
        mountPath: '"/config"'
        readOnly: true
      - name: '"tenants"'
        mountPath: '"/tenants"'
        readOnly: true
  dspm-cb-worker:
    image:
      registry: "globals.gcr"
      tag: "local._images.cb_worker.component.tag ?? local._images.cb_worker.family.tag"
      repository: "local._images.cb_worker.component.repository ?? local._images.cb_worker.family.repository"
    resources:
      requests:
        cpu: 'true ? "150m" : "150m"'
        memory: 'true ? "256Mi" : "256Mi"'
      limits:
        cpu: 'true ? "1" : "3"'
        memory: 'true ? "1Gi" : "2Gi"'
    extraVolumeMounts:
      - name: '"config"'
        mountPath: '"/config"'
        readOnly: true
      - name: '"tenants"'
        mountPath: '"/tenants"'
        readOnly: true

volumes:
  - name: '"api-certs-dspm"'
    secret:
      defaultMode: 186
      optional: false
      secretName: globals.metro_secrets

initContainers:
  initTemporalMomento:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: "'********'"
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
  initTemporalCbWorker:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: "'********'"
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'

hpa:
  maxReplicas: 'region.viso_env == "dev" ? "4" : "14"'

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    CORTEX_PLATFORM_URI: '"http://" + globals.env.CORTEX_PLATFORM_URI'
    X_FILES_SKINNER_INTERNAL_URL: '"http://dspm-x-files-skinner-svc." + local.namespaceOverride + ".svc.cluster.local"'
    FDA_INTERNAL_URL: '"http://dspm-fda-api-svc." + globals.metro_namespace + ".svc.cluster.local:8899"'
    DIG_INFRA_PRODUCT: '"CORTEX"'
    DIG_INFRA_CLOUD: '"GCP"'
    PROMETHEUS_PORT: 11011
    TEMPORAL_URL: 'globals.temporal_frontend_address'
    TEMPORAL_NAMESPACE_CB: '"dspm-cb"'
    TEMPORAL_NAMESPACE_X_FILES: '"dspm-x-files"'
    TEMPORAL_QUEUE_NAME: "'x_files_queue'"
    MALWARE_DETECTION_URL: '"http://cwp-malware-detection-service-svc." + local.namespaceOverride + ".svc.cluster.local:8080"'
    AWS_CA_BUNDLE: '"/etc/certs/egress.crt"'
    ASSET_UPDATES_SNS: '""'
    WORKERS: "5"
    EGRESSPROXY_CA_PATH: '"/etc/certs/egress.crt"'
    EGRESSPROXY_URL: '"************:" + globals.egress_proxy_port'
    CORTEX_BASE_PATH: '"http://" + globals.env.CORTEX_PLATFORM_URI'
    REDIS_HOST: 'globals.dspm_redis_address'
    REDIS_PORT: "6379"
    REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'dspm_redis_password'"
          optional: false
    DB_URL:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'mongodb_uri_cwp'"
          optional: false

# per tenant configs to apply for /tenants/tenantX.yaml
stConfig:
  # TODO: add feature flags config maps?
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
    
        "dataFrom": [],

        "data": {
          "DB_NAME": "dspm-x-files-" + .tenant.lcaas_id,
          "FILE_OUTPUT_TOPIC": "projects/" + .tenant.project_id + "/topics/dspm-file-analysis-" + .tenant.lcaas_id,
          "ASSET_OUTPUT_TOPIC": "projects/" + .tenant.project_id + "/topics/dspm-asset-analysis-" + .tenant.lcaas_id,
          "COLUMN_ANALYSIS_OUTPUT_TOPIC": "projects/" + .tenant.project_id + "/topics/dspm-column-analysis-" + .tenant.lcaas_id,
          "LISTING_DATA_SNS": "projects/" + .tenant.project_id + "/topics/dspm-listing-data-" + .tenant.lcaas_id,
          "HEALTH_LOGS_TOPIC_ID": "projects/" + .tenant.project_id + "/topics/cloud-health-monitoring-" + .tenant.lcaas_id
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'