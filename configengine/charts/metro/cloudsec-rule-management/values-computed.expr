_component_name: "'rule-management'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: '"4c308f92b8d5a443be06d9f24365d7da6b047752"'
  repository: "local._images.component.repository ?? local._images.family.repository"

deployment:
  strategy:
    type: '"RollingUpdate"'
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

serviceAccount:
  create: true
  annotations:
    "iam.gke.io/gcp-service-account": 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '"200m"'
    memory: '"500Mi"'
  limits:
    cpu: "'2'"
    memory: '"4Gi"'

env:
  GENERIC_use_legacy_config: false
  GCP_PROJECT_ID: metro.project_id
  DB_USER: '"root"'
  DB_URL: globals.env.PGHOST
  DB_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: globals.metro_secrets
        key: '"postgres_password"'
        optional: false

hpa:
  enabled: false

nodeSelector:
  "xdr-pool": '"wi-dynamic"'

priorityClassName: '"high-priority-deployment"'

cronjob:
  schedule: '"17 0 * * *"'
  automountServiceAccountToken: false
  env:
    RULES_MANAGEMENT_CRON_JOB: '"true"'
    GENERIC_use_legacy_config: false
    DB_USER: '"root"'
    DB_URL: globals.env.PGHOST
    DB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: '"postgres_password"'
          optional: false

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace
  data:
    BQ_RETRY_MAX_ATTEMPTS: "10"
    RMS_INIT_VERSION: '"1.0.0"'
    CORTEX_PLATFORM_URL: '"platform.xdr-mt.svc.cluster.local"'
    PLATFORM_API_PORT: "8000"
    SEARCH_AUTO_SUGGEST_API_URL: '"search-and-investigate.xdr-mt.svc.cluster.local"'
    SEARCH_AUTO_SUGGEST_API_PORT: "8000"
    XSPM_API_URL: '"xspm-scanner.xdr-mt.svc.cluster.local"'
    XSPM_API_PORT: "8080"
    CIEM_API_URL: '"ciem-api-svc.xdr-mt.svc.cluster.local"'
    CIEM_API_PORT: "8080"
    GCPCONF_ENV_REGION: region.gcp_region
    DB_URL: globals.env.PGHOST
    RULE_MANAGEMENT_MT_BUCKET_NAME: 'region.viso_env == "dev" ? "rule-management-dev" : region.viso_env == "prod-gv" ? "rule-management-prod-gv" : region.viso_env == "prod-fr" ? "rule-management-prod-fr" : "rule-management-prod"'
    RULES_MIGRATION_WORKFLOW_PENDING_TIMEOUT: '"300"'
    RULES_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT: '"1800"'
    RULES_MIGRATION_WORKFLOW_INTERVAL: '"60"'
    EGRESSPROXY_URL: globals.egress_proxy_address
    EGRESSPROXY_CA_PATH: '"/etc/cert/egress.crt"'
  dataFrom:
    - '"common-config"'

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,
        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
        "dataFrom": [
          "common-config"
        ],
        "data": {
          "GCP_PROJECT_ID": string(.tenant.project_id),
          "GCPCONF_PROJECT_ID": string(.tenant.lcaas_id),
          "GENERIC_LCAAS_ID": string(.tenant.lcaas_id),
          "GCPCONF_ENV_REGION": .region.gcp_region,
          "GCPCONF_REAL_REGION": .region.gcp_region,
          "GCPCONF_REGION": .tenant.bq_location,
          "DB_NAME": "cspm_" + string(.tenant.lcaas_id),
          "DB_URL": .globals.env.PGHOST,
          "XDR_REDIS_HOST": "redis." + .globals.metro_namespace + ".svc.cluster.local",
          "XDR_REDIS_PORT": "6379",
          "DB_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres",
                "key": "POSTGRES_CSPM_USERNAME",
                "optional": false
              }
            }
          },
          "DB_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres-ps",
                "key": "POSTGRES_CSPM_PASSWORD",
                "optional": false
              }
            }
          },
          "XDR_REDIS_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-cloudsec-secrets",
                "key": "XDR_REDIS_PASSWORD",
                "optional": false
              }
            }
          }
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'
  - name: '"proxydome-certs"'
    secret:
      defaultMode: 444
      optional: false
      secretName: globals.metro_secrets
      items:
        - key: '"egress.crt"'
          path: '"egress.crt"'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
  - name: '"proxydome-certs"'
    mountPath: '"/etc/cert/"'
    readOnly: true
