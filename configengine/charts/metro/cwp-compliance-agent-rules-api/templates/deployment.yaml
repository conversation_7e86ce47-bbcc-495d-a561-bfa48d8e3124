apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with .Values.deploymentAnnotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
    {{- include "chart.labels" . | nindent 4 }}
    {{- with .Values.deploymentLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "chart.fullname" . }}
  namespace: {{ include "common.namespace" . }}
spec:
  {{- with (include "common.deployment.replicas" .) }}
  replicas: {{ . }}
  {{- end }}
  selector:
    matchLabels: {{ include "chart.selectorLabels" . | nindent 6 }}
  {{- with .Values.deployment.strategy }}
  strategy: {{ toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations: {{ toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "chart.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      enableServiceLinks: true
      shareProcessNamespace: false
      containers:
        - name: {{ include "chart.fullname" . }}
          image: {{ printf "%s/%s:%s" .Values.image.registry .Values.image.repository .Values.image.tag }}
          imagePullPolicy: {{ default "IfNotPresent" .Values.image.pullPolicy }}
          ports:
            - containerPort: 8080
              protocol: TCP
          {{- with .Values.env }}
          env: {{ (include "common.renderEnv" .) | nindent 12 }}
          {{- end }}
          {{- with .Values.envFrom }}
          envFrom: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.startupProbe }}
          startupProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.livenessProbe }}
          livenessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.containerSecurityContext }}
          securityContext: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.resources }}
          resources: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.extraVolumeMounts }}
          volumeMounts: {{ toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
        - {{ toYaml $constraint | nindent 10 | trim }}
          {{- if not $constraint.labelSelector }}
          labelSelector:
            matchLabels: {{ include "chart.selectorLabels" $ | nindent 14 }}
          {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.restartPolicy }}
      restartPolicy: {{ . }}
      {{- end }}
      {{- with .Values.podSecurityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      {{- with .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . | int }}
      {{- end }}
      volumes:
        {{- with .Values.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
