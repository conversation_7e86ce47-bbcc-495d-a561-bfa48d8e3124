apiVersion: v1
kind: Service
metadata:
  {{-  with .Values.service.annotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
    {{- include "chart.labels" . | nindent 4 }}
    {{- with .Values.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "chart.fullname" . }}-svc
  namespace: {{ include "common.namespace" . }}
spec:
  ports:
    - port: 8080
      protocol: TCP
      targetPort: 8080
  selector: {{ include "chart.selectorLabels" . | nindent 4 }}
  type: ClusterIP
