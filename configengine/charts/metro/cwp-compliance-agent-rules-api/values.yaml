envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
restartPolicy: ""
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}

terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

replicas: 1

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: cwp
  team: compliance
podLabels:
  group: cwp
  team: compliance
podAnnotations:
  prometheus.io/port: "8080"
  prometheus.io/scrape: "true"

serviceAccount:
  create: true
  automountServiceAccountToken: false

service:
  annotations: {}

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

nodeSelector:
  xdr-pool: "wi-dynamic"

deployment:
  strategy:
    type: "RollingUpdate"

hpa:
  enabled: false

vsg:
  enabled: false
