apiVersion: v2
name: cwp-compliance-agent-rules-api
description: A Helm chart for Kubernetes
type: application
version: 0.2.5
dependencies:
- name: common
  version: "*.*.*"
  repository: "file://../../common/common"
- alias: mtConfig
  name: mt-config
  version: "*.*.*"
  repository: "file://../../common/mt-config"
- alias: stConfig
  name: st-config
  version: "*.*.*"
  repository: "file://../../common/st-config"
appVersion: 0.2.0
annotations:
  owner.panw/team: compliance
  owner.panw/group: cwp
  panw.com/deploy-eval: "true"
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/cwp-compliance-agent-rules-api
  owner.panw/people-slack-handle-owners-group: '@nwolfin'
  owner.panw/team-slack-handle: '#cwp-prod-issues'
  owner.panw/people-slack-handle-team-lead: '@nwolfin'
