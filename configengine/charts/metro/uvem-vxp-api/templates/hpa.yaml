{{- if .Values.hpa.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
    {{- include "chart.labels" . | nindent 4 }}
    {{- with .Values.hpa.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ printf "%s-hpa" (include "chart.fullname" .) }}
  namespace: {{ include "common.namespace" . }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "chart.fullname" . }}
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  minReplicas: {{ .Values.hpa.minReplicas }}
  metrics: {{ toYaml .Values.hpa.metrics | nindent 4 }}
  {{- with .Values.hpa.behavior }}
  behavior: {{ toYaml . | nindent 4 }}
  {{- end -}}
{{- end -}}
