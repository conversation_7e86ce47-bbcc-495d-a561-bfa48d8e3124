{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
metadata:
  {{- with .Values.serviceAccount.annotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
    {{- include "chart.labels" . | nindent 4 }}
    {{- with .Values.serviceAccount.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "chart.serviceAccountName" . }}
  namespace: {{ include "common.namespace" . }}
{{- end -}}
