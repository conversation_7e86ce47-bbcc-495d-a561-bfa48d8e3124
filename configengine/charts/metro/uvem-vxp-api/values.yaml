envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
restartPolicy: ""
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}

terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

env: {}

deployment:
  strategy:
    type: Recreate
deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: uvem
  team: uvem
podLabels:
  group: uvem
  team: uvem
podAnnotations:
  prometheus.io/port: "8080"
  prometheus.io/scrape: "true"

service:
  annotations: {}

serviceAccount:
  create: true
  automountServiceAccountToken: false

resources:
  requests:
    cpu: 100m
    memory: 1Gi
  limits:
    cpu: 1
    memory: 2Gi

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /startup
    port: 8080
    scheme: HTTP
  periodSeconds: 120
  successThreshold: 1
  timeoutSeconds: 600

podSecurityContext: {}

nodeSelector:
  xdr-pool: wi-dynamic

hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  behavior:
    scaleDown:
      policies:
        - type: Percent
          periodSeconds: 120
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - type: Percent
          periodSeconds: 60
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 120
