apiVersion: v2
name: uvem-vxp-api
description: A Helm chart for Kubernetes
type: application
version: 0.2.3
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../../common/common"
  - alias: mtConfig
    name: mt-config
    version: "*.*.*"
    repository: "file://../../common/mt-config"
  - alias: stConfig
    name: st-config
    version: "*.*.*"
    repository: "file://../../common/st-config"
appVersion: "0.2.0"
annotations:
  "panw.com/deploy-eval":  "true"
  owner.panw/group: 'uvem'
  owner.panw/team: 'uvem'
  owner.panw/team-slack-handle: '#uvem-vulnerability-experience-public'
  owner.panw/people-slack-handle-owners-group: '@uvem-vulnerability-experience-group'
  owner.panw/people-slack-handle-team-lead: '@Venky Chintapandu'
  owner.panw/source-code-ops-helm-chart-url: 'https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/-/tree/dev/configengine/charts/metro/uvem-vxp-api' 
