_component_name: "'uvem-vxp-api'"

fullnameOverride: local._component_name
namespaceOverride: "globals.metro_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  VXP_IS_METRO_CONFIG: true
  VXP_CONFIG_BASE_PATH: '"/"'

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

# global configs to apply for /config/config.yaml
mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  dataFrom:
    - '"common-config"'

  data:
    GCPCONF_PROJECT_ID: metro.project_id
    GARAGE_GCP_PROJECT_ID: metro.project_id
    CORTEX_PLATFORM_URL: "'http://' + globals.env.CORTEX_PLATFORM_URI"


# per tenant configs to apply for /tenants/tenantX.yaml
stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          .tenant.lcaas_id + "-configmap",
          .tenant.lcaas_id + "-configmap-feature-flags"
        ],

        "data": {
          "GCPCONF_PROJECT_ID": .tenant.project_id,
          "TENANT_ID": .tenant.lcaas_id,
          "PROJECT_ID": .tenant.project_id,
          "MYSQLCONF_PORT": "3306",
          "MYSQLCONF_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql",
                "key": "MYSQL_UVM_VXP_USERNAME",
                "optional": false
              }
            }
          },
          "MYSQLCONF_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql-ps",
                "key": "MYSQL_UVM_VXP_PASSWORD",
                "optional": false
              }
            }
          },
          "UVEM_MYSQLCONF_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql",
                "key": "MYSQL_UVM_VXP_USERNAME",
                "optional": false
              }
            }
          },
          "UVEM_MYSQLCONF_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql-ps",
                "key": "MYSQL_UVM_VXP_PASSWORD",
                "optional": false
              }
            }
          },
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
