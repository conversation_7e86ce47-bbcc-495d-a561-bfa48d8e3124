namespaceOverride: globals.metro_namespace

common-config:
  fullnameOverride: '"common-config"'
  namespaceOverride: globals.metro_namespace
  data:
    VIEWSCONF_PROJECT: "'xdr-zeus'"
    REDISCONF_ADDR: '"redis." + globals.metro_namespace + ".svc.cluster.local"'
    MYSQLCONF_HOST: '"mysql." + globals.metro_namespace + ".svc.cluster.local"'
    CORTEX_PLATFORM_URL: '"platform." + globals.metro_namespace + ".svc.cluster.local:8000"'
    GCPCONF_PROJECT_ID: metro.project_id

cas-global-config:
  fullnameOverride: '"cas-global-config"'
  namespaceOverride: globals.metro_namespace
  data:
    IS_MULTI_TENANT: true
    ARGO_HTTP_STEP_IMAGE: 'globals.gcr + "/golden-images/curl:8.11.1"'
    ARGO_SERVER_URL: '"http://argo-workflows-server." + globals.metro_namespace + ".svc.cluster.local:2746"'
    REDIS_HOST: '"cas-redis." + globals.metro_namespace + ".svc.cluster.local"'
    CORTEX_PLATFORM_URL: '"platform." + globals.metro_namespace + ".svc.cluster.local:8000"'
    MONGO_DB_USERNAME: "'root'"
    MONGO_DB_HOST_NAME: 'globals.cwp_mongo_db_address'
    MONGO_DB_DATABASE_NAME: "'cas-global'"

ciem-global-config:
  fullnameOverride: '"ciem-global-config"'
  namespaceOverride: globals.metro_namespace
  data:
    BIGQUERY_TENANT: metro.id
    CLOUDSEC_CLOUD_API_SERVICE: "'cloud-api-service'"
    CLOUDSEC_NAMESPACE: 'globals.metro_namespace + ".svc.cluster.local"'
    CLOUDSEC_REST_HOST_MT: '"http://"'
    CLOUDSEC_RULE_MANAGEMENT_SERVICE: "'rule-management'"
    CONCURRENCY_MODE: '"PARALLEL_STREAMS"'
    CORTEX_PLATFORM_REST_CONNECT_TIMEOUT_SECS: "60"
    CORTEX_PLATFORM_REST_HOST: '"http://" + globals.metro_namespace + "-"'
    CORTEX_PLATFORM_REST_READ_TIMEOUT_SECS: "60"
    ENVIRONMENT: "region.viso_env"
    ISSUES_IN_MESSAGE_BATCH_SIZE: "20"
    ISSUE_TOPIC_BATCH_SIZE: "10"
    IS_MULTI_TENANT: true
    POLLING_TIME_OFFSET_MS: "600000"
    POSTGRES_DB: "'ciem'"
    POSTGRES_HOST: globals.env.PGHOST
    POSTGRES_PORT: "5432"
    POSTGRES_USER: "'root'"
    PROJECT_ID: metro.project_id
    PUBSUB_INITIAL_RETRY_SECONDS : "1"
    PUBSUB_RETRY_DELAY_MULTIPLIER : "2.0"
    PUBSUB_RETRY_MAX_DELAY_SECONDS : "30"
    PUBSUB_RETRY_TIMEOUT_SECONDS : "300"
    RETRY_BACKOFF_DELAY_MS: "15000"
    RETRY_MAX_ATTEMPTS: "40"
    RULE_SCANNER_WORKER_TIMEOUT_MS: "3600000"
    RULE_SCAN_ORCHESTRATOR_THREAD_POOL_SIZE: "1"
    STATS_PROJECT_ID: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
    TENANT_ID: metro.id

cwp-global-config:
  fullnameOverride: '"cwp-global-config"'
  namespaceOverride: globals.metro_namespace
  data:
    MESSAGE_BUS_PREFETCH_COUNT: '"1"'
    MESSAGE_BUS_WORKER_POOL_SIZE: '"1"'
    MESSAGE_BUS_MAX_OUTSTANDING_BYTES: '"-1"'
    GENERIC_XDR_ENVIRONMENT: region.xdr_env
    CWP_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    CWP_REDIS_PORT: '"6379"'
    OTEL_EXPORTER_OTLP_ENDPOINT: '"opentelemetry-collector.monitoring.svc.cluster.local:4318"'
    CORE_ASSET_ANALYZER_REDIS_DB_NUMBER: '"0"'
    CORE_ASSET_ANALYZER_REDIS_PORT: '"6379"'
    CORE_ASSET_ANALYZER_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    CWP_REGISTRY_DISCOVERY_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    CWP_REGISTRY_SCAN_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    IMAGE_ANALYZER_REDIS_DB_NUMBER: '"0"'
    IMAGE_ANALYZER_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    IMAGE_ANALYZER_REDIS_PORT: '"6379"'
    MALWARE_DETECTION_REDIS_DB_NUMBER: '"0"'
    MALWARE_DETECTION_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    MALWARE_DETECTION_REDIS_PORT: '"6379"'
    PC1_MIGRATION_REDIS_DB_NUMBER: '"0"'
    PC1_MIGRATION_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    PC1_MIGRATION_REDIS_PORT: '"6379"'
    SCAN_RESULTS_ENRICHER_REDIS_DB_NUMBER: '"0"'
    SCAN_RESULTS_ENRICHER_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    SCAN_RESULTS_ENRICHER_REDIS_PORT: '"6379"'
    SERVERLESS_REDIS_DB_NUMBER: '"0"'
    SERVERLESS_REDIS_HOST: '"cwp-redis." + globals.metro_namespace + ".svc.cluster.local"'
    SERVERLESS_REDIS_PORT: '"6379"'
    PROJECT_ID: metro.project_id
    CORTEX_PLATFORM_URL: '"platform." + globals.metro_namespace + ".svc.cluster.local:8000"'
    VULNERABILITY_ANALYZER_SERVICE_URL: '"http://cwp-vulnerability-analyzer-svc.xdr-mt.svc.cluster.local:8080"'
    RULES_MANAGEMENT_URL: '"http://cwp-rules-management-svc.xdr-mt.svc.cluster.local:8080"'
    SECRET_ANALYZER_SERVICE_URL: '"http://cwp-secret-analyzer-svc.xdr-mt.svc.cluster.local:8080"'
    MALWARE_ANALYZER_SERVICE_URL: '"http://cwp-malware-analyzer-svc.xdr-mt.svc.cluster.local:8080"'
    IMAGE_ANALYZER_SERVICE_URL: '"http://cwp-image-analyzer-svc.xdr-mt.svc.cluster.local:8080"'
    SBOM_ANALYZER_SERVICE_URL: '"http://cwp-sbom-analyzer-svc.xdr-mt.svc.cluster.local:8080"'
    POSTGRESQLCONF_HOST: "globals.env.PGHOST"
    POSTGRESQLCONF_PORT: "'5432'"
    ADS_CLEANING_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_API_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_GROUPING_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_SYNC_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_PRIORITIZATION_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_OBSERVER_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_LAUNCH_POSTGRES_DB_SCHEMA: '"ads"'
    ADS_SNAPSHOT_POSTGRES_DB_SCHEMA: '"ads"'
    SP_SNAPSHOT_POSTGRES_DB_SCHEMA: '"sp_snapshot"'
    GCP_PROJECT_ID: metro.project_id
    ADS_CLEANING_SERVICE_NAME: '"cwp-ais-scanned-account-cleaning"'
    ADS_CLEANING_CORTEX_PLATFORM_URL: globals.env.CORTEX_PLATFORM_URL
    SP_SNAPSHOT_API_ADDRESS: '"http://" + globals.metro_namespace + "-cwp-sp-snapshot-svc." + globals.metro_namespace + ".svc.cluster.local:8080"'
    UAI_AGGREGATED_ASSETS_VIEW: '"public_platform_" + metro.project_id + ".aggregated_assets_view"'
    GENERIC_PRODUCT_TYPE: '"xsiam"'
    GENERIC_XDR_ENVIRONMENT: region.xdr_env
    CODE_VERSION: '"3.13.0"'
    ADS_DEFAULT_SNAPSHOT_QUOTA: '"10000"'
    ADS_DEFAULT_GCP_VOLUME_QUOTA: '"20000"'
    ADS_DEFAULT_AZURE_VOLUME_QUOTA: '"50000"'
    ADS_MAX_ATTACHED_SNAPSHOTS: '"0"'
    ADS_MAX_MULTI_VOLUME_DISKS: '"0"'
    LOGGINGSERVICE_LCAAS_TENANT: replace(metro.id, "mt", "")
    ADS_LAUNCH_MAX_WORKLOAD_LIFE_TIME_SECONDS: '"21600"'
    ADS_OBSERVER_PUBSUB_SUBSCRIPTION_ID:  '"cwp-sp-lifecycle-events-ads-result-sync-sub-" + metro.project_id'
    ADS_LAUNCH_POSTGRES_DB_CONNECTION_STRING_SUFFIX: '"sslmode=disable"'
    CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID: metro.project_id
    SP_SNAPSHOT_KMS_KEY_ALIAS: 'region.viso_env + "-pan-managed-encrypt-snapshots-customers-generic"'

itdr-global-config:
    fullnameOverride: '"itdr-global-config"'
    namespaceOverride: globals.metro_namespace
    data:
      TENANT_ID: metro.id
      CORTEX_PLATFORM_URL: globals.env.CORTEX_PLATFORM_URL
      GCP_PROJECT_ID: metro.project_id
      POSTGRES_HOST: globals.env.PGHOST
      POSTGRES_PORT: '"5432"'
      CWP_REDIS_PORT: '"6379"'
      ITDR_CA_REDIS_DATABASE: '"0"'
      OTEL_EXPORTER_OTLP_ENDPOINT: globals.env.OTEL_EXPORTER_OTLP_ENDPOINT
      CWP_REDIS_HOST: globals.env.REDIS_CWP_URI
      GENERIC_XDR_ENVIRONMENT: region.xdr_env

dml-common-config:
  fullnameOverride: '"dml-common-config"'
  namespaceOverride: globals.metro_namespace
  data:
      CRONUS_HOTKEYS_ENABLED: '"true"'
      CRONUS_HOTKEYS_SAMPLE_RATE: '"2m"'
      CRONUS_HOTKEYS_TRACKER: '"network=ENABLED:True,RATE_WINDOW:2m,TOKENS_IN_WINDOW:400000"'
      CROP_CONTROLLER_HOTKEYS_ENABLED: '"true"'
      CROP_HOTKEYS_ENABLED: '"true"'
      CROP_HOTKEYS_SAMPLING_FREQ: '"2m"'
      CROP_HOTKEYS_STREAMS: '"network=ENABLED:True,TOKENS_IN_WINDOW:400000"'
      HPL_CRONUS_CLIENT_HOTKEYS_ENABLED: '"true"'
      HPL_KEY_VALUE_STORE_IS_BLOCKING: '"True"'
      HPL_KEY_VALUE_STORE_IS_ENABLED: '"True"'
      HPL_KEY_VALUE_STORE_IS_EXCLUSIVE: '"True"'
      HPL_KEY_VALUE_STORE_NETWORK_STORE_TYPE: '"cronus"'
      HPL_REDIS_FLOW_LOGS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local:6379"'

      GONZO_LOGGER_TRANSPORT: '"console:-1"'
      GONZO_PROJECT_ID: metro.project_id
      GONZO_REDIS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local:6379"'
      GONZO_REGION: '"us-central1"'
      GONZO_SLACK_CHANNEL: '"xdr-pipeline-dev"'
      HPL_GCP_PROJECT_ID: metro.project_id
      HPL_GONZO_REDIS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local:6379"'
      HPL_GONZO_REDIS_USE_DEFAULT: '"True"'
      HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_HOSTS: '"scylla-client.scylla.svc"'
      HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_USER: '"user"'
      HPL_REDIS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local:6379"'
      TENANT_ID: metro.id

tenantTopologies: |
  map(values(residents_configs), (
    {
      "fullnameOverride": "tenant-topology",
      "namespaceOverride": .globals.st_namespace,

      "data": {
        "id": .tenant.lcaas_id,
        "status": "active",
        "path": "/tenants/" + .tenant.lcaas_id + ".yaml"
      }
    }
  ))

