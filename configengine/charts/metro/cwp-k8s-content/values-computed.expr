_component_name: "'cwp-k8s-content'"
_component_image_name: "'cwp-k8s-api'"

fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_image_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '"50m"'
    memory: '"512Mi"'
  limits:
    cpu: '"1"'
    memory: '"2Gi"'
    
mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    CONTENT_KMS_KEY_PROJECT: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
    CONTENT_BUCKET: 'region.is_fedramp || region.is_dev ? "global-cwp-content-" + region.multi_project_postfix : "global-cwp-content"'
    CONTENT_KMS_KEY_LOCATION: '"us-central1"'
    CONTENT_KMS_KEY_NAME: '"cwp-content-v2"'
    CONTENT_KMS_KEY_RING: '"content_signing"'
    CONTENT_KMS_KEY_VERSION: '"1"'
  dataFrom:
    - '"common-config"'


stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          "cwp-config"
        ],

        "data": {
          "ENABLE_CLOUD_POSTURE": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-configmap",
                "key": "ENABLE_CLOUD_POSTURE",
                "optional": false
              }
            }
          }
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
