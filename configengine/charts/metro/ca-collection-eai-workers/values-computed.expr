_component_name: "'ca-collection-eai-workers/ca-collection-eai-workers'"
_container_name: "'ca-collection-eai-workers'"

fullnameOverride: '"ca-collection-eai-workers"'
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

matchLabels:
  rate_limit_group: '"shared-gubernator"'
  app_name: '"ca-collection-eai-workers"'

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"ca-collection-eai-worker-pod" + "@" + metro.project_id + ".iam.gserviceaccount.com"'

hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  metrics:
    - resource:
        name: "'cpu'"
        target:
          averageUtilization: 80
          type: "'Utilization'"
      type: "'Resource'"

resources:
  limits:
    cpu: '"500m"'
    memory: '"2Gi"'
  requests:
    cpu: 'region.is_dev ? "50m" : "200m"'
    memory: '"400Mi"'

vsg:
  create: true
  enabled: true
  project: metro.project_id
  oomScaling:
    cooldownMinutes: 15
    enabled: true
    memoryConstantIncrease: "'512Mi'"
    podRequestMemoryMax: "'5Gi'"
    prometheusMetricSource:
      container: local._container_name
      threshold: 2
      windowMinutes: 360

mtConfig:
  fullnameOverride: 'local._container_name + "-config"'
  namespaceOverride: globals.metro_namespace

  data:
    GENERIC_SKIP_SCHEMA_VERIFICATION: "'True'"
    CA_COLLECTION_RIT_BUCKET_NAME: '"marketplace-cortex-content-" + region.multi_project_postfix'
    REDISCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          key: "'redis_password'"
          name: globals.metro_secrets
          optional: false
    CA_COLLECTION_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'redis_password'"
          optional: false
    GONZO_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'redis_password'"
          optional: false
    CA_COLLECTION_REDIS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local" + ":6379"'
    GONZO_REDIS_CONNECTION_STRING: '"redis." + globals.metro_namespace + ".svc.cluster.local" + ":6379"'
    EGRESSPROXY_URL: globals.egress_proxy_address
    EGRESSPROXY_CA_PATH: "'/etc/certs/egress.crt'"

  dataFrom:
    - '"common-config"'

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          .tenant.lcaas_id + "-configmap"
        ],

        "data": {
          "CA_COLLECTION_MYSQL_USER": "pipeline",
          "CA_COLLECTION_LCAAS_ID": .tenant.lcaas_id,
          "CA_COLLECTION_CLOUD_PROJECT_ID": .tenant.project_id,
          "MYSQLCONF_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql",
                "key": "MYSQL_PIPELINE_USERNAME",
                "optional": false
              }
            }
          },
          "MYSQLCONF_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-mysql-ps",
                "key": "MYSQL_PIPELINE_PASSWORD",
                "optional": false
              }
            }
          },
          "CA_COLLECTION_MYSQL_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .globals.tenant_secrets,
                "key": "mysql_password",
                "optional": false
              }
            }
          }
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'
  - name: '"api-certs"'
    secret:
      defaultMode: 272
      optional: false
      secretName: globals.metro_secrets

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
  - name: '"api-certs"'
    mountPath: '"/etc/cert/"'
    readOnly: true
