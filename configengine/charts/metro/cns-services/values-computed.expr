_component_names:
  cns-api: "'cns-api'"
  cns-evaluator: "'cns-evaluator'"
  cns-graph-engine: "'cns-graph-engine'"
  cns-graph-ingester: "'cns-graph-ingester'"

namespaceOverride: globals.metro_namespace

_images:
  cns_api:
    component: "get(images.components, 'cns-api')"
    family: "get(images.families, local._images.cns_api.component.family)"
  cns_evaluator:
    component: "get(images.components, 'cns-evaluator')"
    family: "get(images.families, local._images.cns_evaluator.component.family)"
  cns_graph_engine:
    component: "get(images.components, 'cns-graph-engine')"
    family: "get(images.families, local._images.cns_graph_engine.component.family)"
  cns_graph_ingester:
    component: "get(images.components, 'cns-graph-ingester')"
    family: "get(images.families, local._images.cns_graph_ingester.component.family)"

images:
  cns_api:
    registry: "globals.gcr"
    repository: "local._images.cns_api.component.repository ?? local._images.cns_api.family.repository"
    tag: "local._images.cns_api.component.tag ?? local._images.cns_api.family.tag"
  cns_graph_engine:
    registry: "globals.gcr"
    repository: "local._images.cns_graph_engine.component.repository ?? local._images.cns_graph_engine.family.repository"
    tag: "local._images.cns_graph_engine.component.tag ?? local._images.cns_graph_engine.family.tag"
  cns_graph_ingester:
    registry: "globals.gcr"
    repository: "local._images.cns_graph_ingester.component.repository ?? local._images.cns_graph_ingester.family.repository"
    tag: "local._images.cns_graph_ingester.component.tag ?? local._images.cns_graph_ingester.family.tag"
  cns_evaluator:
    registry: "globals.gcr"
    repository: "local._images.cns_evaluator.component.repository ?? local._images.cns_evaluator.family.repository"
    tag: "local._images.cns_evaluator.component.tag ?? local._images.cns_evaluator.family.tag"

cronjobs:
  evaluator:
    name: '"xdr-mt-cns-evaluator-cronjob"'

  graph_ingester:
    name: '"xdr-mt-cns-graph-ingester-cronjob"'

deployments:
  api:
    name: '"xdr-mt-cns-api"'
    service_name: '"xdr-mt-cns-api-svc"'
  graph_engine:
    name: '"xdr-mt-cns-graph-engine"'

mtConfig:
  fullnameOverride: '"cns-services-config"'
  namespaceOverride: globals.metro_namespace

  data:
    CNS_NEO4J_CONF_BOLT_PORT: "'7687'"
    CNS_NEO4J_CONF_HOST: "'cns-neo4j.' + globals.metro_namespace"
    CNS_GRAPH_ENGINE_PORT: "'8000'"
    CNS_API_ENDPOINT: 'globals.metro_namespace + "-cns-api-svc"'
    CNS_GRAPH_ENGINE_ENDPOINT: 'globals.metro_namespace + "-cns-graph-engine"'
    POSTGRESQLCONF_PORT: "'5432'"
    CNS_API_PORT: "'5000'"
    POSTGRESQLCONF_DB_NAME: "'cwp'"
    POSTGRESQLCONF_USERNAME: "'root'"
    POSTGRESQLCONF_SEARCH_PATH: "'public'"
    CNS_NEO4J_CONF_AUTH:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'cns_neo4j_user_password'"
          optional: false
    FINDINGS_REDIS_ADDRESS: "globals.env.REDIS_CWP_URI"
    FINDINGS_REDIS_PORT: "'6379'"
    FINDINGS_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'cwp_redis_password'"
          optional: false
    POSTGRESQLCONF_HOST: "globals.env.PGHOST"
    POSTGRESQLCONF_PORT: "'5432'"
    POSTGRESQLCONF_DB_NAME: "'cwp'"
    POSTGRESQLCONF_USERNAME: "'root'"
    POSTGRESQLCONF_SEARCH_PATH: "'public'"
    POSTGRESQLCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: globals.metro_secrets
          key: "'postgres_password'"
          optional: false

  
  dataFrom: []

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,

        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },

        "dataFrom": [
          "cwp-config"
        ],

        "data": {
            "CNS_GRAPH_ENGINE_ENDPOINT": globals.metro_namespace + "-cns-graph-engine",
            "CNS_GRAPH_ENGINE_PORT": "8000",

            "CNS_API_ENDPOINT": globals.metro_namespace + "-cns-api-svc",
            "CNS_API_PORT": "5000",

            "FINDINGS_REDIS_ADDRESS": globals.env.REDIS_CWP_URI,
            "FINDINGS_REDIS_PORT": "6379",
            "FINDINGS_REDIS_PASSWORD": {
              "valueFrom": {
                "secretKeyRef": {
                  "name": .globals.tenant_secrets,
                  "key": "cwp_redis_password",
                  "optional": false
                }
              }
            },

            "POSTGRESQLCONF_HOST": .globals.env.PGHOST,
            "POSTGRESQLCONF_PORT": "5432",
            "POSTGRESQLCONF_DB_NAME": "cwp",
            "POSTGRESQLCONF_USERNAME": "root",
            "POSTGRESQLCONF_SEARCH_PATH": "public",

            "POSTGRESQLCONF_PASSWORD": {
              "valueFrom": {
                "secretKeyRef": {
                  "name": .globals.tenant_secrets,
                  "key": "postgres_password",
                  "optional": false
                }
              }
            },

             "ENABLE_CLOUD_POSTURE": {
              "valueFrom": {
                "secretKeyRef": {
                  "name": .tenant.lcaas_id + "-configmap",
                  "key": "ENABLE_CLOUD_POSTURE",
                  "optional": false
                }
              }
            },

            "SECURITY_CONTROLS_UPDATE_TOPIC_NAME": "dp-security-controls-" + .tenant.lcaas_id,

            "CORTEX_CONTEXT_IGNORE_SBAC_ERRORS": "true",
            "CLOUDSEC_CLOUD_API_SERVICE_URL": "cloud-api-service." + .globals.metro_namespace + ".svc.cluster.local:8080",
            "CLOUDSEC_RULES_API_SERVICE_URL": "rule-management." + .globals.metro_namespace  + ".svc.cluster.local:8080",

            /*
              Temp variables that should be cleaned-up later when the target services will be ready
              https://jira-dc.paloaltonetworks.com/browse/CRTX-202984
            */ 
            
            // CloudSec services are still not supported in Metro, using mocks
            "CLOUDSEC_MOCK_RULES": "true",
            "CLOUDSEC_MOCK_POLICIES": "true"
        }
      }
    ))

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"cwp-cna" + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  evaluator_cronjob:
    requests:
      cpu: '"1"'
      memory: '"2Gi"'
    limits:
      cpu: '"4"'
      memory: '"4Gi"'
  graph_ingester_cronjob:
    requests:
      cpu: '"1"'
      memory: '"2Gi"'
    limits:
      cpu: '"4"'
      memory: '"4Gi"'
  graph_engine:
    requests:
      cpu: '"50m"'
      memory: '"256Mi"'
    limits:
      cpu: '"1"'
      memory: '"1Gi"'
  api:
    requests:
      cpu: '"50m"'
      memory: '"256Mi"'
    limits:
      cpu: '"1"'
      memory: '"1Gi"'

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
