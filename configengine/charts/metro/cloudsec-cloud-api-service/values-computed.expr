_component_name: "'cloud-api-service'"
fullnameOverride: local._component_name
namespaceOverride: globals.metro_namespace

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: '"c130b43891a1e73c9a78ff45750ebc1b0cdbc501"'
  repository: "local._images.component.repository ?? local._images.family.repository"

deployment:
  strategy:
    type: '"RollingUpdate"'
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

serviceAccount:
  create: true
  annotations:
    "iam.gke.io/gcp-service-account": 'local._component_name + "@" + metro.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '"200m"'
    memory: '"500Mi"'
  limits:
    cpu: "'2'"
    memory: '"4Gi"'

env:
  GENERIC_use_legacy_config: false
  GCP_PROJECT_ID: metro.project_id

hpa:
  enabled: false

nodeSelector:
  "xdr-pool": '"wi-dynamic"'

priorityClassName: '"high-priority-deployment"'

mtConfig:
  fullnameOverride: 'local._component_name + "-config"'
  namespaceOverride: globals.metro_namespace
  data:
    PLATFORM_API_PORT: "8000"
    CLOUDSEC_DATABASE_PREFIX: '"cspm"'
    DS_DATABASE_PREFIX: '"ds_"'
    CLOUDSEC_COMMON_POSTGRES_SVC: '"postgres." + globals.metro_namespace + ".svc.cluster.local"'
    CLOUDSEC_COMMON_POSTGRES_PORT: "5432"
    LOGCONF_LOG_CONSOLE_MIN_SEVERITY: '"INFO"'
    LOGGING_TYPE: '"json"'
    GCPCONF_ENV_REGION: region.gcp_region
    SOURCE_WORLD: '"cloudsec"'
    BQ_STATS_TOPIC: '"cloudsec-bq-stats"'
    XDR_REDIS_PORT: '"6379"'
    CLOUDSEC_REDIS_DB: '"0"'
    REDIS_MAX_CONNECTIONS: '"30"'
    EGRESSPROXY_URL: '"localhost:11117"'
    EGRESSPROXY_CA_PATH: '"/etc/cert/egress.crt"'
    SCHEDULER_CHECK_INTERVAL: '"5"'
    WORKFLOW_THREADPOOL_MAX_WORKERS: '"10"'
    COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_PENDING_TIMEOUT: '"2100"'
    COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT: '"5400"'
    COMPLIANCE_STANDARD_MIGRATION_WORKFLOW_INTERVAL: '"60"'
    POLICIES_MIGRATION_WORKFLOW_PENDING_TIMEOUT: '"7800"'
    POLICIES_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT: '"5400"'
    POLICIES_MIGRATION_WORKFLOW_INTERVAL: '"60"'
    NOTIFICATIONS_MIGRATION_WORKFLOW_INTERVAL: '"60"'
    NOTIFICATIONS_MIGRATION_WORKFLOW_PROGRESS_TIMEOUT: '"3600"'
    NOTIFICATIONS_MIGRATION_WORKFLOW_PENDING_TIMEOUT: '"13500"'
    GET_CONTROLS_PAGE_SIZE: '"1000"'
    GET_COMPLIANCE_STANDARDS_PAGE_SIZE: '"1000"'
    MANAGEMENT_OTLP_METRICS_EXPORT_URL: '"http://otel-collector.monitoring.svc.cluster.local:4318/v1/metrics"'
    OTEL_EXPORTER_OTLP_ENDPOINT: '"http://otel-collector.monitoring.svc.cluster.local:4318"'
    OTEL_EXPORTER_OTLP_PROTOCOL: '"http/protobuf"'
  dataFrom:
    - '"common-config"'

stConfig:
  tenants: |
    map(values(residents_configs), (
      {
        "fullnameOverride": local.mtConfig.fullnameOverride,
        "namespaceOverride": .globals.st_namespace,
        "pushTarget": {
          "namespace": .globals.metro_namespace,
          "property": .tenant.lcaas_id + ".yaml",
          "remoteKey": "tenants-" + local.mtConfig.fullnameOverride
        },
        "dataFrom": [
          "common-config"
        ],
        "data": {
          "GCP_PROJECT_ID": string(.tenant.project_id),
          "GCPCONF_PROJECT_ID": string(.tenant.lcaas_id),
          "GCPCONF_REAL_REGION": .region.gcp_region,
          "GCPCONF_REGION": .tenant.bq_location,
          "GCPCONF_ENV_REGION": .region.gcp_region,
          "GENERIC_LCAAS_ID": string(.tenant.lcaas_id),
          "CLOUDSEC_DB_NAME": "cspm_" + string(.tenant.lcaas_id),
          "CLOUDSEC_COMMON_POSTGRES_RULE_MGMT_SCHEMA": "public",
          "CLOUDSEC_COMMON_POSTGRES_POLICY_SCHEMA": "cloudsec_policy_management",
          "PG_CON_POOL_MIN_CONNECTION": "1",
          "PG_CON_POOL_MAX_CONNECTION": "10",
          "BQ_STATS_REPORTING_ENABLED": "true",
          "CLOUDSEC_REMEDIATION_SUPPORTED_RULE_TYPES": "config,attack_path,identity,network_reachability",
          "EGRESSPROXY_URL": "localhost:11117",
          "LOGCONF_LOG_CONSOLE_MIN_SEVERITY": "DEBUG",
          "LOGGING_TYPE": "REGULAR_LOGGING",
          "PGGSSENCMODE": "disable",
          "XDR_REDIS_PORT": "6379",
          "XDR_REDIS_HOST": "redis." + .globals.metro_namespace + ".svc.cluster.local",
          "XDR_REDIS_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-secrets",
                "key": "redis_password",
                "optional": false
              }
            }
          },
          "CLOUDSEC_COMMON_POSTGRES_USER": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres",
                "key": "POSTGRES_CSPM_USERNAME",
                "optional": false
              }
            }
          },
          "POSTGRES_DB_PASSWORD": {
            "valueFrom": {
              "secretKeyRef": {
                "name": .tenant.lcaas_id + "-postgres-ps",
                "key": "POSTGRES_CSPM_PASSWORD",
                "optional": false
              }
            }
          }
        }
      }
    ))

extraVolumes:
  - name: '"config"'
    secret:
      secretName: local.mtConfig.fullnameOverride
  - name: '"tenants"'
    secret:
      secretName: '"tenants-" + local.mtConfig.fullnameOverride'
  - name: '"proxydome-certs"'
    secret:
      defaultMode: 444
      optional: false
      secretName: globals.metro_secrets
      items:
        - key: '"egress.crt"'
          path: '"egress.crt"'

extraVolumeMounts:
  - name: '"config"'
    mountPath: '"/config"'
    readOnly: true
  - name: '"tenants"'
    mountPath: '"/tenants"'
    readOnly: true
  - name: '"proxydome-certs"'
    mountPath: '"/etc/cert/"'
    mountPropagation: '"None"'
    readOnly: true
