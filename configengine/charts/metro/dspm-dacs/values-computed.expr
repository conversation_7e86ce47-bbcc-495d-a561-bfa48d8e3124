_component_image: "'dspm-data-classification-settings'"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

_service_names:
  dashboard: "'dspm-dacs-dashboard'"
  taskScheduler: "'dspm-dacs-tasks-scheduler'"

_service_accounts_names:
  dashboard: "'dspm-dacs'"
  taskScheduler: "'dspm-dacs-ts'"

global:
  image:
    registry: "globals.gcr"
    tag: "local._images.component.tag ?? local._images.family.tag"
    repository: "local._images.component.repository ?? local._images.family.repository"
  tenantConfigsSecretName: '"metro-dspm-dacs-tenantconfigs"'
  configuration:
    metro:
      datasource:
        hikari:
          poolName: "'Hikari'"
          auto-commit: "'false'"
    management:
      endpoint:
        health:
          group:
            liveness-datasource:
              include: '"metroDataSource"'
      health:
        db:
          enabled: "'false'"

dashboard:
  fullnameOverride: 'local._service_names.dashboard'
  namespaceOverride: "globals.metro_namespace"
  serviceAccount:
    name: 'local._service_accounts_names.dashboard'
    create: true
    annotations:
      iam.gke.io/gcp-service-account: 'local._service_accounts_names.dashboard + "@" + metro.project_id + ".iam.gserviceaccount.com"'
  service:
    create: true
  hpa:
    enabled: false

  resources:
    requests:
      cpu: 'region.viso_env == "dev" ? "200m" : "200m"'
      memory: 'region.viso_env == "dev" ? "1Gi" : "1Gi"'
    limits:
      cpu: 'region.viso_env == "dev" ? "3" : "3"'
      memory: 'region.viso_env == "dev" ? "1700Mi" : "1700Mi"'
  env:
    JAVA_OPTS: "'-Xms128m -Xmx1024m'"
    MAIN_CLASS: "'security.dig.dacs.DataClassificationSettingsApp'"
    SPRING_PROFILES_ACTIVE: "'dashboard, internalApi'"
    CORTEX_ENV_TYPE: 'region.is_dev ? "dev" : "prod"'
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    METRO_TENANT_CONFIG_DIR: "'/config/tenants'"

taskScheduler:
  fullnameOverride: 'local._service_names.taskScheduler'
  namespaceOverride: "globals.metro_namespace"
  image:
    registry: "globals.gcr"
    tag: "local._images.component.tag ?? local._images.family.tag"
    repository: "local._images.component.repository ?? local._images.family.repository"
  serviceAccount:
    name: 'local._service_accounts_names.taskScheduler'
    create: true
    annotations:
      iam.gke.io/gcp-service-account: 'local._service_accounts_names.taskScheduler + "@" + metro.project_id + ".iam.gserviceaccount.com"'
  resources:
    requests:
      cpu: 'region.viso_env == "dev" ? "100m" : "100m"'
      memory: 'region.viso_env == "dev" ? "1Gi" : "1Gi"'
    limits:
      cpu: 'region.viso_env == "dev" ? "1" : "2"'
      memory: 'region.viso_env == "dev" ? "1700Mi" : "1700Mi"'
  env:
    JAVA_OPTS: "'-Xms128m -Xmx1024m'"
    MAIN_CLASS: "'security.dig.dacs.DataClassificationSettingsApp'"
    SPRING_PROFILES_ACTIVE: "'tasksScheduler'"
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    METRO_TENANT_CONFIG_DIR: "'/config/tenants'"

