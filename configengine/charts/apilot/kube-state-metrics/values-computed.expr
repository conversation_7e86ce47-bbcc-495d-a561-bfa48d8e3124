fullnameOverride: '"kube-state-metrics-" + tenant.lcaas_id'

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "v2.16.0" : "v2.13.0"'

addonResizer:
  envFrom: []
  extraVolumes: {}
  extraVolumeMounts: {}
  image:
    registry: "globals.gcr"
  args:
    - "'--container=kube-state-metrics'"
    - "'--cpu=100m'"
    - "'--extra-cpu=1m'"
    - "'--memory=100Mi'"
    - "'--extra-memory=2Mi'"
    - "'--threshold=5'"
    - '"--deployment=" + local.fullnameOverride'
