senv: {}
envFrom: []
affinity: {}
tolerations:
  - key: xdr-pool
    operator: Equal
    value: "wi-static"
extraVolumes: {}
deploymentAnnotations: {}
podAnnotations: {}
extraVolumeMounts: {}
priorityClassName: ""
topologySpreadConstraints: {}

nodeSelector:
  xdr-pool: wi-static

replica: 1

deployment:
  strategy:
    type: Recreate

terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

image:
  registry: us-docker.pkg.dev/xdr-registry-prod-us-01
  repository: golden-images/redis
  tag: 7.4.0-alpine

persistence:
  enabled: true
  labels: {}
  annotations:
    helm.toolkit.fluxcd.io/driftDetection: disabled
  storageClass: ssd-storage
  size: 6Gi
  accessModes:
    - ReadWriteOnce

service:
  annotations: {}

serviceAccount:
  create: false
  annotations: {}

livenessProbe:
  exec:
    command:
      - /bin/sh
      - -c
      - redis-cli -h 127.0.0.1 ping
  initialDelaySeconds: 15
  timeoutSeconds: 3

readinessProbe:
  exec:
    command:
      - /bin/sh
      - -c
      - redis-cli -h 127.0.0.1 ping
  initialDelaySeconds: 20
  timeoutSeconds: 5

startupProbe: {}

podSecurityContext:
  runAsUser: 999
  runAsGroup: 1000
  fsGroup: 1000
  runAsNonRoot: false

args:
  maxclients: "4000"
  maxmemory-policy: noeviction
  requirepass: "$(REDISCONF_PASSWORD)"

resources:
  requests:
    cpu: 300m
    memory: 2Gi
  limits:
    cpu: 1
    memory: 2Gi

deploymentLabels:
  group: platform
  team: platform

podLabels:
  group: platform
  team: platform
