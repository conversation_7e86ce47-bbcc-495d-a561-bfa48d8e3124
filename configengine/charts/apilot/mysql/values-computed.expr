resources:
  requests:
    cpu: '"400m"'
    memory: '"6Gi"'
  limits:
    cpu: '"1200m"'
    memory: '"8Gi"'

fullnameOverride: '"mysql"'
namespaceOverride: globals.apilot_shardscape_namespace

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "8.0.44-debian" : "8.0.39-debian"'
serviceAccount:
  name: "'mysql8'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

env:
  MYSQL_ROOT_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: '"apilot-secrets"'
        key: '"mysql_password"'
        optional: false

persistence:
  storageClassName: '"ssd-storage"'
  size: '"20Gi"'

snapshots:
  hourlyEnabled: false
  dailyEnabled: false

  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"mysql-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"mysql-hourly-" + tenant.lcaas_id'
