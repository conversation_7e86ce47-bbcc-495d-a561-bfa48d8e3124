_component_name: "'prometheus'"

fullnameOverride: '"monitoring-" + tenant.lcaas_id + "-prometheus"'

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "v3.5.0" : "v3.4.2"'
resources:
  requests:
    cpu: 'globals.is_internal ? "100m" : "300m"'
    memory: '"700Mi"'
  limits:
    cpu: '"1"'
    memory: '"1Gi"'

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.apilot_project_id + ".iam.gserviceaccount.com"'

external_labels:
  tenant_type: "tenant.authcode"
  product_type: "lower(license.product_type)"
  lcaas_id: "tenant.lcaas_id"

