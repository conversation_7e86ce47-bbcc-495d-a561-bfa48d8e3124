apiVersion: v2
name: overseer
description: A Helm chart for Kubernetes
type: application
version: 0.2.2
appVersion: "1.16.0"
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
annotations:
  "panw.com/deploy-eval": "!tenant.is_metro_tenant"
  owner.panw/group: platform
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/overseer
  owner.panw/team: platform
  owner.panw/team-slack-handle: '@platform-infra-owners'
  owner.panw/people-slack-handle-team-lead: '@gacohen'
  owner.panw/people-slack-handle-owners-group: '@mweigert'
