fullnameOverride: orchestration
namespaceOverride: cas # remove after the migration to Cortex new platform

serviceAccount: argo-controller

nodeJsContainer:
  image:
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: golden-images/node
    tag: 22.12.0-alpine3.19
    pullPolicy: IfNotPresent

jqSupportingContainer:
  image:
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: golden-images/toolbox
    tag: 1.0-**********
    pullPolicy: IfNotPresent

orchestration:
  image: # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/orchestration-orchestration
    tag: stable-v1.0.2-c10c11f6 # will be replaced on demand
    pullPolicy: IfNotPresent

triggerBranchScans:
  image:
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/source-control-trigger-branch-scans
    tag: "" # will be replaced on demand
    pullPolicy: IfNotPresent

augmentor:
  image:
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/persistence-augmentor
    tag: stable-v1.2.0-2692-992e76e5 # will be replaced on demand
    pullPolicy: IfNotPresent

argo_http:
  image: us-docker.pkg.dev/xdr-registry-prod-us-01/golden-images/curl:8.11.1

graphEntitiesLoader:
  image: # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/cicd-graph-entities-loader
    tag: "" # will be replaced on demand
    pullPolicy: IfNotPresent

periodScans:
  disabled: false
  schedule: 0 6,18 * * * # trigger at 6:00 and 18:00

argoWorkflows:
  serverUrl: http://argo-workflows-server.cas.svc.cluster.local:2746
  password:
    secretName: argo-controller.service-account-token
    secretKey: token

argoWorkflowsServiceAccount: argo-controller
scanRepoWorkflowTemplateName: repo-scan-template
scanBranchWorkflowTemplateName: branch-scan-template
customerScanWorkflowTemplateName: customer-scan-template
calculateSopPpeFindingsWorkflow: calculate-sop-ppe-findings-workflow
persistenceWorkflowTemplateName: multi-persist-template
persistenceV2WorkflowTemplateName: multi-persist-v2-template
scannersWorkflowTemplateName: scanners-orchestration-workflow-template
filesFetchingWorkflowTemplateName: files-fetching-template
planScanWorkflowTemplateName: plan-scan-template
syncCustomerWorkflowTemplateName: sync-customer-template
prScanWorkflowTemplateName: pr-scan-template
cliScanWorkflowTemplateName: cli-scan-template
prCommentsWorkflowTemplateName: pr-comments-template
calcScanResultTemplateName: scan-result-template
tfRunTaskResultsTemplateName: tf-run-task-results-template
gitUsersWorkflowTemplateName: git-users-template
fetchExternalProjectReportWorkflowTemplateName: get-external-project-report-template
collaboratorReportWorkflowTemplateName: collaborator-report-template
tracingWorkflowTemplateName: code-to-cloud-tracing-workflow-template
vcsPipelineToolsWorkflowTemplateName: calculate-vcs-pipeline-tools-processor-workflow
enrichAssetsFindings: multi-enrich-template
codeToCloudTaggingBotWorkflowTemplateName: code-to-cloud-tagging-bot-workflow
getScannedBranchesTemplateName: get-scanned-branches-template

sourceControl:
  serviceAccount: source-control-cas

argoServer:
  protocol: http
  hostname: argo-workflows-server
  port: 2746

argoAPIKeySecretName: argo-controller.service-account-token
argoAPIKeySecretKey: token

deployments:
  sourceControl:
    host: http://source-control
  customersManagement:
    host: http://customers-management
  scansManagement:
    host: http://scans-management
    scanEventsTopicEnvName: SCAN_EVENTS_TOPIC
    scanEventsDlTopicEnvName: SCAN_EVENTS_DL_TOPIC
    prScanSubscriptionEnvName: PR_SCAN_SUBSCRIPTION
    periodicScanSubscriptionEnvName: PERIODIC_SCAN_SUBSCRIPTION
    cliScanSubscriptionEnvName: CLI_SCAN_SUBSCRIPTION
  resourcesCalculatorApi:
    host: http://resources-calculator-api:3000
  billingApi:
    host: http://billing-api
  dataGateway:
    host: http://data-gateway

workflowLabels:
  group: cas
  team: flow
podLabels:
  group: cas
  team: flow

parallelism:
  branchScanByCustomerScan: 100
  branchScanByRepoScan: 10
  syncAndPersistCustomerEntrypointSteps: 1
  syncAndPersistCustomerSubSteps: 2

neo4j:
  serverUri: neo4j://dp-neo4j-cluster.xdr-st.svc.cluster.local:7687
  userName: neo4j

mongo:
  password:
    secretName: infra-secrets
    secretKey: mongodb_password
  userName: root
  databaseName: cas
