apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    type: pr-scan-next-gen-template
  name: pr-scan-next-gen-template
  namespace: {{.Values.namespaceOverride}}
spec:
  arguments:
    parameters:
    - name: scan-id
    - name: repository-id
    - name: repository-name
    - name: repository-owner
    - name: repository-asset-id
    - name: commit-hash
    - name: branch-name
    - name: pr-number
    - name: target-branch-name
    - name: integration-type
    - name: integration-id
    - name: scan-configuration
    - name: repository-is-public
    - name: repository-is-archived
    - name: files-path-output
    - name: files-count-output
    - name: previous-scan-id
    - name: customer-modules
    - name: start-date
    - name: otel-trace-id
    - name: otel-span-id
    - name: git-provider-domain
    - name: customer-id
    - name: enable-next-gen-data-collector
    - name: trace-id
      value: missing-trace-id
  entrypoint: main
  hooks:
    failed:
      expression: workflow.status == "Failed"
      template: failed-exit-handler
  metrics:
    prometheus:
    - help: Workflow duration by name
      histogram:
        buckets:
        - 5.0
        - 30.0
        - 60.0
        - 120.0
        - 300.0
        - 600.0
        - 1800.0
        - 3600.0
        value: '{{`{{workflow.duration}}`}}'
      labels:
      - key: name
        value: pr_scan_next_gen_template
      - key: namespace
        value: {{.Values.namespaceOverride}}
      - key: status
        value: '{{`{{workflow.status}}`}}'
      - key: file_count
        value: '{{`{{=asInt(workflow.parameters[''files-count-output'']) <= 10 ? ''small''
          : asInt(workflow.parameters[''files-count-output'']) > 20 ? ''large'' :
          ''medium'' }}`}}'
      name: workflow_duration_secs
  templates:
  - inputs:
      parameters:
      - name: clone-path
      - name: diff-clone-path
      - name: scan-configuration
      - name: excluded-paths
      - name: patch-file-mapping-path
      - name: files-path
      - name: previous-scan-id
      - name: scanner-name
      - name: clone-type
      - name: repository-is-public
      - name: repository-is-archived
      - name: scanners-plan
      - name: otel-trace-id
      - name: otel-span-id
      - default: empty-value
        name: git-provider-domain
    name: scan-enrich-and-comment
    steps:
    - - arguments:
          parameters:
          - name: scanner-name
            value: '{{`{{inputs.parameters.scanner-name}}`}}'
          - name: scan-configuration
            value: '{{`{{inputs.parameters.scan-configuration}}`}}'
          - name: excluded-paths
            value: '{{`{{inputs.parameters.excluded-paths}}`}}'
          - name: repository-id
            value: '{{`{{workflow.parameters.repository-id}}`}}'
          - name: branch-name
            value: '{{`{{workflow.parameters.branch-name}}`}}'
          - name: scan-id
            value: '{{`{{workflow.parameters.scan-id}}`}}'
          - name: code-files
            value: '{{`{{=inputs.parameters[''clone-type''] == ''FULL'' ? inputs.parameters[''clone-path'']
              : inputs.parameters[''diff-clone-path'']}}`}}'
          - name: source-type
            value: '{{`{{workflow.parameters.integration-type}}`}}'
          - name: files-path
            value: '{{`{{inputs.parameters.files-path}}`}}'
          - name: scan-type
            value: '{{`{{=workflow.parameters[''integration-type''] matches ''HCP_TFC_RUN_TASKS|HCP_TFE_RUN_TASKS''
              ? ''CICD'' : ''PR''}}`}}'
          - name: scanner-scan-type
            value: CICD
          - name: repository-name
            value: '{{`{{workflow.parameters.repository-name}}`}}'
          - name: repository-owner
            value: '{{`{{workflow.parameters.repository-owner}}`}}'
          - name: repository-asset-id
            value: '{{`{{workflow.parameters.repository-asset-id}}`}}'
          - name: integration-id
            value: '{{`{{workflow.parameters.integration-id}}`}}'
          - name: previous-scan-id
            value: '{{`{{workflow.parameters.previous-scan-id}}`}}'
          - name: patch-file-mappings
            value: '{{`{{inputs.parameters.patch-file-mapping-path}}`}}'
          - name: pr-number
            value: '{{`{{workflow.parameters.pr-number}}`}}'
          - name: commit-hash
            value: '{{`{{workflow.parameters.commit-hash}}`}}'
          - name: repository-is-public
            value: '{{`{{workflow.parameters.repository-is-public}}`}}'
          - name: repository-is-archived
            value: '{{`{{workflow.parameters.repository-is-archived}}`}}'
          - name: scanners-plan
            value: '{{`{{inputs.parameters.scanners-plan}}`}}'
          - name: target-branch
            value: '{{`{{workflow.parameters.target-branch-name}}`}}'
          - name: disk-size-request
            value: '2000'
          - name: disk-size-limit
            value: '2000'
          - name: files-to-scan-path
            value: '{{`{{inputs.parameters.files-path}}`}}'
          - name: files-to-skip-path
            value: ''
          - name: use-cache
            value: 'false'
          - name: otel-trace-id
            value: '{{`{{inputs.parameters.otel-trace-id}}`}}'
          - name: otel-span-id
            value: '{{`{{inputs.parameters.otel-span-id}}`}}'
          - name: git-provider-domain
            value: '{{`{{inputs.parameters.git-provider-domain}}`}}'
        name: scan-and-enrich
        templateRef:
          name: scan-and-enrich-next-gen-template
          template: main
  - container:
      command:
      - curl
      - --fail-with-body
      - --no-progress-meter
      - -X
      - POST
      - -H
      - 'TRACE-ID: {{`{{workflow.parameters.trace-id}}`}}'
      - -H
      - 'Content-Type: application/json'
      - -d
      - '{"scanType": "{{`{{=workflow.parameters[''integration-type''] matches ''HCP_TFC_RUN_TASKS|HCP_TFE_RUN_TASKS''
        ? ''CICD'' : ''PR''}}`}}", "scanId": "{{`{{workflow.parameters.scan-id}}`}}", "repositoryId":
        "{{`{{workflow.parameters.repository-id}}`}}", "branchName": "{{`{{workflow.parameters.branch-name}}`}}"}'
      - --max-time
      - '180'
      - '{{.Values.deployments.scansManagement.host}}/v1/scans/management/branch-scan/statuses'
      env:
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: trigger_scans_management_calculation
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: PROJECT_ID
        valueFrom:
          configMapKeyRef:
            key: GCPCONF_PROJECT_ID
            name: cas-configmap
            optional: true
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: {{.Values.argo_http.image}}
      imagePullPolicy: IfNotPresent
      resources:
        limits:
          cpu: 100m
          memory: 1Gi
        requests:
          cpu: 50m
          memory: 100Mi
          ephemeral-storage: 100Mi
    metadata:
      labels:
        step_type: http
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 1.0
          - 10.0
          - 60.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: trigger_scans_management_calculation
        - key: status
          value: '{{`{{status}}`}}'
        - key: step_type
          value: http
        name: job_duration_seconds
    name: trigger-scans-management-calculation
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'' or indexOf(lower(lastRetry.message), ''(exit code 6)'') > -1
        or indexOf(lower(lastRetry.message), ''(exit code 7)'') > -1 or indexOf(lower(lastRetry.message),
        ''(exit code 28)'') > -1 or indexOf(lower(lastRetry.message), ''(exit code
        55)'') > -1 or indexOf(lower(lastRetry.message), ''(exit code 56)'') > -1'
      limit: 4
    timeout: 6h
  - container:
      command:
      - curl
      - --fail-with-body
      - --no-progress-meter
      - -X
      - POST
      - -H
      - 'TRACE-ID: {{`{{workflow.parameters.trace-id}}`}}'
      - -H
      - 'Content-Type: application/json'
      - -d
      - '{"customer_id": "{{`{{workflow.parameters.customer-id}}`}}", "scan_id": "{{`{{workflow.parameters.scan-id}}`}}",
        "integration_id": "{{`{{workflow.parameters.integration-id}}`}}", "repository_id":
        "{{`{{workflow.parameters.repository-id}}`}}", "branch": "{{`{{workflow.parameters.branch-name}}`}}",
        "commit_id": "{{`{{workflow.parameters.commit-hash}}`}}", "pr_number": "{{`{{workflow.parameters.pr-number}}`}}",
        "trace_id": "{{`{{workflow.parameters.otel-trace-id}}`}}", "span_id": "{{`{{workflow.parameters.otel-span-id}}`}}",
        "target_branch": "{{`{{workflow.parameters.target-branch-name}}`}}", "repository_extra_context":
        {"gitProviderDomain": "{{`{{workflow.parameters.git-provider-domain}}`}}", "repositoryAssetId":
        "{{`{{workflow.parameters.repository-asset-id}}`}}", "repositoryIsArchived": "{{`{{workflow.parameters.repository-is-archived}}`}}",
        "repositoryIsPublic": "{{`{{workflow.parameters.repository-is-public}}`}}", "repositoryName":
        "{{`{{workflow.parameters.repository-name}}`}}", "repositoryOwner": "{{`{{workflow.parameters.repository-owner}}`}}",
        "sourceType": "{{`{{workflow.parameters.integration-type}}`}}"}}'
      - --max-time
      - '180'
      - '{{.Values.deployments.dataGateway.host}}/api/cas/v1/data-gateway/clone/PR'
      env:
      - name: TRACE_ID
        value: '{{`{{workflow.parameters.trace-id}}`}}'
      - name: WORKFLOW_ID
        value: '{{`{{workflow.uid}}`}}'
      - name: JOB_NAME
        value: trigger_data_gateway_pr_task
      - name: WORKFLOW_NAME
        value: '{{`{{workflow.name}}`}}'
      - name: MIN_LOG_LEVEL
        valueFrom:
          configMapKeyRef:
            key: MIN_LOG_LEVEL
            name: cas-configmap
      - name: METRICS_RECEIVER_URL
        valueFrom:
          configMapKeyRef:
            key: METRICS_RECEIVER_URL
            name: cas-configmap
            optional: true
      - name: PROJECT_ID
        valueFrom:
          configMapKeyRef:
            key: GCPCONF_PROJECT_ID
            name: cas-configmap
            optional: true
      envFrom:
      - configMapRef:
          name: cas-configmap-feature-flags-defaults
      - configMapRef:
          name: cas-configmap-feature-flags
      image: {{.Values.argo_http.image}}
      imagePullPolicy: IfNotPresent
      resources:
        limits:
          cpu: 100m
          memory: 1Gi
        requests:
          cpu: 50m
          memory: 100Mi
          ephemeral-storage: 100Mi
    metadata:
      labels:
        step_type: http
    metrics:
      prometheus:
      - help: job duration by name
        histogram:
          buckets:
          - 1.0
          - 10.0
          - 60.0
          value: '{{`{{duration}}`}}'
        labels:
        - key: job_name
          value: trigger_data_gateway_pr_task
        - key: status
          value: '{{`{{status}}`}}'
        - key: step_type
          value: http
        name: job_duration_seconds
    name: trigger-data-gateway-pr-task
    retryStrategy:
      affinity:
        nodeAntiAffinity: {}
      backoff:
        duration: '10'
        factor: 2
      expression: 'indexOf(lower(lastRetry.message), ''pod was rejected'') > -1 or
        indexOf(lower(lastRetry.message), ''(exit code 128)'') > -1 or indexOf(lower(lastRetry.message),
        ''unknown (exit code 255)'') > -1 or indexOf(lower(lastRetry.message), ''(exit
        code 64): artifact'') > -1 or indexOf(lower(lastRetry.message), ''node was
        low on resource'') > -1 or indexOf(lower(lastRetry.message), ''could not find
        default credentials'') > -1 or indexOf(lower(lastRetry.message), ''ephemeral
        local storage'') > -1 or lastRetry.message == ''PodInitializing'' or lastRetry.message
        == ''OnError'' or indexOf(lower(lastRetry.message), ''(exit code 6)'') > -1
        or indexOf(lower(lastRetry.message), ''(exit code 7)'') > -1 or indexOf(lower(lastRetry.message),
        ''(exit code 28)'') > -1 or indexOf(lower(lastRetry.message), ''(exit code
        55)'') > -1 or indexOf(lower(lastRetry.message), ''(exit code 56)'') > -1'
      limit: 4
    timeout: 6h
  - name: main
    steps:
    - - arguments:
          parameters:
          - name: scan-id
            value: '{{`{{workflow.parameters.scan-id}}`}}'
          - name: files-path
            value: '{{`{{workflow.parameters.files-path-output}}`}}'
          - name: customer-modules
            value: '{{`{{=fromJSON(workflow.parameters[''customer-modules''])}}`}}'
          - name: trigger-type
            value: CICD
          - name: scan-configuration
            value: '{{`{{=toJson(jsonpath(workflow.parameters[''scan-configuration''],
              ''$.scanners''))}}`}}'
          - name: excluded-paths
            value: '{{`{{=toJson(jsonpath(workflow.parameters[''scan-configuration''],
              ''$.excludedPaths''))}}`}}'
          - name: repository-id
            value: '{{`{{workflow.parameters.repository-id}}`}}'
          - name: branch-name
            value: '{{`{{workflow.parameters.branch-name}}`}}'
          - name: previous-scan-id
            value: '{{`{{workflow.parameters.previous-scan-id}}`}}'
          - name: integration-id
            value: '{{`{{workflow.parameters.integration-id}}`}}'
          - name: commit-hash
            value: '{{`{{workflow.parameters.commit-hash}}`}}'
          - name: target-branch
            value: '{{`{{workflow.parameters.target-branch-name}}`}}'
          - name: pr-number
            value: '{{`{{workflow.parameters.pr-number}}`}}'
          - name: files-count
            value: '{{`{{workflow.parameters.files-count-output}}`}}'
          - name: otel-trace-id
            value: '{{`{{workflow.parameters.otel-trace-id}}`}}'
          - name: otel-span-id
            value: '{{`{{workflow.parameters.otel-span-id}}`}}'
        name: plan-scan
        templateRef:
          name: {{.Values.planScanWorkflowTemplateName}}
          template: plan-scan-runner
    - - name: trigger-data-gateway-pr-task
        template: trigger-data-gateway-pr-task
        when: '{{`{{= (sprig.int(workflow.parameters[''files-count-output'']) > 0 ? len(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
          ''$.scanners''), {#[''shouldRun'']})) > 0 : false) && (workflow.parameters[''enable-next-gen-data-collector'']
          == ''true'') }}`}}'
    - - arguments:
          parameters:
          - name: scan-id
            value: '{{`{{workflow.parameters.scan-id}}`}}'
          - name: repositoryId
            value: '{{`{{workflow.parameters.repository-id}}`}}'
          - name: integrationType
            value: '{{`{{workflow.parameters.integration-type}}`}}'
          - name: flow
            value: PR
          - name: prFlow
            value: '{{`{{=any(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
              ''$.scanners''), {#[''shouldRun'']}), {#[''cloneType''] == ''FULL''})
              ? any(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
              ''$.scanners''), {#[''shouldRun'']}), {#[''cloneType''] == ''DIFF''})
              ? ''DIFF_AND_FULL'' : ''FULL'' : ''DIFF''}}`}}'
          - name: branch
            value: '{{`{{workflow.parameters.branch-name}}`}}'
          - name: commit
            value: '{{`{{workflow.parameters.commit-hash}}`}}'
          - name: pr
            value: '{{`{{workflow.parameters.pr-number}}`}}'
          - name: targetBranch
            value: '{{`{{workflow.parameters.target-branch-name}}`}}'
          - name: filesPath
            value: '{{`{{workflow.parameters.files-path-output}}`}}'
          - name: filesCount
            value: '{{`{{workflow.parameters.files-count-output}}`}}'
          - name: planScanResult
            value: '{{`{{=steps[''plan-scan''].outputs.parameters[''plan-scan-result'']}}`}}'
          - name: customer-modules
            value: '{{`{{=workflow.parameters[''customer-modules'']}}`}}'
          - name: otelTraceId
            value: '{{`{{workflow.parameters.otel-trace-id}}`}}'
          - name: otelSpanId
            value: '{{`{{workflow.parameters.otel-span-id}}`}}'
        name: fetch-files
        templateRef:
          name: {{.Values.filesFetchingWorkflowTemplateName}}
          template: clone-runner
        when: '{{`{{= (sprig.int(workflow.parameters[''files-count-output'']) > 0 ? len(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
          ''$.scanners''), {#[''shouldRun'']})) > 0 : false) && (workflow.parameters[''enable-next-gen-data-collector'']
          != ''true'') }}`}}'
    - - arguments:
          parameters:
          - name: clone-path
            value: '{{`{{=steps[''fetch-files''].outputs.parameters[''clone-path'']}}`}}'
          - name: diff-clone-path
            value: '{{`{{=steps[''fetch-files''].outputs.parameters[''diff-clone-path'']}}`}}'
          - name: patch-file-mapping-path
            value: '{{`{{=steps[''fetch-files''].outputs.parameters[''patch-file-mapping-path'']}}`}}'
          - name: files-path
            value: '{{`{{workflow.parameters.files-path-output}}`}}'
          - name: previous-scan-id
            value: '{{`{{workflow.parameters.previous-scan-id}}`}}'
          - name: scanner-name
            value: '{{`{{item.scannerName}}`}}'
          - name: scan-configuration
            value: '{{`{{=toJson(toJson(jsonpath(workflow.parameters[''scan-configuration''],
              ''$.scanners'')))}}`}}'
          - name: excluded-paths
            value: '{{`{{=toJson(toJson(jsonpath(workflow.parameters[''scan-configuration''],
              ''$.excludedPaths'')))}}`}}'
          - name: clone-type
            value: '{{`{{item.cloneType}}`}}'
          - name: repository-is-public
            value: '{{`{{workflow.parameters.repository-is-public}}`}}'
          - name: repository-is-archived
            value: '{{`{{workflow.parameters.repository-is-archived}}`}}'
          - name: target-branch
            value: '{{`{{workflow.parameters.target-branch-name}}`}}'
          - name: scanners-plan
            value: '{{`{{=toJson(steps[''plan-scan''].outputs.parameters[''plan-scan-result''])}}`}}'
          - name: otel-trace-id
            value: '{{`{{workflow.parameters.otel-trace-id}}`}}'
          - name: otel-span-id
            value: '{{`{{workflow.parameters.otel-span-id}}`}}'
          - name: git-provider-domain
            value: '{{`{{workflow.parameters.git-provider-domain}}`}}'
        continueOn:
          error: true
          failed: true
        name: scan-enrich-and-comment
        template: scan-enrich-and-comment
        when: '{{`{{= (sprig.int(workflow.parameters[''files-count-output'']) > 0 ? len(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
          ''$.scanners''), {#[''shouldRun'']})) > 0 : false) && (workflow.parameters[''enable-next-gen-data-collector'']
          != ''true'') }}`}}'
        withParam: '{{`{{=toJson(filter(jsonpath(steps[''plan-scan''].outputs.parameters[''plan-scan-result''],
          ''$.scanners''), {#[''shouldRun'']}))}}`}}'
    - - name: trigger-scans-management-calculation
        template: trigger-scans-management-calculation
        when: '{{`{{= workflow.parameters[''enable-next-gen-data-collector''] != ''true''
          }}`}}'
  - name: failed-exit-handler
    steps:
    - - arguments:
          parameters:
          - name: scan-id
            value: '{{`{{workflow.parameters.scan-id}}`}}'
          - name: repository-id
            value: '{{`{{workflow.parameters.repository-id}}`}}'
          - name: branch-name
            value: '{{`{{workflow.parameters.branch-name}}`}}'
          - name: trigger-type
            value: CICD
          - name: integration-id
            value: '{{`{{workflow.parameters.integration-id}}`}}'
          - name: target-branch
            value: '{{`{{workflow.parameters.target-branch-name}}`}}'
          - name: pr-number
            value: '{{`{{workflow.parameters.pr-number}}`}}'
          - name: commit-hash
            value: '{{`{{workflow.parameters.commit-hash}}`}}'
          - name: block-pr-on-error
            value: '{{`{{=jsonpath(workflow.parameters[''scan-configuration''], ''$.prScanning'')[''blockOnError'']}}`}}'
          - name: wf-start-date
            value: '{{`{{workflow.creationTimestamp}}`}}'
          - name: previous-scan-id
            value: '{{`{{workflow.parameters.previous-scan-id}}`}}'
          - name: otel-trace-id
            value: '{{`{{workflow.parameters.otel-trace-id}}`}}'
          - name: otel-span-id
            value: '{{`{{workflow.parameters.otel-span-id}}`}}'
        name: scan-failure-handler
        templateRef:
          name: scan-failure-handler-template
          template: scan-failure-handler
  workflowMetadata:
    labelsFrom:
      name:
        expression: workflow.name
      uid:
        expression: workflow.uid
      scan-id:
        expression: workflow.parameters['scan-id']
      trace-id:
        expression: workflow.parameters['trace-id']
      repository-id:
        expression: sprig.trimAll('.-_', sprig.trunc(63, sprig.regexReplaceAll('[^A-Za-z0-9_.-]',
          '-', '{{`{{workflow.parameters.repository-id}}`}}')))
      repository-name:
        expression: sprig.trimAll('.-_', sprig.trunc(63, sprig.regexReplaceAll('[^A-Za-z0-9_.-]',
          '-', '{{`{{workflow.parameters.repository-name}}`}}')))
      repository-asset-id:
        expression: sprig.trimAll('.-_', sprig.trunc(63, sprig.regexReplaceAll('[^A-Za-z0-9_.-]',
          '-', '{{`{{workflow.parameters.repository-asset-id}}`}}')))
      integration-id:
        expression: sprig.trimAll('.-_', sprig.trunc(63, sprig.regexReplaceAll('[^A-Za-z0-9_.-]',
          '-', '{{`{{workflow.parameters.integration-id}}`}}')))
