_component_name: "'cwp-compliance-uai-scanner'"

fullnameOverride: 'globals.st_resource_prefix + "-cwp-k8s-uai-scanner"'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

env:
  ENABLE_CLOUD_POSTURE: "'true'"

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
  limits:
    cpu: '"200m"'
    memory: '"512Mi"'
