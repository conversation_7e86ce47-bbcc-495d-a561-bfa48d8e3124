_component_name: "'cloudsec-rules-resident-init'"

namespaceOverride: globals.st_namespace
fullnameOverride: local.namespaceOverride + "-cloudsec-rules-resident-init"
tenant_id: tenant.lcaas_id

image:
  repository: globals.gcr + "/prismacloud/rule-management-service-resident-init"
  tag: '"496e134a7b854be44de10cfcd5745b1de9110fde"'

env:
  RESIDENT: tenant.lcaas_id
  TENANT_ID: tenant.lcaas_id
  DATABASE: '"cspm_" + tenant.lcaas_id'
  COMMON_INIT_VERSION: "'1.0.0'"
  RMS_API_INIT_VERSION: "'1.0.0'"
  RMS_COMMON_POSTGRES_API_SCHEMA: "'public'"
  CLOUDSEC_RW_POSTGRES_USER:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres"'
        key: "'POSTGRES_CSPM_USERNAME'"
        optional: false
  CLOUDSEC_RW_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres-ps"'
        key: "'POSTGRES_CSPM_PASSWORD'"
        optional: false
  CLOUDSEC_COMMON_POSTGRES_SVC: "'postgres.xdr-mt.svc.cluster.local'"
  CLOUDSEC_COMMON_POSTGRES_PORT: "'5432'"
  CLOUDSEC_COMMON_POSTGRES_USER: "'root'"
  CLOUDSEC_COMMON_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cloudsec-secrets"'
        key: "'CLOUDSEC_COMMON_POSTGRES_PASSWORD'"
        optional: false