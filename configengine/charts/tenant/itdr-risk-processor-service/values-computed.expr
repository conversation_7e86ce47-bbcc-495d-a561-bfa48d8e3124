_component_name: "'itdr-risk-processor-service'"
_component_image: "'itdr-risk-processor'"

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.st_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'postgres_password'"
        optional: false
  GCP_PROJECT_ID: "tenant.project_id"
  ITDR_CASE_UPDATE_SUBSCRIPTION_ID: '"itdr-update-risk-processor-" + tenant.lcaas_id + "-sub"'
  ITDR_CASE_UPDATE_DLQ_SUBSCRIPTION_ID: '"itdr-update-risk-processor-dlq-" + tenant.lcaas_id + "-sub"'
  ITDR_POSTGRES_DATABASE: 'tenant.lcaas_id+"_itdr_risk_processor"'
  POSTGRES_HOST: globals.env.PGHOST
  ITDR_POSTGRES_MIGRATION_DATABASE_NAME: 'tenant.lcaas_id+"_itdr_risk_processor"'

extraVolumes: {}
service: {}

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-itdr"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-itdr-feature-flags"'
      optional: false

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"itdr-risk-processor-service@" + tenant.project_id + ".iam.gserviceaccount.com"'

hpa:
  minReplicas: 1
  maxReplicas: 3

  metrics:
    - external:
        metric:
          name: '"pubsub.googleapis.com|subscription|num_undelivered_messages"'
          selector:
            matchLabels:
              resource.labels.project_id: "tenant.project_id"
              resource.labels.subscription_id: '"itdr-update-risk-processor-" + tenant.lcaas_id + "-sub"'
        target:
          type: '"AverageValue"'
          averageValue: '"2500"'
      type: '"External"'
