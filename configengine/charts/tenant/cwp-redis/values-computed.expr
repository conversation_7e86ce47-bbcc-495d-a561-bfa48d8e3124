fullnameOverride: 'globals.st_resource_prefix + "-cwp-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'cwp_redis_password'"
        optional: false



snapshots: 
  dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
  hourlyEnabled: 'region.viso_env != "dev"'
  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"cwp-redis-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"cwp-redis-hourly-" + tenant.lcaas_id'