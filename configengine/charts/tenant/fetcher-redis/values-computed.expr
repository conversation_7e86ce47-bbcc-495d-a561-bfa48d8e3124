fullnameOverride: 'globals.st_resource_prefix + "-fetcher-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-secrets"'
        key: "'fetcher_redis_password'"
        optional: false

args:
  maxclients: 'infra_ff.enable_megatron_xdr ? "30000" : "20000"'
