_component_name: "'dspm-mac-worker'"
_component_image: "'dspm-mac'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

_cert_command: |-
  [
    "cp -R /opt/java/openjdk/lib/security/* /cacerts/ &&",
    "awk 'BEGIN {c=0;} /-----BEGIN CERTIFICATE-----/ {filename=sprintf(\"/tmp/cert_%d.crt\", c++);} {print > filename}' /etc/certs/egress.crt &&",
    "i=0; for certfile in /tmp/cert_*.crt; do keytool -import -noprompt -trustcacerts -alias \"proxy_crt_$i\" -file \"$certfile\" -keystore /cacerts/cacerts -storepass changeit 2>/dev/null; i=$((i + 1)); done &&",
    "rm -f /tmp/cert_*.crt"
  ]

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : "2Gi"'

env:
  SPRING_PROFILES_ACTIVE: "'prod,messaging,worker'"

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-mac"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-mac-worker"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-mac-feature-flags"'
      optional: false

initContainers:
  initTemporal:
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
  initCert:
    image:
      registry: globals.gcr
      tag: "local._images.component.tag ?? local._images.family.tag"
      repository: "local._images.component.repository ?? local._images.family.repository"
    command:
      - "'sh'"
      - "'-c'"
      - join(local._cert_command, "\n") + "\n"

hpa:
  maxReplicas: 'region.viso_env == "dev" ? "4" : "10"'