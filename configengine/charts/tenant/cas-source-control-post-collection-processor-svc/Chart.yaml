apiVersion: v2
name: source-control-post-collection-processor-svc
description: A Helm chart for source-control-post-collection-processor-svc; Owner cas/datastream
type: application
version: 0.1.0
appVersion: "0.1.0"

dependencies:
  - name: service-library-chart
    version: "*.*.*"
    repository: file://../cas-library-charts/service/
  - name: common
    version: '*.*.*'
    repository: file://../common


annotations:
  # Please verify panw.com/deploy-eval value with the following:
  # 1. DevOps Platform SKUs: https://docs.google.com/spreadsheets/d/1Nv7TzWAuCp97FGs-0igIDOdAqO89I_zovmWUiKsvGEA
  # 2. Available values: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/-/blob/dev/configengine/inputs/globals.expr?ref_type=heads#L314
  panw.com/deploy-eval: |
    globals.enable_cloud_posture ||
    globals.enable_cloud_appsec
  owner.panw/group: cas
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/cas-source-control-post-collection-processor-svc
  owner.panw/team: datastream
  owner.panw/people-slack-handle-team-lead: '@shkatz'
  owner.panw/people-slack-handle-owners-group: '@lbecker'