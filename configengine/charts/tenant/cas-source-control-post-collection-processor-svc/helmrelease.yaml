apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: source-control-post-collection-processor-svc
  namespace: "{{ .globals.cas_namespace }}"
spec:
  releaseName: source-control-post-collection-processor-svc
  chart:
    spec:
      chart: tenant/source-control-post-collection-processor-svc
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
      version: 0.2.0
  driftDetection:
    mode: enabled
  install:
    disableWait: true
    createNamespace: true
    remediation:
      retries: -1
  interval: 1m0s
  targetNamespace: "{{ .globals.cas_namespace }}"
  rollback:
    disableWait: true
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
    remediation:
      retries: -1
  values: {}