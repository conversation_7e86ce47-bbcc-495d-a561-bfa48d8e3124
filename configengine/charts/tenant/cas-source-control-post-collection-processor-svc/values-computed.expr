
_component_name: "'post-collection-processor'"

namespaceOverride: "globals.cas_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"
  pullPolicy: 'region.is_dev ? "Always" : "IfNotPresent"'

tenant:
  id: 'tenant.lcaas_id'
  isMultiTenant: 'tenant.is_metro_tenant'

vsg:
  project: "tenant.project_id"

resources:
  limits:
    cpu: '"2"'
    memory: '"2Gi"'
    ephemeral-storage: '"500Mi"'
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "50m" : "500m"'
    memory: '"512Mi"'
    ephemeral-storage: '"50Mi"'