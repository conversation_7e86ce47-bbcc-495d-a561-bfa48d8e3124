fullnameOverride: post-collection-processor

namespaceOverride: cas # replace with "" after the migration to Cortex new platform

# -- service number of replicas
replicaCount: 1

envFrom: []

image: {}

# -- secret to use for image pulling
imagePullSecrets: []

deployment:
  strategy:
    type: RollingUpdate

# -- String to partially override "service.fullname" template
nameOverride: ""

# -- Feature Flags Configuration
featureFlags: false

serviceAccount:
  # -- # Set to false to prevent automatic mounting of the service account token into the pod
  automountServiceAccountToken: true
  # -- The name of the service account to use
  # cas service account would be created using cas-service-accounts chart
  name: source-control-cas

# -- Annotations to be added to the service pods
podAnnotations: {}

# Node selector to schedule pods on specific nodes with the given label (relevant when deploying to GCP tenant)
nodeSelector:
  xdr-pool: wi-dynamic

# -- Security Context to set on the pod level
podSecurityContext: {}
  # fsGroup: 2000

# -- Security Context to set on the container level
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  # -- Wheter to enable auto-scaling.
  enabled: true
  # -- Sets the type of the Service
  type: ClusterIP
  # -- Service port
  port: 80
  # -- Application target port
  targetPort: 3000

# -- Prometheus configuration for scraping metrics from the service.
prometheus: {}
  # enabled: "false"  # Set to "true" to enable scraping, otherwise defaults to "false"
  # port: 3000  # Optional, specify a custom application port for Prometheus scraping, by default it is 3000

# containers' liveness probe
# The liveness probe checks the '/healthz' endpoint to see if the container is healthy.
# If this check fails, Kubernetes will restart the container to try to restore its healthy state.
livenessProbe:
  httpGet:
    path: /healthz
    port: http

# containers' readiness probe
# The readiness probe checks the '/readyz' endpoint to determine if the container is ready to accept traffic.
# If this check fails, the container is removed from service endpoints until it passes.
readinessProbe:
  httpGet:
    path: /readyz
    port: http

autoscaling:
  # -- Wheter to enable auto-scaling.
  enabled: false
  # -- Minimum reoplicas.
  minReplicas: 1
  # -- Maximum reoplicas.
  maxReplicas: 5
  # -- CPU utilization percentage threshold.
  targetCPUUtilizationPercentage: 85
  # -- Memory utilization percentage threshold.
  targetMemoryUtilizationPercentage: 85

# -- Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# -- Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: []
restartPolicy: ""
priorityClassName: ""
topologySpreadConstraints: {}

containerSecurityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000


terminationGracePeriodSeconds: 30
deploymentAnnotations:
  reloader.stakater.com/auto: "true"

startupProbe: {}

deploymentLabels:
  group: cas
  team: datastream
podLabels:
  group: cas
  team: datastream

# For more information, refer to VSG README.md: https://gitlab.xdr.pan.local/xdr/development/komodo/-/blob/master/README.md
vsg:
  create: false
  enabled: false
  pollingMinutes: 5
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}

horizontalPodAutoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 120
  metrics:
    - type: Resource
      resource:
        name: "cpu"
        target:
          averageUtilization: 85
          type: "Utilization"
    - type: Resource
      resource:
        name: "memory"
        target:
          averageUtilization: 85
          type: "Utilization"

env:
  MIN_LOG_LEVEL:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: MIN_LOG_LEVEL
        optional: true
  CI_BUILD_LOGS_BUCKET:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: CI_BUILD_LOGS_BUCKET
        optional: false
  METRICS_NAMESPACE: data-collector

  LCAAS_ID:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: LCAAS_ID
        optional: false
  PROJECT_ID:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: GCPCONF_PROJECT_ID

  MONGO_DB_DATABASE_NAME: cas
  MONGO_DB_HOST_NAME:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: MONGO_DB_HOST_NAME
        optional: false
  MONGO_DB_USERNAME: root
  MONGO_DB_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: mongodb_password
        optional: false

  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_password
        optional: false
  REDIS_HOST:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_host
        optional: false

  PUBSUB_FAILURES_BUCKET:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PUBSUB_FAILURES_BUCKET
        optional: false

  PERIODIC_DATA_COLLECTOR_RESULTS_TOPIC:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_DATA_COLLECTOR_RESULTS_TOPIC

  PERIODIC_POST_COLLECTION_PROCESSOR_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_POST_COLLECTION_PROCESSOR_SUBSCRIPTION
