_component_name: "'alyx-migration'"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

_env:
  UnifiedAssetInventoryConf_SINGLESTORE_PASS:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'singlestore_password'"
        optional: false
  EMAILO365AUTHORIZATIONFLOW_CLIENT_ID:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-email-collector-secrets"'
        key: "'email_client_id'"
        optional: false
  EMAILO365AUTHORIZATIONFLOW_CLIENT_SECRET:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-email-collector-secrets"'
        key: "'email_client_secret'"
        optional: false
  EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_ID:
    valueFrom:
      secretKeyRef:
        key: '"email_regional_client_id"'
        name: 'tenant.lcaas_id + "-email-collector-secrets"'
        optional: false
  EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_SECRET:
    valueFrom:
      secretKeyRef:
        key: '"email_regional_client_secret"'
        name: 'tenant.lcaas_id + "-email-collector-secrets"'
        optional: false
  XpansePolicyManagerConf_client_id:
    valueFrom:
      secretKeyRef:
        name: "'xpanse-tenant-credentials-client-id-secret'"
        key: "'xpanse-tenant-credentials-client-id'"
        optional: false
  XpansePolicyManagerConf_client_secret:
    valueFrom:
      secretKeyRef:
        name: "'xpanse-tenant-credentials-client-secret-secret'"
        key: "'xpanse-tenant-credentials-client-secret'"
        optional: false
  XpanseClientConf_client_id:
    valueFrom:
      secretKeyRef:
        name: "'xpanse-tenant-credentials-client-id-secret'"
        key: "'xpanse-tenant-credentials-client-id'"
        optional: false
  XpanseClientConf_client_secret:
    valueFrom:
      secretKeyRef:
        name: "'xpanse-tenant-credentials-client-secret-secret'"
        key: "'xpanse-tenant-credentials-client-secret'"
        optional: false
  XpanseEmbeddedPolicyConf_global_storage_project_id:
    value: "tenant.project_id"
  XpanseEmbeddedPolicyConf_global_storage_bucket_name:
    value: "globals.expanse_embedded_policy_conf_global_storage_bucket_name"
  PARTYZAURUSCONF_XQLE_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'gonzo_redis_password'"
        optional: false
  AGENTAPICONF_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'agent_api_redis_password'"
        name: "globals.tenant_secrets"
        optional: false
  AssetManagementConf_redlock_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'assets_redis_password'"
        optional: false
  XSOARELASTICCONF_HOST:
    value: "'elastic-es-xsoar'"
  XSOARELASTICCONF_USERNAME:
    value: "'elastic'"
  XSOARELASTICCONF_PORT:
    value: "'9200'"
  XSOARELASTICCONF_SCHEMA:
    value: "'https'"
  XSOARELASTICCONF_VERIFY_CERTS:
    value: "'False'"
  XSOARELASTICCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "'elastic-es-elastic-user'"
        key: "'elastic'"
        optional: false
  UNIFIEDASSETINVENTORYCONF_NEO4J_PASS:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'neo4j_password'"
        optional: false
  CWPREDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'cwp_redis_password'"
        name: "globals.tenant_secrets"
        optional: false

env:
  XpanseClientConf_client_id: "license.enable_asm ? local._env.XpanseClientConf_client_id : nil"
  XpanseClientConf_client_secret: "license.enable_asm ? local._env.XpanseClientConf_client_secret : nil"
  XpansePolicyManagerConf_client_id: "license.enable_asm ? local._env.XpansePolicyManagerConf_client_id : nil"
  XpansePolicyManagerConf_client_secret: "license.enable_asm ? local._env.XpansePolicyManagerConf_client_secret : nil"
  XpanseEmbeddedPolicyConf_global_storage_project_id: "license.enable_asm ? local._env.XpanseEmbeddedPolicyConf_global_storage_project_id : nil"
  XpanseEmbeddedPolicyConf_global_storage_bucket_name: "license.enable_asm ? local._env.XpanseEmbeddedPolicyConf_global_storage_bucket_name : nil"
  EMAILO365AUTHORIZATIONFLOW_CLIENT_ID: "!region.is_fedramp ? local._env.EMAILO365AUTHORIZATIONFLOW_CLIENT_ID : nil"
  EMAILO365AUTHORIZATIONFLOW_CLIENT_SECRET: "!region.is_fedramp ? local._env.EMAILO365AUTHORIZATIONFLOW_CLIENT_SECRET : nil"
  EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_ID: "!region.is_fedramp ? local._env.EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_ID : nil"
  EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_SECRET: "!region.is_fedramp ? local._env.EMAILO365AUTHORIZATIONFLOW_REGION_V1_CLIENT_SECRET : nil"
  AGENTAPICONF_REDIS_PASSWORD: "infra_ff.enable_redis_agent_api ? local._env.AGENTAPICONF_REDIS_PASSWORD : nil"
  PARTYZAURUSCONF_XQLE_REDIS_PASSWORD: "globals.gonzo_redis ? local._env.PARTYZAURUSCONF_XQLE_REDIS_PASSWORD : nil"
  UnifiedAssetInventoryConf_SINGLESTORE_PASS: "infra_ff.enable_cortex_platform ? local._env.UnifiedAssetInventoryConf_SINGLESTORE_PASS : nil"
  XSOARELASTICCONF_HOST: "license.is_xsoar ? local._env.XSOARELASTICCONF_HOST : nil"
  XSOARELASTICCONF_USERNAME: "license.is_xsoar ? local._env.XSOARELASTICCONF_USERNAME : nil"
  XSOARELASTICCONF_PORT: "license.is_xsoar ? local._env.XSOARELASTICCONF_PORT : nil"
  XSOARELASTICCONF_SCHEMA: "license.is_xsoar ? local._env.XSOARELASTICCONF_SCHEMA : nil"
  XSOARELASTICCONF_PASSWORD: "license.is_xsoar ? local._env.XSOARELASTICCONF_PASSWORD : nil"
  XSOARELASTICCONF_VERIFY_CERTS: "license.is_xsoar ? local._env.XSOARELASTICCONF_VERIFY_CERTS : nil"
  ThirdPartySaas_service_account: '"collector-" + tenant.lcaas_id + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
  MAILEXCHANGEROUTER_SENSITIVE_DATA_KEY:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'mx_router_data_key'"
        optional: false
  PUBLICAPI_ENCRYPTION_256KEY:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'publicapi_encryption_256key'"
        optional: false
  PUBLICAPI_ENCRYPTION_KEY:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'publicapi_encryption_key'"
        optional: false
  MYSQLCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'mysql_password'"
        name: 'tenant.is_metro_tenant ? globals.st_resource_prefix + "-api-mysql" : globals.tenant_secrets'
        optional: false
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'redis_password'"
        optional: false
  ANALYTICSCONF_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'analytics_redis_password'"
        optional: false
  REPORTSCONF_REPORTS_ENCRYPTION_KEY:
    valueFrom:
      secretKeyRef:
        name: globals.tenant_secrets
        key: "'report_encryption_key'"
        optional: false
  ScyllaConf_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'scylla_password'"
        optional: false
  ScyllaConfXCloud_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "globals.scylla_conf_xcloud_password"
        optional: false
  XCLOUDREDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "globals.xcloud_redisconf_password"
        optional: false
  MAILINGCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'sendgrid_api_key'"
        optional: false
  AssetManagementConf_redis_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'assets_redis_password'"
        optional: false
  AssetManagementConf_redlock_password:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'assets_redis_password'"
        optional: false
  ScyllaConf_user:
    value: "globals.scylla_user"
  no_proxy:
    value: '"$(no_proxy)," + globals.st_resource_prefix + "-frontend"'
  ScyllaConf_scylla_endpoint:
    value: "globals.scylla_endpoint"
  UNIFIEDASSETINVENTORYCONF_NEO4J_PASS: "infra_ff.enable_cortex_platform ? local._env.UNIFIEDASSETINVENTORYCONF_NEO4J_PASS : nil"
  CWPREDISCONF_PASSWORD: "infra_ff.enable_cortex_platform ? local._env.CWPREDISCONF_PASSWORD : nil"
  ALYX_MIGRATION_VALIDATE: false
  ALYX_MIGRATION_SCYLLA_TO_ALYX: 'infra_ff.enable_migrate_alyx_data_to_scylla ? false : true'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-feature-flags"'
      optional: false

namespace: '"xdr-st"'
enable: 'infra_ff.enable_alyx_migration && infra_ff.enable_alyx && infra_ff.enable_scylla'
backoffLimit: 3
serviceAccountName: '"api-pod"'
image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"