apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: alyx-migration
  namespace: alyx
spec:
  chart:
    spec:
      chart: tenant/alyx-migration
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
        interval: "{{ .globals.flux.chart_pull_interval }}"
  driftDetection:
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    disableWait: true
    createNamespace: true
    remediation:
      retries: -1
  interval: 1m0s
  rollback:
    disableWait: true
  targetNamespace: alyx
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
  values: {}
  dependsOn:
    - name: dp-ipl-alyx
      namespace: "{{ .globals.dp_ipl_alyx_namespace }}"
    - name: dms
      namespace: {{ .globals.st_namespace }}
    - name: pipeline
      namespace: {{ .globals.st_namespace }}
    - name: xql-engine
      namespace: {{ .globals.st_namespace }}