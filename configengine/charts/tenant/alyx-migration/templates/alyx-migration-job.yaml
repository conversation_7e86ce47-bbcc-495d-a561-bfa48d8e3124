{{- if .Values.enable -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: "scylla-migrate-to-alyx-job"
  namespace: {{ .Values.namespace }}
  annotations:
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: {{ .Values.backoffLimit }}
  ttlSecondsAfterFinished: 86400
  activeDeadlineSeconds: 6000
  template:
    spec:
      restartPolicy: Never
      serviceAccountName: {{ .Values.serviceAccountName }}
      automountServiceAccountToken: true
      containers:
      - name: alyx-migrate
        image: {{ printf "%s/%s:%s" .Values.image.registry .Values.image.repository .Values.image.tag }}
        command: ["python", "/src/playbook_executor/playbooks/alyx_migration_playbook.py"]
        resources:
          requests:
            cpu: "2"
            memory: "4Gi"
        env: {{ (include "common.renderEnv" .Values.env) | nindent 10 }}
        envFrom: {{ toYaml .Values.envFrom | nindent 10 }}
{{- end -}}