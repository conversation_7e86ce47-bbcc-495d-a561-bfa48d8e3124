apiVersion: v1
appVersion: "1.0"
description: A Helm chart for Alyx Migration Job
name: alyx-migration
version: 1.0.0
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
annotations:
  "panw.com/deploy-eval": "(infra_ff.enable_pipeline || infra_ff.enable_cortex_platform) && !tenant.is_metro_tenant"
  owner.panw/group: dp
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/alyx-migration
  owner.panw/team: dml-data-pipeline
  owner.panw/team-slack-handle: '@pipeline-prod-owners'
  owner.panw/people-slack-handle-team-lead: '@etenne'
  owner.panw/people-slack-handle-owners-group: '@yneuman'