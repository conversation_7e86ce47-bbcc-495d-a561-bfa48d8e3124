neo4j:
  name: "'dp-neo4j-cluster'"
  edition: "'enterprise'"
  acceptLicenseAgreement: "'yes'"
  resources:
    cpu: "'1000m'"
    memory: '!infra_ff.is_enable_prod_spec ? "4Gi" : infra_ff.enable_megatron_xdr ? "32Gi" : "8Gi"'

fullnameOverride: "'dp-neo4j-cluster'"

image:
  customImage: 'region.is_fedramp ? globals.gcr + "/golden-images/neo4j:2025.10.1-enterprise" : globals.gcr + "/golden-images/neo4j:5.26.0-enterprise"'
volumes:
  data:
    mode: "'volumeClaimTemplate'"
    volumeClaimTemplate:
      storageClassName: "'ssd-storage'"
      accessModes:
        - "'ReadWriteOnce'"
      resources:
        requests:
          storage: "'100Gi'"

analytics:
  enabled: true

services:
  default:
    annotations:
      networking.gke.io/load-balancer-type: "'Internal'"
  neo4j:
    annotations:
      helm.sh/resource-policy: "'keep'"
      networking.gke.io/load-balancer-type: "'Internal'"
    cleanup:
      enabled: true
      image:
        registry: globals.gcr
        repository: "'golden-images/kubectl'"

podSpec:
  annotations:
    prometheus.io/port: "'2004'"
    prometheus.io/scrape: "'true'"

config:
  db.logs.query.enabled: "'INFO'"
  db.logs.query.threshold: "'10s'"
  dbms.security.auth_enabled: "'true'"
  db.logs.query.transaction.enabled: "'INFO'"
  db.logs.query.transaction.threshold: "'10s'"
  dbms.security.procedures.unrestricted: "'apoc.*'"
  server.metrics.prometheus.enabled: "'true'"
  server.metrics.prometheus.endpoint: "'0.0.0.0:2004'"

apoc_config:
  apoc.ttl.enabled: "'true'"

service_account_annotations:
   iam.gke.io/gcp-service-account: '"dp-neo4j-cluster" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
