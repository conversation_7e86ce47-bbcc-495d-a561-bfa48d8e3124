_component_name: "'prometheus'"

namespaceOverride: globals.monitoring_namespace
fullnameOverride: '"monitoring-" + tenant.lcaas_id + "-prometheus"'

is_platform: "infra_ff.enable_cortex_platform"

region: "region.viso_env"

needs_large_spec: license.monthly_tb_licenses > 35 || license.total_agents > 10000

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "v3.5.0" : "v3.4.2"'
resources:
  requests:
    cpu: |-
      infra_ff.enable_megatron_xdr ? "2" : local.needs_large_spec ? "1" :
      license.is_small_epp ? "70m" : "0.3"
    memory: |-
      infra_ff.enable_megatron_xdr ? "12Gi" :
      local.needs_large_spec ? "8Gi" : license.is_small_epp ? "512Mi" : "1Gi"
  limits:
    cpu: |-
      infra_ff.enable_megatron_xdr ? "3" : local.needs_large_spec ?
      "2" : "1"
    memory: |-
      infra_ff.enable_megatron_xdr ? "14Gi" : local.needs_large_spec ?
       "8Gi" : "2Gi"

nodeSelector:
  xdr-pool: 'license.is_small_epp ? "wi-static" : "wi-dynamic"'

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

external_labels:
  tenant_type: 'infra_ff.enable_paying_monitoring ? "paying" : tenant.authcode'
  product_type: "lower(license.product_type)"
  product_tier: "lower(license.product_tier)"
  lcaas_id: "tenant.lcaas_id"
  xdr_id: "tenant.xdr_id"
  tenant_id: "lower(tenant.project_id)"
  product_code: "lower(license.product_code)"

priorityClassName: "globals.priority_classes.monitoring_priority"
