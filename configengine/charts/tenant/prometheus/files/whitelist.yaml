kubernetes-pods:
  regex:
    - active_cloud_connectors
    - active_cloud_outposts
    - agent_mgmt_processor_pubsub_consumer_errors_total
    - agent_mgmt_processor_pubsub_consumer_inflight_messages
    - agent_mgmt_processor_pubsub_consumer_latency_seconds
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_count
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_sum
    - agent_mgmt_processor_pubsub_consumer_messages_total
    - agent_to_group_calculation_count
    - agent_to_group_calculation_duration
    - alert_monitoring_metrics_bigquery_alert_status_counts
    - alert_monitoring_metrics_mysql_alert_status_counts
    - alert_monitoring_metrics_close_incident_with_open_alert_counts
    - alert_monitoring_metrics_open_alerts_per_incident_counts
    - alert_monitoring_metrics_alert_internal_ids_per_external_id
    - alert_monitoring_metrics_mysql_incident_status_counts
    - alert_monitoring_metrics_bigquery_incident_status_counts
    - alert_monitoring_metrics_duplicate_alert_counts
    - alert_monitoring_metrics_incident_alert_count_mismatch
    - alert_monitoring_metrics_incident_with_no_alert_counts
    - alert_monitoring_metrics_open_alert_older_than_10_days_counts
    - alyx_prefix_cache
    - alyx_rdb_block_cache_stats
    - alyx_rdb_compaction_time_micros
    - alyx_rdb_compression_time
    - alyx_rdb_full_bloom_filter_ratio
    - alyx_rdb_non_last_level_seek
    - alyx_rdb_op_bytes
    - alyx_rdb_operation_latency_micros
    - alyx_rdb_prefix_bloom_filter_ratio
    - alyx_rdb_write_stall_micros
    - alyx_request_duration_seconds_bucket
    - alyx_request_duration_seconds_count
    - alyx_server_connect_events_count
    - alyx_server_connection_failures
    - alyx_server_connections_total
    - alyx_server_request_failures
    - alyx_client_latency_seconds_bucket
    - alyx_client_latency_seconds_count
    - alyx_client_requests_failed_total
    - alyx_client_requests_total
    - alyx_dao_eal_num_of_items_total
    - alyx_dao_eal_operation_latency_seconds_total
    - alyx_dao_eal_requests_count_total
    - alyx_dss_ad_fetch_batch_size_bucket
    - alyx_dss_ad_fetch_duration_seconds_bucket
    - analytics__count_events_that_were_fetched_from_bq_total
    - analytics__count_events_that_were_fetched_from_gcs_total
    - analytics__tenant_have_reached_enrichment_limit
    - analytics_active_hosts
    - analytics_content_delta_between_requested_sync_to_sync_finished
    - analytics_content_delta_time_from_insertion
    - analytics_content_loader_data_loader_provider_data_updater_last_update_time
    - analytics_content_loader_data_loader_provider_last_update_time
    - analytics_content_loader_data_updater_failed_update_provider
    - analytics_content_loader_data_updater_fatal_error
    - analytics_content_loader_data_updater_last_execution_time
    - analytics_content_loader_data_updater_provider_failed_update_entity
    - analytics_content_loader_data_updater_provider_last_update_time
    - analytics_content_loader_data_updater_provider_total_time
    - analytics_correlations_.+
    - analytics_cycle_completed_analytics_product_count
    - analytics_cycle_profile_time_spent
    - analytics_cycle_total_analytics_product_count
    - analytics_cycle_running
    - analytics_de_v2__batches_processed_wall_time_count
    - analytics_de_v2__batches_processed_wall_time_sum
    - analytics_de_v2__events_processed_count
    - analytics_de_v2__events_processed_per_type_sum
    - analytics_de_v2__events_processed_sum
    - analytics_de_v2_detection_component_wall_time_sum
    - analytics_de_v2_detection_processing_part_wall_time_sum
    - analytics_de_v2_internal_queue_size
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total
    - analytics_de_v2_profile_engine_api_keys_count_sum
    - analytics_de_v2_profile_engine_api_request_time_sum
    - analytics_de_v2_rocks_keys_count_count
    - analytics_de_v2_rocks_keys_count_sum
    - analytics_de_v2_rocks_request_time_count
    - analytics_de_v2_rocks_request_time_sum
    - analytics_de_v2_vectorized_matcher_compile_detectors_sum
    - analytics_de_v2_vectorized_matcher_layer_wall_time_sum
    - analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total
    - analytics_de_v2_vectorized_matcher_wall_time_sum
    - analytics_decay_time_per_staging_table_count
    - analytics_decay_time_per_staging_table_sum
    - analytics_decision_time_per_staging_table_count
    - analytics_decision_time_per_staging_table_sum
    - analytics_delta_time_from_last_successful_decider_calculation
    - analytics_detection_component_process_time_count
    - analytics_detection_component_process_time_sum
    - analytics_detection_emitted_alerts_count
    - analytics_detection_emitted_alerts_sum
    - analytics_detection_engine_consumer_nacks_total
    - analytics_detection_hit_publish_time_count
    - analytics_detection_hit_publish_time_sum
    - analytics_detection_num_of_analytics_product_access_total
    - analytics_detection_outer_udf_execution_time_count
    - analytics_detection_outer_udf_execution_time_sum
    - analytics_detection_profile_matcher_get_profile_from_db_time_count
    - analytics_detection_profile_matcher_get_profile_from_db_time_sum
    - analytics_detection_state_populator_ingestion_rows_total
    - analytics_detection_state_populator_ingestion_time_count
    - analytics_detection_state_populator_ingestion_time_sum
    - analytics_detection_udf_execution_time_count
    - analytics_detection_udf_execution_time_sum
    - analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time
    - analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time
    - analytics_dss_last_updated_on_last_change
    - analytics_dss_sync_times_difference
    - analytics_dynamic_profile_updater_time_count
    - analytics_dynamic_profile_updater_time_sum
    - analytics_enabled
    - analytics_flood_stuck_alerts_total
    - analytics_flood_tasks_total
    - analytics_global_flood_total
    - analytics_global_profiles_contribution_failure
    - analytics_installed_content_version
    - analytics_llm_max_daily_requests_reached
    - analytics_llm_requests_bucket
    - analytics_llm_requests_count
    - analytics_llm_requests_sum
    - analytics_llm_task_success_rate_total
    - analytics_llm_task_success_rate_created
    - analytics_local_flood_total
    - analytics_objects_table_last_creation_time
    - analytics_product_consecutive_stage_failures
    - analytics_product_last_successful_api_table_creation_timestamp
    - analytics_profile_.+
    - analytics_rocksdb_num_keys_query
    - analytics_rocksdb_num_requests
    - analytics_rocksdb_response_time_count
    - analytics_rocksdb_response_time_sum
    - analytics_rocksdb_backup_download_failed
    - analytics_rocksdb_compaction_tasks
    - analytics_role_calculation_delay
    - analytics_role_count
    - analytics_task_processor_parse_metablob_error_created
    - analytics_task_processor_parse_metablob_error_total
    - analytics_task_processor_process_message_time_count
    - apisec_asset_manager_asset_manager_delete_assets_total_errors
    - apisec_asset_manager_asset_manager_get_assets_retention_total_errors
    - apisec_asset_manager_asset_manager_get_assets_total_errors
    - apisec_asset_manager_asset_manager_publish_assets_total_errors
    - apisec_asset_manager_assets_batch_size
    - apisec_asset_manager_assets_retention_flow_duration_seconds_bucket
    - apisec_asset_manager_assets_retention_flow_duration_seconds_sum
    - apisec_asset_manager_assets_retention_flow_duration_seconds_count
    - apisec_asset_manager_delete_asset_messages_total
    - apisec_asset_manager_get_assets_status_code
    - apisec_asset_manager_process_specs_counter
    - apisec_asset_manager_publish_uai_flow_duration_seconds_bucket
    - apisec_asset_manager_publish_uai_flow_duration_seconds_count
    - apisec_asset_manager_publish_uai_flow_duration_seconds_sum
    - apisec_asset_manager_pubsub_input_total_errors
    - apisec_asset_manager_total_assets_ETL_timeouts
    - apisec_asset_manager_total_delete_asset_messages
    - apisec_asset_manager_total_number_of_input_DTOs
    - apisec_asset_manager_total_number_of_pubsub_input_messages
    - apisec_asset_manager_total_publish_asset_messages
    - apisec_asset_manager_total_retries_for_get_assets_api_call
    - apisec_asset_manager_total_UAI_asset_ingestion_errors
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_count
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_sum
    - apisec_asset_manager_filtered_invalid_assets_before_publish
    - apisec_asset_manager_total_number_of_dtos_with_uncommon_status_code
    - apisec_bff_api_concurrent_requests
    - apisec_bff_api_latency_seconds_bucket
    - apisec_bff_api_request_size_bytes_bucket
    - apisec_bff_api_requests_total
    - apisec_bff_api_response_size_bytes_bucket
    - apisec_enricher_apigw_handle_metablob_time_ms
    - apisec_enricher_apigw_http_transactions_creation_count
    - apisec_enricher_apigw_http_transactions_creation_error_count
    - apisec_enricher_apigw_http_transactions_creation_error_on_second_time_count
    - apisec_enricher_apigw_http_transactions_creation_on_second_time_count
    - apisec_enricher_classification_time_seconds_bucket
    - apisec_enricher_classification_time_seconds_count
    - apisec_enricher_classification_time_seconds_sum
    - apisec_enricher_classification_total
    - apisec_enricher_classification_error_total
    - apisec_enricher_find_geo_ip_location_time_ms
    - apisec_enricher_http_handler_handle_metablob_time_ms
    - apisec_enricher_metablob_handler_not_found_count
    - apisec_enricher_metablob_parsing_errors_count
    - apisec_enricher_parsing_rules_raw_data_into_dto_errors_count
    - apisec_enricher_pubsub_callback_time_ms
    - apisec_enricher_received_pubsub_msgs_count
    - apisec_enricher_send_http_transactions_to_groupping_pubsub_count
    - apisec_enricher_set_module_sources_errors_count
    - apisec_enricher_enricher_initialization_errors
    - apisec_enricher_partial_session_cache_initialization_failure
    - apisec_enricher_edr_data_without_apisec_events_count
    - apisec_enricher_error_parsing_metablob_count
    - apisec_enricher_error_publish_http_transactions_count
    - apisec_enricher_error_vendor_product_not_exist_count
    - apisec_enricher_find_geo_ip_errors_count
    - apisec_enricher_handle_unreadable_metablob_count
    - apisec_enricher_timestamp_parsing_error_count
    - apisec_grouping_service_api_model_collapse_per_depth_per_height
    - apisec_grouping_service_api_tree_check_tree_logic_duration_bucket
    - apisec_grouping_service_api_tree_check_tree_logic_duration_count
    - apisec_grouping_service_api_tree_check_tree_logic_duration_sum
    - apisec_grouping_service_api_tree_number_of_trees_per_height
    - apisec_grouping_service_mongo_operation_latency_seconds_bucket
    - apisec_grouping_service_mongo_operation_latency_seconds_count
    - apisec_grouping_service_mongo_operation_latency_seconds_sum
    - apisec_grouping_service_number_of_filtered_paths_by_filter_type
    - apisec_grouping_service_number_of_paths_per_onboarding_status
    - apisec_grouping_service_pubsub_input_counter
    - apisec_grouping_service_pubsub_input_error_counter
    - apisec_grouping_service_pubsub_output_counter
    - apisec_grouping_service_pubsub_output_error_counter
    - apisec_grouping_service_regex_check_regex_duration_bucket
    - apisec_grouping_service_regex_check_regex_duration_count
    - apisec_grouping_service_regex_check_regex_duration_sum
    - apisec_grouping_service_regex_hits_total
    - apisec_grouping_service_single_transaction_flow_duration_bucket
    - apisec_grouping_service_single_transaction_flow_duration_count
    - apisec_grouping_service_single_transaction_flow_duration_sum
    - apisec_grouping_service_component_is_down_total
    - apisec_grouping_service_processor_errors_total
    - apisec_grouping_service_relation_find_agent_asset_id_error_count
    - apisec_inspection_api_aggregation_counter
    - apisec_inspection_api_aggregation_duration_bucket
    - apisec_inspection_api_aggregation_duration_count
    - apisec_inspection_api_aggregation_duration_sum
    - apisec_inspection_api_lru_init_counter
    - apisec_inspection_bit_aggregation_counter
    - apisec_inspection_content_error
    - apisec_inspection_content_pull
    - apisec_inspection_content_pull_duration_bucket
    - apisec_inspection_content_pull_duration_count
    - apisec_inspection_content_pull_duration_sum
    - apisec_inspection_content_update_check
    - apisec_inspection_content_update_check_duration_bucket
    - apisec_inspection_content_update_check_duration_count
    - apisec_inspection_content_update_check_duration_sum
    - apisec_inspection_content_update_check_error
    - apisec_inspection_issue_count
    - apisec_inspection_issue_creation_error_count
    - apisec_inspection_lru_entry_total_counter
    - apisec_inspection_lru_error
    - apisec_inspection_malformed_content
    - apisec_inspection_message_processing_duration_bucket
    - apisec_inspection_message_processing_duration_count
    - apisec_inspection_message_processing_duration_sum
    - apisec_inspection_pubsub_input_counter
    - apisec_inspection_pubsub_input_error_counter
    - apisec_inspection_pubsub_output_counter
    - apisec_inspection_pubsub_output_error_counter
    - apisec_inspection_rule_engine_aggregation_cache_eviction
    - apisec_inspection_rule_engine_cycle
    - apisec_inspection_rule_engine_execution
    - apisec_inspection_rule_error
    - apisec_inspection_rule_evaluation_duration_bucket
    - apisec_inspection_rule_execution_duration_bucket
    - apisec_inspection_telemetry_error_counter
    - apisec_inspection_telemetry_sent_counter
    - apisec_inspection_telemetry_throttled_counter
    - apisec_inspection_unknown_content
    - apisec_issue_patcher_cron_job_errors_total
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_bucket
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_count
    - apisec_issue_patcher_cron_job_get_agent_issues_query_duration_seconds_sum
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_bucket
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_count
    - apisec_issue_patcher_cron_job_groupid_resolution_duration_seconds_sum
    - apisec_issue_patcher_cron_job_issues_total
    - apisec_risk_engine_content_errors_total
    - apisec_risk_engine_drift_detection_duration_seconds_bucket
    - apisec_risk_engine_drift_detection_duration_seconds_count
    - apisec_risk_engine_drift_detection_duration_seconds_sum
    - apisec_risk_engine_dspm_classification_call_duration_in_seconds_bucket
    - apisec_risk_engine_dspm_load_errors_total
    - apisec_risk_engine_get_speclets_calls_total
    - apisec_risk_engine_get_speclets_duration_seconds_bucket
    - apisec_risk_engine_get_speclets_duration_seconds_count
    - apisec_risk_engine_get_speclets_duration_seconds_sum
    - apisec_risk_engine_get_speclets_invalid_total
    - apisec_risk_engine_get_spec_calls_total
    - apisec_risk_engine_get_spec_duration_seconds_bucket
    - apisec_risk_engine_get_spec_duration_seconds_count
    - apisec_risk_engine_get_spec_duration_seconds_sum
    - apisec_risk_engine_findings_upsert_duration_seconds_bucket
    - apisec_risk_engine_findings_upsert_duration_seconds_count
    - apisec_risk_engine_findings_upsert_duration_seconds_sum
    - apisec_risk_engine_findings_upsert_emits
    - apisec_risk_engine_findings_upserted
    - apisec_risk_engine_issues_upsert_duration_seconds_bucket
    - apisec_risk_engine_issues_upsert_duration_seconds_count
    - apisec_risk_engine_issues_upsert_duration_seconds_sum
    - apisec_risk_engine_issues_upsert_emits
    - apisec_risk_engine_issues_upserted
    - apisec_risk_engine_metablobs_handled
    - apisec_risk_engine_specs_handled_total
    - apisec_risk_engine_spec_cache_access_total
    - apisec_risk_engine_spec_risks_detection_duration_seconds_bucket
    - apisec_risk_engine_spec_risks_detection_duration_seconds_count
    - apisec_risk_engine_spec_risks_detection_duration_seconds_sum
    - apisec_risk_engine_spec_static_scan_failures_total
    - apisec_risk_engine_transaction_handling_duration_in_seconds_bucket
    - apisec_risk_engine_transaction_handling_duration_in_seconds_count
    - apisec_risk_engine_transaction_handling_duration_in_seconds_sum
    - apisec_risk_engine_transactions_handled
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum
    - apisec_risk_engine_findings_deleted
    - apisec_risk_engine_findings_deleted_emits
    - apisec_risk_engine_findings_ingestion_errors
    - apisec_risk_engine_transaction_risk_detection_errors_total
    - apisec_scan_manager_upload_scan_results_error_total
    - apisec_scan_manager_upload_scan_results_count_total
    - apisec_spec_service_cron_spec_gate_duration_seconds_sum
    - apisec_spec_service_cron_spec_gate_duration_seconds_count
    - apisec_spec_service_cron_spec_gate_duration_seconds_bucket
    - apisec_spec_service_errors_total
    - apisec_spec_service_file_size_bytes_sum
    - apisec_spec_service_file_size_bytes_count
    - apisec_spec_service_file_size_bytes_bucket
    - apisec_spec_service_mongo_duration_seconds_bucket
    - apisec_spec_service_mongo_duration_seconds_count
    - apisec_spec_service_mongo_duration_seconds_sum
    - apisec_spec_service_pubsub_duration_seconds_bucket
    - apisec_spec_service_pubsub_duration_seconds_count
    - apisec_spec_service_pubsub_duration_seconds_sum
    - apisec_spec_service_records_fetched_total
    - apisec_spec_service_records_handled_total
    - app_hub_prometheus_ingester_edr_errors_total
    - app_hub_prometheus_ingester_xql_errors_total
    - application_hub_permanent_errors_total
    - application_hub_public_api_requests_total
    - application_hub_temporary_errors_total
    - asm_alerts_mitre_backfill_sync_error_total
    - asm_etl_complete
    - asm_etl_count
    - asm_etl_duration
    - asm_export_assets_gauge
    - asm_incidents_mitre_backfill_sync_error_total
    - asm_mitre_mappings_sync_error_total
    - asset_mgmt_assoc_engine_acquire_lock_count_total
    - asset_mgmt_assoc_engine_association_conflicts_count
    - asset_mgmt_assoc_engine_association_process_time_count
    - asset_mgmt_assoc_engine_association_process_time_sum
    - asset_mgmt_diff_maker_last_exec_time_sec
    - asset_mgmt_general_assets_count_per_cloud_provider
    - asset_mgmt_general_assets_count_per_source
    - asset_mgmt_general_total_assets_count
    - asset_mgmt_ingester_assets_processed_count_total
    - asset_mgmt_reducer_last_exec_time_sec
    - asset_mgmt_snapshot_mgr_acquire_lock_total
    - archive_storage_aggregator_aggregator_compression_rate
    - archive_storage_aggregator_aggregator_process_duration
    - archive_storage_aggregator_committed_object_size
    - archive_storage_aggregator_compression_job_status
    - archive_storage_aggregator_delete_objects_count
    - archive_storage_aggregator_parse_error_count
    - archive_storage_aggregator_process_object_duration_micro_seconds_bucket
    - archive_storage_aggregator_processed_bytes_total
    - archive_storage_aggregator_processed_objects_count
    - archive_storage_aggregator_raw_object_size
    - argo_workflows_cas_scanners_orchestration_pre_scan_periodic_pre_scan_input_files_total
    - argo_workflows_cas_scanners_orchestration_pre_scan_pr_pre_scan_input_files_total
    - argo_workflows_cas_scanners_post_scan_duration_secs_bucket
    - argo_workflows_cas_scanners_pre_scan_duration_secs_bucket
    - argo_workflows_cas_scanners_pre_scan_duration_secs_count
    - argo_workflows_cas_scanners_pre_scan_duration_secs_sum
    - argo_workflows_cas_scanners_scanner_duration_secs_bucket
    - argo_workflows_cas_scanners_scanner_duration_secs_count
    - argo_workflows_cas_scanners_scanner_duration_secs_sum
    - argo_workflows_cas_scanners_scanner_iac_duration_secs_count
    - argo_workflows_job_duration_seconds_count
    - argo_workflows_workflow_duration_secs_count
    - attack_path_rules
    - attack_path_start_total
    - attack_path_success_total
    - attack_path_verdicts
    - attack_path_failure
    - auto_suggest_failure_total
    - batch_scanner_assets
    - batch_scanner_assets_scanned_milliseconds_bucket
    - batch_scanner_assets_scanned_milliseconds_count
    - batch_scanner_assets_scanned_milliseconds_sum
    - batch_scanner_rules_processed_total
    - batch_scanner_verdict_generated
    - batch_scanner_scanlog_generated
    - batch_scanner_scanlog_export_timer_milliseconds_sum
    - batch_scanner_scanlog_page_exported_total
    - batch_scanner_scanlog_exported_size_kb_sum
    - bigquery_adapter_.+
    - cas_actions_handler_issues_upsert_total
    - cas_actions_handler_pr_action_duration_seconds_bucket
    - cas_actions_handler_pr_action_duration_seconds_sum
    - cas_actions_handler_pr_action_duration_seconds_count
    - cas_actions_handler_pr_action_error_total
    - cas_actions_handler_update_pr_status_total
    - cas_actions_handler_generate_issues_duration_seconds_bucket
    - cas_actions_handler_generate_issues_duration_seconds_sum
    - cas_actions_handler_generate_issues_duration_seconds_total
    - cas_actions_handler_generate_issues_error_total
    - cas_actions_handler_watchdog_took_action_total
    - cas_applications_job_application_functions_seconds_bucket
    - cas_applications_job_application_functions_seconds_count
    - cas_applications_job_application_functions_seconds_sum
    - cas_dashboards_api_service_duration_seconds_bucket
    - cas_dashboards_api_service_duration_seconds_count
    - cas_dashboards_api_service_duration_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_count
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_cli_error_total
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_count
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_periodic_error_total
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_count
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_pr_error_total
    - cas_persistence_policies_engine_unsupported_operator_total
    - cas_product_analytics_send_to_cortex_seconds_bucket
    - cas_product_analytics_send_to_cortex_seconds_count
    - cas_product_analytics_send_to_cortex_seconds_sum
    - cas_persistence_app_genesis_errors_total
    - cas_persistence_app_lens_errors_total
    - cas_persistence_app_stream_errors_total
    - cas_persistence_app_code_errors_total
    - cas_persistence_scai_errors_total
    - cas_persistence_scan_ops_errors_total
    - cas_persistence_core_errors_total
    - cas_persistence_flow_errors_total
    - cas_persistence_issues_errors_total
    - cas_persistence_lilo_errors_total
    - cas_persistence_stitch_errors_total
    - cas_persistence_manager_persistence_manager_event_handling_seconds_bucket
    - cas_persistence_manager_persistence_manager_event_handling_seconds_count
    - cas_persistence_manager_persistence_manager_event_handling_seconds_sum
    - cas_scans_management_scan_duration_seconds_bucket
    - cas_scans_management_scan_duration_seconds_count
    - cas_scans_management_scan_duration_seconds_sum
    - manager_persistence_manager_event_handling_error_total
    - cas_http_request_duration_seconds_bucket
    - cas_http_request_duration_seconds_count
    - cas_http_request_duration_seconds_sum
    - cas_http_request_size_bytes_count
    - cas_http_request_size_bytes_sum
    - cas_http_response_size_bytes_count
    - cas_http_response_size_bytes_sum
    - cas_job_duration_seconds_bucket
    - cas_job_duration_seconds_count
    - cas_job_duration_seconds_sum
    - cas_job_artifacts_size_bytes_count
    - cas_job_artifacts_size_bytes_sum
    - cas_actions_handler_pr_scan_until_status_duration_seconds_bucket
    - cas_actions_handler_pr_scan_until_status_duration_seconds_count
    - cas_actions_handler_pr_scan_until_status_duration_seconds_sum
    - cas_unified_cli_command_count_total
    - cas_unified_cli_os_total
    - cas_unified_cli_scan_result_total
    - cas_policies_api_policy_created_total
    - cas_cas_scanner_tasks_manager_cas_scanner_task_manager_publish_messages_count_total
    - ciem_counter_access_analyzer_state_machine_runtime_exceptions_total
    - ciem_counter_collection_list_accounts_api_results_counter_total
    - ciem_counter_epc_validation_snapshot_failure_counter_total
    - ciem_counter_epc_calculator_aws_load_roles_mapping_total
    - ciem_counter_epc_calculator_aws_role_filtered_total
    - ciem_counter_epc_calculator_aws_permissions_created_by_calculator_total
    - ciem_counter_epc_calculator_aws_un_onboarded_permissions_total
    - ciem_counter_epc_calculator_aws_final_permissions_total
    - ciem_counter_epc_calculator_aws_normalized_role_calculator_exception_total
    - ciem_counter_epc_sync_page_upload_failure_counter_total
    - ciem_counter_epc_time_to_permission_kpi_total
    - ciem_counter_excessive_evidence_cache_hit_total
    - ciem_counter_excessive_evidence_cache_miss_total
    - ciem_counter_excessive_evidence_cache_unexpected_miss_total
    - ciem_counter_excessive_evidence_calculation_error_total
    - ciem_counter_excessive_evidence_inline_policy_service_error_total
    - ciem_counter_excessive_evidence_issue_failure_total
    - ciem_counter_excessive_evidence_issue_success_total
    - ciem_counter_excessive_evidence_persistent_storage_hits_total
    - ciem_counter_excessive_evidence_provider_error_total
    - ciem_counter_issues_publisher_total_published_total
    - ciem_counter_last_access_processor_added_records_counter_total
    - ciem_counter_last_access_audit_log_transformer_processed_records_counter
    - ciem_counter_last_access_audit_log_transformer_processed_records_counter_total
    - ciem_counter_last_access_audit_log_transformer_unrecognized_records_counter_total
    - ciem_counter_last_access_empty_pages_total
    - ciem_counter_last_access_processor_compacted_records_counter_total
    - ciem_counter_last_access_processor_merged_supported_actions_counter_total
    - ciem_counter_least_privileged_access_too_many_policies_total
    - ciem_timer_access_analyzer_processor_job_elapsed_time_seconds_count
    - ciem_timer_access_analyzer_processor_job_elapsed_time_seconds_max
    - ciem_timer_access_analyzer_processor_job_elapsed_time_seconds_sum
    - ciem_timer_least_privileged_access_controller_metadata_raw_seconds_count
    - ciem_timer_least_privileged_access_controller_metadata_raw_seconds_max
    - ciem_timer_least_privileged_access_controller_metadata_raw_seconds_sum
    - ciem_timer_least_privileged_access_controller_optimize_raw_seconds_count
    - ciem_timer_least_privileged_access_controller_optimize_raw_seconds_max
    - ciem_timer_least_privileged_access_controller_optimize_raw_seconds_sum
    - ciem_counter_sync_sync_publish_accounts_counter_total
    - ciem_counter_health_manager_account_messages_publish_by_type_total
    - ciem_counter_health_manager_account_status_publish_total
    - ciem_counter_health_manager_account_messages_publish_total
    - ciem_counter_health_manager_account_message_type_total
    - ciem_gauge_account_manager_list_accounts_api_result_size
    - ciem_gauge_collection_list_accounts_api_results_gauge
    - ciem_gauge_collection_accounts_by_cloud_type
    - ciem_gauge_creation_time_slot_time_sec
    - ciem_gauge_creation_time_total_bytes_processed
    - ciem_timer_api_controller_elapsed_time_seconds_bucket
    - ciem_timer_epc_calculator_aws_default_calculator_time_seconds_count
    - ciem_timer_epc_calculator_aws_default_calculator_time_seconds_max
    - ciem_timer_epc_calculator_aws_default_calculator_time_seconds_sum
    - ciem_timer_epc_calculator_aws_normalized_role_calculator_time_seconds_count
    - ciem_timer_epc_calculator_aws_normalized_role_calculator_time_seconds_max
    - ciem_timer_epc_calculator_aws_normalized_role_calculator_time_seconds_sum
    - ciem_timer_graph_controller_calculate_graph_seconds_bucket
    - ciem_timer_findings_calculator_job_elapsed_time_seconds_count
    - ciem_timer_findings_calculator_job_elapsed_time_seconds_max
    - ciem_timer_findings_calculator_job_elapsed_time_seconds_sum
    - ciem_gauge_epc_snapshot_status_account_monitor
    - ciem_gauge_epc_status_account_monitor
    - ciem_gauge_epc_sync_permissions_calculated_gauge
    - ciem_gauge_epc_validation_snapshot_to_big_query_time
    - ciem_gauge_epc_calculator_aws_roles_mapping_table_size
    - ciem_gauge_issues_publisher_total_published
    - ciem_gauge_last_access_processor_supported_actions_gauge
    - ciem_gauge_metrics_publisher_human_identity_with_admin_permission
    - ciem_gauge_metrics_publisher_non_human_identity_with_admin_permission
    - ciem_gauge_metrics_publisher_new_issues
    - ciem_gauge_metrics_publisher_resolved_issues
    - ciem_gauge_metrics_publisher_excessive_policies
    - ciem_gauge_metrics_publisher_assumed_by_third_party
    - ciem_gauge_metrics_publisher_assumed_by_third_party_with_admin_permission
    - ciem_gauge_metrics_publisher_total_issues
    - ciem_gauge_metrics_publisher_unused_permissions
    - ciem_gauge_metrics_publisher_identity_category_permissions
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_numIssues
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_avgDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_maxDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_minDataAge
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_avgSinceCalculated
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_maxSinceCalculated
    - ciem_gauge_metrics_publisher_issue_evidence_tracker_minSinceCalculated
    - ciem_gauge_metrics_publisher_total_unresolved_issues_by_detection_rule
    - ciem_gauge_rule_scanner_orchestrator_success_percentages
    - ciem_gauge_rule_scanner_orchestrator_total_rules
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_max
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_count
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_sum
    - ciem_timer_api_controller_elapsed_time_seconds_count
    - ciem_timer_api_controller_elapsed_time_seconds_max
    - ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_count
    - ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_sum
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_count
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_sum
    - ciem_timer_collection_list_accounts_api_time_seconds_count
    - ciem_timer_creation_time_maintenance_job_elapsed_time_seconds_max
    - ciem_timer_epc_consumer_total_time_seconds_max
    - ciem_timer_epc_consumer_total_time_seconds_count
    - ciem_timer_epc_consumer_total_time_seconds_sum
    - ciem_timer_epc_calculator_oci_total_time_seconds_max
    - ciem_timer_epc_calculator_oci_total_time_seconds_count
    - ciem_timer_epc_calculator_oci_total_time_seconds_sum
    - ciem_timer_epc_calculator_oci_policy_calculator_total_time_seconds_max
    - ciem_timer_epc_calculator_oci_policy_calculator_total_time_seconds_count
    - ciem_timer_epc_calculator_oci_policy_calculator_total_time_seconds_sum
    - ciem_timer_epc_calculator_oci_policy_parser_total_time_seconds_max
    - ciem_timer_epc_calculator_oci_policy_parser_total_time_seconds_count
    - ciem_timer_epc_calculator_oci_policy_parser_total_time_seconds_sum
    - ciem_timer_epc_calculator_oci_generate_hierarchy_total_time_seconds_max
    - ciem_timer_epc_calculator_oci_generate_hierarchy_total_time_seconds_count
    - ciem_timer_epc_calculator_oci_generate_hierarchy_total_time_seconds_sum
    - ciem_timer_epc_calculator_oci_discovery_data_expand_time_seconds_max
    - ciem_timer_epc_calculator_oci_discovery_data_expand_time_seconds_count
    - ciem_timer_epc_calculator_oci_discovery_data_expand_time_seconds_sum
    - ciem_timer_epc_calculator_oci_permission_pipeline_total_time_seconds_max
    - ciem_timer_epc_calculator_oci_permission_pipeline_total_time_seconds_count
    - ciem_timer_epc_calculator_oci_permission_pipeline_total_time_seconds_sum
    - ciem_counter_epc_calculator_oci_discovery_data_expand_unsupported_total
    - ciem_counter_epc_calculator_oci_pipeline_statement_failure_counter_total
    - ciem_counter_epc_calculator_oci_policy_statement_counter_total
    - ciem_timer_epc_sync_total_sync_time_seconds_count
    - ciem_timer_epc_sync_total_sync_time_seconds_sum
    - ciem_timer_evidence_sync_controller_calculate_seconds_bucket
    - ciem_timer_evidence_sync_controller_calculate_seconds_count
    - ciem_timer_evidence_sync_controller_calculate_seconds_sum
    - ciem_timer_evidence_sync_controller_calculate_seconds_max
    - ciem_timer_evidence_status_controller_get_status_seconds_bucket
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_bucket
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_max
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_count
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_max
    - ciem_timer_excessive_evidence_graph_batch_calculation_seconds_sum
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_count
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_max
    - ciem_timer_excessive_evidence_table_batch_calculation_seconds_sum
    - ciem_timer_graph_controller_calculate_graph_seconds_count
    - ciem_timer_graph_controller_calculate_graph_seconds_max
    - ciem_timer_graph_controller_calculate_graph_seconds_sum
    - ciem_timer_job_elapsed_time_seconds_count
    - ciem_timer_job_elapsed_time_seconds_max
    - ciem_timer_job_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_max
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_max
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_count
    - ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_sum
    - ciem_timer_last_access_processor_insert_elapsed_time_seconds_max
    - ciem_timer_last_access_processor_job_elapsed_time_seconds_count
    - ciem_timer_last_access_processor_job_elapsed_time_seconds_max
    - ciem_timer_least_privileged_access_controller_metadata_seconds_count
    - ciem_timer_least_privileged_access_controller_metadata_seconds_max
    - ciem_timer_least_privileged_access_controller_metadata_seconds_sum
    - ciem_timer_least_privileged_access_controller_optimize_seconds_count
    - ciem_timer_least_privileged_access_controller_optimize_seconds_bucket
    - ciem_timer_least_privileged_access_controller_optimize_seconds_max
    - ciem_timer_least_privileged_access_controller_optimize_seconds_sum
    - ciem_timer_rule_management_get_enabled_rules_seconds_count
    - ciem_timer_rule_management_get_enabled_rules_seconds_max
    - ciem_timer_rule_management_get_enabled_rules_seconds_sum
    - ciem_timer_rule_scanner_orchestrator_run_seconds_sum
    - ciem_timer_rule_scanner_orchestrator_run_seconds_count
    - ciem_timer_rule_scanner_orchestrator_run_seconds_max
    - ciem_timer_rule_scanner_run_seconds_count
    - ciem_timer_rule_scanner_run_seconds_max
    - ciem_timer_rule_scanner_run_seconds_sum
    - ciem_timer_sync_total_time_seconds_count
    - ciem_timer_sync_total_time_seconds_max
    - ciem_timer_sync_total_time_seconds_sum
    - ciem_timer_sync_sync_account_time_seconds_count
    - ciem_timer_sync_sync_account_time_seconds_sum
    - ciem_timer_sync_sync_publish_accounts_time_seconds_count
    - ciem_timer_sync_sync_publish_accounts_time_seconds_sum
    - ciem_timer_collection_list_accounts_api_time_seconds_sum
    - ciem_timer_table_controller_get_data_seconds_bucket
    - ciem_timer_table_controller_get_data_seconds_count
    - ciem_timer_table_controller_get_data_seconds_max
    - ciem_timer_table_controller_get_data_seconds_sum
    - ciem_timer_table_controller_get_view_def_seconds_bucket
    - ciem_timer_table_controller_get_view_def_seconds_count
    - ciem_timer_table_controller_get_view_def_seconds_max
    - ciem_timer_table_controller_get_view_def_seconds_sum
    - ciem_timer_epc_phase_total_time_seconds_max
    - ciem_timer_epc_phase_total_time_seconds_count
    - ciem_timer_epc_phase_total_time_seconds_sum
    - ciem_timer_epc_precalc_total_time_seconds_max
    - ciem_timer_epc_precalc_total_time_seconds_count
    - ciem_timer_epc_precalc_total_time_seconds_sum
    - ciem_timer_epc_calculator_aws_load_roles_mapping_time_seconds_count
    - ciem_timer_epc_calculator_aws_load_roles_mapping_time_seconds_sum
    - ciem_timer_epc_calculator_aws_roles_regex_processing_time_seconds_count
    - ciem_timer_epc_calculator_aws_roles_regex_processing_time_seconds_max
    - ciem_timer_epc_calculator_aws_roles_regex_processing_time_seconds_sum
    - ciem_timer_health_manager_execution_time_seconds_sum
    - ciem_timer_health_manager_execution_time_seconds_max
    - ciem_timer_health_manager_execution_time_seconds_count
    - ciem_timer_health_manager_send_health_by_connector_time_seconds_sum
    - ciem_timer_health_manager_send_health_by_connector_time_seconds_max
    - ciem_timer_health_manager_send_health_by_connector_time_seconds_count
    - ciem_timer_idp_precalc_total_time_seconds_count
    - ciem_timer_idp_precalc_total_time_seconds_max
    - ciem_timer_idp_precalc_total_time_seconds_sum
    - ciem_gauge_idp_extractor_aws_identity_center_total_identities_calculated
    - ciem_gauge_idp_extractor_azure_ad_total_identities_calculated
    - ciem_gauge_idp_extractor_okta_total_identities_calculated
    - ciem_timer_idp_extractor_aws_identity_center_elapsed_time_seconds_sum
    - ciem_timer_idp_extractor_aws_identity_center_elapsed_time_seconds_max
    - ciem_timer_idp_extractor_aws_identity_center_elapsed_time_seconds_count
    - ciem_timer_idp_extractor_azure_ad_elapsed_time_seconds_sum
    - ciem_timer_idp_extractor_azure_ad_elapsed_time_seconds_max
    - ciem_timer_idp_extractor_azure_ad_elapsed_time_seconds_count
    - ciem_timer_idp_extractor_okta_elapsed_time_seconds_sum
    - ciem_timer_idp_extractor_okta_elapsed_time_seconds_max
    - ciem_timer_idp_extractor_okta_elapsed_time_seconds_count
    - ciem_gauge_epc_validation_time_passed_since_last_snapshot
    - ciem_counter_health_check_execution_total
    - ciem_gauge_static_evidence_issue_batch_processor_success_percentage
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_count
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_max
    - ciem_timer_static_evidence_issue_batch_processor_total_time_seconds_sum
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_count
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_sum
    - ciem_timer_static_evidence_issue_sync_total_time_seconds_max
    - ciem_timer_static_evidence_processor_run_total_time_seconds_count
    - ciem_timer_static_evidence_processor_run_total_time_seconds_sum
    - ciem_timer_static_evidence_processor_run_total_time_seconds_max
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_count
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_sum
    - ciem_timer_static_evidence_single_issue_sync_total_time_seconds_max
    - ciem_counter_static_evidence_single_issue_sync_counter_total
    - ciem_counter_rql_to_xql_migration_failure_counter_total
    - ciem_counter_rql_to_xql_migration_success_counter_total
    - ciem_timer_rql_to_xql_migration_controller_migrate_seconds_count
    - ciem_timer_rql_to_xql_migration_controller_migrate_seconds_max
    - ciem_timer_rql_to_xql_migration_controller_migrate_seconds_sum
    - ciem_timer_rule_management_filter_rules_seconds_count
    - ciem_timer_rule_management_filter_rules_seconds_max
    - ciem_timer_rule_management_filter_rules_seconds_sum
    - ciem_counter_rule_scanner_duplicate_external_id_counter_total
    - ciem_timer_dynamic_group_precalc_extract_rules_total_time_seconds_count
    - ciem_timer_dynamic_group_precalc_extract_rules_total_time_seconds_sum
    - ciem_timer_dynamic_group_precalc_extract_rules_total_time_seconds_max
    - ciem_timer_oci_dynamic_group_precalc_asset_page_total_time_seconds_count
    - ciem_timer_oci_dynamic_group_precalc_asset_page_total_time_seconds_sum
    - ciem_timer_oci_dynamic_group_precalc_asset_page_total_time_seconds_max
    - ciem_timer_oci_dynamic_group_precalc_job_total_time_seconds_count
    - ciem_timer_oci_dynamic_group_precalc_job_total_time_seconds_sum
    - ciem_timer_oci_dynamic_group_precalc_job_total_time_seconds_max
    - ciem_timer_oci_dynamic_group_precalc_tenancy_total_time_seconds_count
    - ciem_timer_oci_dynamic_group_precalc_tenancy_total_time_seconds_sum
    - ciem_timer_oci_dynamic_group_precalc_tenancy_total_time_seconds_max
    - ciem_timer_oci_dynamic_group_epc_fetch_members_total_time_seconds_count
    - ciem_timer_oci_dynamic_group_epc_fetch_members_total_time_seconds_sum
    - ciem_timer_oci_dynamic_group_epc_fetch_members_total_time_seconds_max
    - ciem_gauge_oci_dynamic_group_precalc_asset_total_assets
    - ciem_gauge_oci_dynamic_group_precalc_asset_total_pages
    - cloud_connectors_templates_created_total
    - cloud_onboarding_errors_total
    - cloud_outposts_templates_created_total
    - cloud_assets_collection_csp_api_request_duration_seconds_bucket
    - cloud_assets_collection_csp_api_request_duration_seconds_count
    - cloud_assets_collection_csp_api_request_duration_seconds_sum
    - cloud_assets_collection_num_failed_tasks_total
    - cloud_assets_collection_num_successful_tasks_total
    - cloud_assets_collection_num_content_archive_files_download_failures_total
    - cloud_assets_collection_num_rit_files_yaml_parsing_failure_total
    - cloud_assets_collection_num_rits_content_version_failures_total
    - cloud_assets_collection_num_wrong_format_content_files_total
    - cloud_assets_collection_message_processing_duration_seconds_bucket
    - cloud_assets_collection_message_processing_duration_seconds_count
    - cloud_assets_collection_message_processing_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum
    - cloud_assets_collection_num_panic_jmes_path_functions_total
    - cloud_assets_collection_platform_api_request_duration_seconds_bucket
    - cloud_assets_collection_platform_api_request_duration_seconds_count
    - cloud_assets_collection_platform_api_request_duration_seconds_sum
    - cloud_assets_collection_rit_files_duplications
    - cc_cache_update_key_failure_total
    - cc_cache_update_time_seconds_bucket
    - cc_cache_update_time_seconds_count
    - cc_cache_update_time_seconds_sum
    - classification_mgmt_bigquery_write_error_total
    - classification_mgmt_pubsub_publish_error_total
    - classification_mgmt_content_delivery_error_total
    - classification_mgmt_dashboard_error_total
    - classification_mgmt_dashboard_business_total
    - compliance_cleanup_controls_records
    - compliance_cleanup_mysql_error_total
    - compliance_cleanup_mysql_time_taken
    - compliance_cleanup_results_catalog_mapping
    - compliance_cleanup_results_error_total
    - compliance_cleanup_results_records
    - compliance_cleanup_results_scores
    - compliance_cleanup_results_time_taken
    - compliance_cleanup_scan_logs
    - compliance_cleanup_scan_logs_error_total
    - compliance_cleanup_scan_logs_time_taken
    - compliance_cleanup_standards_records
    - control_processing_failures_total
    - cns_asset_conversion_errors_total
    - cwp_cns_asset_network_properties_evaluation_time_count
    - cwp_cns_asset_network_properties_evaluation_time_sum
    - cwp_cns_asset_network_security_rules_evaluation_time_count
    - cwp_cns_asset_network_security_rules_evaluation_time_sum
    - cwp_cns_cloud_graph_ingestion_time
    - cns_engine_errors_total
    - cwp_cns_findings_enrichment_duration_seconds
    - cwp_cns_findings_published
    - cwp_cns_graph_ingester_enabled
    - cwp_cns_graph_nodes_constructed_total
    - cns_invalid_policies
    - cns_invalid_rules
    - cwp_cns_issues_emitter_duration_seconds
    - cwp_cns_issues_emitter_errors_total
    - cwp_cns_issues_emitter_queue_size
    - cwp_cns_issues_published
    - cwp_scan_orchestrator_task_producer_failures_total
    - cns_issues_emitter_duration_seconds_count
    - cns_issues_emitter_errors_total
    - cns_issues_emitter_queue_size
    - cns_job_duration_seconds
    - cns_job_duration_seconds_sum
    - cns_job_init_failed
    - cns_num_assets
    - cns_num_policies
    - cns_num_rules
    - cns_policy_issues_generated
    - cns_rule_duration_seconds
    - cns_rule_findings_generated
    - cold_storage_aggregator_.+.
    - cold_storage_datasets_aggregator_aggregator_compression_rate
    - cold_storage_datasets_aggregator_aggregator_process_duration
    - cold_storage_datasets_aggregator_committed_object_size
    - cold_storage_datasets_aggregator_compression_job_status
    - cold_storage_datasets_aggregator_dataset_errors_count
    - cold_storage_datasets_aggregator_delete_objects_count
    - cold_storage_datasets_aggregator_processed_bytes_total
    - cold_storage_datasets_aggregator_processed_objects_count
    - cold_storage_datasets_aggregator_raw_object_size
    - cold_storage_datasets_aggregator_spawned_aggregators_count
    - cold_tables_sync_job_failure_total
    - contextual_search_graph_neo4j_query_execution_time_millis_bucket
    - contextual_search_graph_neo4j_query_execution_time_millis_count
    - contextual_search_graph_neo4j_query_execution_time_millis_created
    - contextual_search_graph_neo4j_query_execution_time_millis_sum
    - cortex_gw_messages_processor_.+
    - cortex_cdl_to_clcs_migration_failed_total
    - cortex_cdl_to_clcs_migration_succeeded_total
    - cortex_platform_http_request_duration_highr_seconds_count
    - cortex_platform_http_request_duration_highr_seconds_sum
    - cortex_platform_http_request_duration_seconds_count
    - cortex_platform_http_request_duration_seconds_sum
    - cortex_platform_http_request_size_bytes_count
    - cortex_platform_http_request_size_bytes_sum
    - cortex_platform_http_requests_total
    - cortex_platform_http_response_size_bytes_count
    - cortex_platform_http_response_size_bytes_sum
    - cronus_active_connections_total
    - cronus_client_client_roundtrip_latency_sec_bucket
    - cronus_client_connection_wait_duration_seconds_bucket
    - cronus_client_rate_limited_requests_total
    - cronus_client_requests_total
    - cronus_dao_inserts_total
    - cronus_db_repair_dropped_entries_total
    - cronus_handler_wait_time_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_count
    - cronus_hotkeys_count_delay_duration_seconds_sum
    - cronus_hotkeys_count_duration_seconds_bucket
    - cronus_hotkeys_count_duration_seconds_count
    - cronus_hotkeys_count_duration_seconds_sum
    - cronus_hotkeys_count_in_time_window
    - cronus_hotkeys_events_count_in_time_window
    - cronus_hotkeys_threshold_in_time_window
    - cronus_last_processed_index
    - cronus_log_cache_get_ops
    - cronus_operator_cluster_hotkeys_count_in_time_window
    - cronus_operator_cluster_hotkeys_events_count_in_time_window
    - cronus_rebalance_download_bytes_total
    - cronus_rebalance_read_bytes_total
    - cronus_rebalance_upload_bytes_total
    - cronus_request_duration_seconds_bucket
    - cronus_request_process_duration_seconds_bucket
    - cronus_requests_queue_stream_feed_wait_duration_seconds_bucket
    - cronus_requests_total
    - cronus_rows_index_latency_seconds_bucket
    - cronus_storage_latency_seconds_bucket
    - cronus_throttled_write_requests_total
    - cronus_tree_index_compaction_duration_seconds_bucket
    - cs_migration_result_ratio
    - cs_migration_errors_total
    - custom_rules_counter_total
    - cwp_account_and_outpost_processing_duration_seconds_count
    - cwp_ads_collectors_duration_seconds_sum
    - cwp_ads_collectors_duration_seconds_count
    - cwp_ads_collectors_duration_seconds_bucket
    - cwp_ads_dangling_snapshots_total
    - cwp_ads_instances
    - cwp_ads_missed_sla_instances
    - cwp_ads_scanned_assets_without_volumes
    - cwp_ads_sync_duration_seconds_count
    - cwp_ads_sync_failed_assets_total
    - cwp_ads_total_scan_results_freshness_seconds
    - cwp_ads_total_disk_size_bytes_bucket
    - cwp_ads_total_disk_size_bytes_count
    - cwp_ads_total_disk_size_bytes_sum
    - cwp_ads_used_disk_size_bytes_bucket
    - cwp_ads_used_disk_size_bytes_count
    - cwp_ads_used_disk_size_bytes_sum
    - cwp_ads_containers_per_partition_bucket
    - cwp_ads_containers_per_partition_count
    - cwp_ads_containers_per_partition_sum
    - cwp_ads_container_images_per_partition_bucket
    - cwp_ads_container_images_per_partition_count
    - cwp_ads_container_images_per_partition_sum
    - cwp_ads_files_per_partition_bucket
    - cwp_ads_files_per_partition_count
    - cwp_ads_files_per_partition_sum
    - cwp_ads_scan_duration_per_partition_seconds_bucket
    - cwp_ads_scan_duration_per_partition_seconds_count
    - cwp_ads_scan_duration_per_partition_seconds_sum
    - cwp_ads_container_images_per_asset_bucket
    - cwp_ads_container_images_per_asset_count
    - cwp_ads_container_images_per_asset_sum
    - cwp_ads_containers_per_host_bucket
    - cwp_ads_containers_per_host_count
    - cwp_ads_containers_per_host_sum
    - cwp_ads_files_per_asset_count
    - cwp_ads_files_per_asset_sum
    - cwp_ads_total_disk_size_bytes_bucket
    - cwp_ads_total_disk_size_bytes_count
    - cwp_ads_total_disk_size_bytes_sum
    - cwp_ads_used_disk_size_bytes_bucket
    - cwp_ads_used_disk_size_bytes_count
    - cwp_ads_used_disk_size_bytes_sum
    - cwp_ads_results_cache_latency_seconds_bucket
    - cwp_ads_results_cache_latency_seconds_count
    - cwp_ads_results_cache_latency_seconds_sum
    - cwp_api_concurrent_requests
    - cwp_api_latency_seconds
    - cwp_api_latency_seconds_bucket
    - cwp_api_requests_total
    - cwp_api_scan_result_size_bytes_bucket
    - cwp_api_scan_result_size_bytes_sum
    - cwp_api_scan_result_size_bytes_count
    - cwp_api_service_api_latency_seconds
    - cwp_asset_filter_scan_request_handling_duration_seconds
    - cwp_asset_filter_scan_requests_total
    - cwp_billing_poller_asset_count_duration_bucket
    - cwp_billing_poller_asset_count_duration_sum
    - cwp_billing_poller_num_assets
    - cwp_billing_reporter_billing_units
    - cwp_billing_reporter_publish_duration_bucket
    - cwp_billing_reporter_publish_duration_sum
    - cwp_billing_reporter_publish_response_status
    - cwp_bucket_communication_latency_seconds_bucket
    - cwp_bucket_communication_latency_seconds_sum
    - cwp_bucket_communication_latency_seconds_count
    - cwp_bucket_copy_latency_seconds_bucket
    - cwp_bucket_copy_latency_seconds_sum
    - cwp_bucket_copy_latency_seconds_count
    - cwp_buffered_instances_egress_total
    - cwp_buffered_instances_ingress_total
    - cwp_buffered_regions_egress_total
    - cwp_buffered_regions_ingress_total
    - cwp_check_group_threshold_duration_seconds_count
    - cwp_ci_analyzer_api_latency_seconds
    - cwp_ci_analyzer_api_requests_total
    - cwp_ci_analyzer_api_response_time_second
    - cwp_ci_analyzer_api_response_time_second_bucket
    - cwp_ci_analyzer_api_returned_object_size_bytes
    - cwp_ci_analyzer_api_returned_object_size_bytes_bucket
    - cwp_ci_analyzer_issues_processing_duration_seconds
    - cwp_ci_analyzer_issues_processing_duration_seconds_bucket
    - cwp_cloud_accounts_outgoing_http_request_latency_seconds
    - cwp_cloud_accounts_outgoing_http_requests_total
    - cwp_cns_tenant_run_success_total
    - cwp_cns_tenant_run_failed_total
    - cwp_cns_api_request_with_scopes
    - cwp_cns_asset_conversion_errors_total
    - cwp_cns_asset_network_properties_evaluation_time_created
    - cwp_cns_asset_network_security_controls_evaluation_time_count
    - cwp_cns_asset_network_security_controls_evaluation_time_created
    - cwp_cns_asset_network_security_controls_evaluation_time_sum
    - cwp_cns_asset_network_security_rules_evaluation_time_created
    - cwp_cns_engine_errors_total
    - cwp_cns_invalid_policies
    - cwp_cns_invalid_rules
    - cwp_cns_job_duration_seconds
    - cwp_cns_job_init_failed
    - cwp_cns_large_tenant_asset_batching_enabled
    - cwp_cns_logs_total
    - cwp_cns_new_batching_cycle_started
    - cwp_cns_num_assets
    - cwp_cns_num_filtered_findings
    - cwp_cns_num_policies
    - cwp_cns_num_rules
    - cwp_cns_num_trusted_ip_ranges
    - cwp_cns_policy_issues_generated
    - cwp_cns_realm_successfully_retrieved_count
    - cwp_cns_realms_ingested_total
    - cwp_cns_realms_queue_size
    - cwp_cns_realms_retrieving_job_duration_seconds
    - cwp_cns_realms_total
    - cwp_cns_remaining_assets_queue_size
    - cwp_cns_rule_duration_seconds_sum
    - cwp_cns_rule_evaluation_time_count
    - cwp_cns_rule_evaluation_time_created
    - cwp_cns_rule_evaluation_time_sum
    - cwp_cns_rule_findings_generated
    - cwp_cns_rule_query_time_count
    - cwp_cns_rule_query_time_created
    - cwp_cns_rule_query_time_sum
    - cwp_cns_service_started
    - cwp_cns_service_started_created
    - cwp_cns_service_started_total
    - cwp_cns_total_number_of_assets
    - cwp_cns_total_number_of_realms
    - cwp_cns_uai_assets_read_total
    - cwp_cns_uai_conversion_error_total
    - cwp_cns_xpanse_batch_processed
    - cwp_cns_xpanse_query_processing_time_max
    - cwp_cns_xpanse_retriever_batch_processed
    - cwp_cns_xpanse_total_dynamic_scan_requests
    - cwp_cns_xpanse_total_rows_skipped
    - cwp_cns_xpanse_total_scan_targets_matched
    - cwp_cns_xpanse_total_scan_targets_sent
    - cwp_compliance_agent_rules_calculator_active_compliance_policies_fetching_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_active_compliance_policies_fetching_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_agent_data_hashing_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_agent_data_hashing_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_asset_group_ids_fetching_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_asset_group_ids_fetching_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_asset_group_ids_per_agent_total
    - cwp_compliance_agent_rules_calculator_custom_rules_fetching_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_custom_rules_fetching_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_custom_rules_found_total
    - cwp_compliance_agent_rules_calculator_cwp_app_errors_total
    - cwp_compliance_agent_rules_calculator_cwp_policy_zip_size_bytes_count
    - cwp_compliance_agent_rules_calculator_cwp_policy_zip_size_bytes_sum
    - cwp_compliance_agent_rules_calculator_policies_found_total
    - cwp_compliance_agent_rules_calculator_policies_per_agent_total
    - cwp_compliance_agent_rules_calculator_policy_state_enrichment_from_db_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_policy_state_enrichment_from_db_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_rule_file_size_bytes_count
    - cwp_compliance_agent_rules_calculator_rule_file_size_bytes_sum
    - cwp_compliance_agent_rules_calculator_rules_calculation_total_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_rules_calculation_total_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_sign_policy_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_sign_policy_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_upload_policy_zip_to_gcs_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_upload_policy_zip_to_gcs_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_write_agent_data_to_db_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_write_agent_data_to_db_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_write_file_to_disk_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_write_file_to_disk_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_write_files_to_disk_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_write_policy_information_to_db_latency_seconds_bucket
    - cwp_compliance_agent_rules_calculator_write_policy_information_to_db_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_write_policy_information_to_db_latency_seconds_sum
    - cwp_compliance_agent_rules_calculator_zip_policy_latency_seconds_count
    - cwp_compliance_agent_rules_calculator_zip_policy_latency_seconds_sum
    - cwp_compliance_publisher_api_latency_seconds_bucket
    - cwp_compliance_publisher_api_latency_seconds_sum
    - cwp_compliance_publisher_component_latency_seconds_bucket
    - cwp_compliance_publisher_component_latency_seconds_sum
    - cwp_compliance_publisher_e2e_latency_seconds_bucket
    - cwp_compliance_publisher_e2e_latency_seconds_count
    - cwp_compliance_publisher_e2e_latency_seconds_sum
    - cwp_compliance_publisher_errors_total
    - cwp_compliance_publisher_findings_published_total
    - cwp_compliance_publisher_issues_published_total
    - cwp_compliance_publisher_messages_income_total
    - cwp_compliance_publisher_messages_published_total
    - cwp_compliance_publisher_process_cpu_seconds_total
    - cwp_compliance_publisher_process_max_fds
    - cwp_compliance_publisher_process_network_receive_bytes_total
    - cwp_compliance_publisher_process_network_transmit_bytes_total
    - cwp_compliance_publisher_process_open_fds
    - cwp_compliance_publisher_process_resident_memory_bytes
    - cwp_compliance_publisher_process_start_time_seconds
    - cwp_compliance_publisher_process_virtual_memory_bytes
    - cwp_compliance_publisher_process_virtual_memory_max_bytes
    - cwp_compliance_publisher_send_scan_log_seconds_bucket
    - cwp_compliance_publisher_send_scan_log_seconds_count
    - cwp_compliance_publisher_send_scan_log_seconds_sum
    - cwp_compliance_rules_size_bytes_count
    - cwp_compliance_uai_scanner_app_errors_total
    - cwp_compliance_uai_scanner_assets_total
    - cwp_compliance_uai_scanner_e2e_latency_bucket
    - cwp_compliance_uai_scanner_output_publish_latency_bucket
    - cwp_compliance_uai_scanner_output_size_bucket
    - cwp_compliance_uai_scanner_rules_scan_res_total
    - cwp_compliance_uai_scanner_rules_total
    - cwp_compliance_uai_scanner_scan_latency_bucket
    - cwp_compliance_uai_scanner_scan_per_asset_latency_bucket
    - cwp_compliance_uai_scanner_scan_per_rule_latency_bucket
    - cwp_compliance_uai_scanner_success_rate_per_asset_total
    - cwp_containers_analyzer_container_deletion_latency_seconds_count
    - cwp_containers_analyzer_container_deletion_latency_seconds_bucket
    - cwp_containers_analyzer_container_deletion_latency_seconds_sum
    - cwp_containers_analyzer_container_deletions_error_total
    - cwp_containers_analyzer_container_deletions_success_total
    - cwp_containers_analyzer_delete_containers_gap_count_total
    - cwp_containers_analyzer_global_limit_reached_count_total
    - cwp_containers_analyzer_non_drift_containers_count_total
    - cwp_containers_analyzer_publish_error_count_total
    - cwp_containers_analyzer_scan_result_size_bytes_bucket
    - cwp_containers_analyzer_scan_result_size_bytes_count
    - cwp_containers_analyzer_scan_result_size_bytes_sum
    - cwp_containers_analyzer_total_number_of_containers_in_uai
    - cwp_containers_analyzer_update_limit_by_image_for_non_running_containers_error_total
    - cwp_core_asset_analyzer_api_latency_seconds_bucket
    - cwp_core_asset_analyzer_api_latency_seconds_sum
    - cwp_core_asset_analyzer_message_bus_requests_latency_bucket
    - cwp_core_asset_analyzer_uai_publisher_publish_request_latency
    - cwp_core_asset_analyzer_uai_publisher_publish_requests_latency_count
    - cwp_core_asset_analyzer_uai_publisher_publish_total_requests
    - cwp_create_scanning_groups_duration_seconds_bucket
    - cwp_create_scanning_groups_duration_seconds_sum
    - cwp_created_scanning_groups_total
    - cwp_cycle_duration_seconds
    - cwp_cycles_total
    - cwp_deadline_requests_total
    - cwp_disk_size_count
    - cwp_error_logs_total
    - cwp_errors_total
    - cwp_fetched_messages_total
    - cwp_fetched_queues_total
    - cwp_fpp_du
    - cwp_fpp_missing_normalized_fields_error_count_total
    - cwp_fpp_performance_duration_seconds_sum
    - cwp_fpp_policy_evaluator_empty_data_count_total
    - cwp_fpp_publish_issues_error_count_total
    - cwp_group_instances_duration_seconds_bucket
    - cwp_group_instances_duration_seconds_sum
    - cwp_http_client_duration_milliseconds_bucket
    - cwp_http_client_duration_milliseconds_count
    - cwp_http_client_request_body_size_bytes_bucket
    - cwp_http_client_request_body_size_bytes_sum
    - cwp_http_client_request_duration_seconds_count
    - cwp_http_client_request_size_bytes_total
    - cwp_http_client_response_size_bytes_total
    - cwp_http_server_duration_milliseconds_bucket
    - cwp_http_server_duration_milliseconds_count
    - cwp_http_server_duration_milliseconds_sum
    - cwp_http_server_request_body_size_bytes_bucket
    - cwp_http_server_request_body_size_bytes_sum
    - cwp_http_server_request_duration_seconds_count
    - cwp_http_server_request_size_bytes_total
    - cwp_http_server_response_body_size_bytes_count
    - cwp_http_server_response_size_bytes_total
    - cwp_instance_backpressure_free_capacity_ratio
    - cwp_instance_handle_duration
    - cwp_instance_handle_duration_seconds_bucket
    - cwp_instance_handle_duration_seconds_count
    - cwp_instance_handle_duration_seconds_sum
    - cwp_instance_scan_duration_minutes_bucket
    - cwp_instance_scan_duration_minutes_sum
    - cwp_instance_scan_duration_minutes_count
    - cwp_instance_types_total
    - cwp_instance_type_errors_total
    - cwp_instances_buffering_duration
    - cwp_instances_buffering_duration_seconds_bucket
    - cwp_instances_buffering_duration_seconds_count
    - cwp_instances_buffering_duration_seconds_sum
    - cwp_instances_total
    - cwp_k8s_connector_api_admission_latency_seconds_bucket
    - cwp_k8s_connector_api_admission_latency_seconds_sum
    - cwp_k8s_connector_api_api_concurrent_requests
    - cwp_k8s_connector_api_api_latency_seconds_bucket
    - cwp_k8s_connector_api_api_latency_seconds_sum
    - cwp_k8s_connector_api_api_requests_total
    - cwp_k8s_connector_api_app_errors_total
    - cwp_k8s_connector_api_auths_total
    - cwp_k8s_connector_api_component_latency_seconds_bucket
    - cwp_k8s_connector_api_component_latency_seconds_count
    - cwp_k8s_connector_api_db_query_errors_total
    - cwp_k8s_connector_api_download_size_bytes_bucket
    - cwp_k8s_connector_api_download_size_bytes_sum
    - cwp_k8s_connector_api_downloads_total
    - cwp_k8s_connector_api_heartbeats_total
    - cwp_k8s_connector_api_process_cpu_seconds_total
    - cwp_k8s_connector_api_process_max_fds
    - cwp_k8s_connector_api_process_network_receive_bytes_total
    - cwp_k8s_connector_api_process_network_transmit_bytes_total
    - cwp_k8s_connector_api_process_open_fds
    - cwp_k8s_connector_api_process_resident_memory_bytes
    - cwp_k8s_connector_api_process_start_time_seconds
    - cwp_k8s_connector_api_process_virtual_memory_bytes
    - cwp_k8s_connector_api_process_virtual_memory_max_bytes
    - cwp_k8s_connector_api_pubsub_publish_total
    - cwp_k8s_connector_api_registrations_total
    - cwp_k8s_connector_api_update_status_total
    - cwp_k8s_connector_api_upload_size_bytes_bucket
    - cwp_k8s_connector_api_upload_size_bytes_sum
    - cwp_k8s_connector_api_uploads_total
    - cwp_k8s_inventory_publisher_api_concurrent_requests
    - cwp_k8s_inventory_publisher_api_latency_seconds_bucket
    - cwp_k8s_inventory_publisher_api_latency_seconds_count
    - cwp_k8s_inventory_publisher_api_latency_seconds_sum
    - cwp_k8s_inventory_publisher_api_requests_total
    - cwp_k8s_inventory_publisher_component_time_seconds_bucket
    - cwp_k8s_inventory_publisher_component_time_seconds_count
    - cwp_k8s_inventory_publisher_component_time_seconds_sum
    - cwp_k8s_inventory_publisher_end_to_end_seconds_bucket
    - cwp_k8s_inventory_publisher_end_to_end_seconds_count
    - cwp_k8s_inventory_publisher_end_to_end_seconds_sum
    - cwp_k8s_inventory_publisher_errors_total
    - cwp_k8s_inventory_publisher_gcs_folder_size_bucket
    - cwp_k8s_inventory_publisher_gcs_folder_size_count
    - cwp_k8s_inventory_publisher_gcs_folder_size_sum
    - cwp_k8s_inventory_publisher_gcs_latency_seconds_bucket
    - cwp_k8s_inventory_publisher_gcs_latency_seconds_count
    - cwp_k8s_inventory_publisher_gcs_latency_seconds_sum
    - cwp_k8s_inventory_publisher_messages_total
    - cwp_k8s_inventory_publisher_process_cpu_seconds_total
    - cwp_k8s_inventory_publisher_process_max_fds
    - cwp_k8s_inventory_publisher_process_open_fds
    - cwp_k8s_inventory_publisher_process_resident_memory_bytes
    - cwp_k8s_inventory_publisher_process_start_time_seconds
    - cwp_k8s_inventory_publisher_process_virtual_memory_bytes
    - cwp_k8s_inventory_publisher_process_virtual_memory_max_bytes
    - cwp_k8s_inventory_publisher_pubsub_latency_seconds_bucket
    - cwp_k8s_inventory_publisher_pubsub_latency_seconds_count
    - cwp_k8s_inventory_publisher_pubsub_latency_seconds_sum
    - cwp_k8s_inventory_publisher_relations_total
    - cwp_k8s_rule_calculator_api_concurrent_requests
    - cwp_k8s_rule_calculator_api_latency_seconds_bucket
    - cwp_k8s_rule_calculator_api_latency_seconds_sum
    - cwp_k8s_rule_calculator_api_requests_total
    - cwp_k8s_rule_calculator_cwp_app_errors_total
    - cwp_k8s_rule_calculator_cwp_cnc_add_request_latency_seconds_bucket
    - cwp_k8s_rule_calculator_cwp_cnc_add_request_latency_seconds_count
    - cwp_k8s_rule_calculator_cwp_component_latency_seconds_bucket
    - cwp_k8s_rule_calculator_cwp_component_latency_seconds_sum
    - cwp_k8s_rule_calculator_cwp_pubsub_message_received_total
    - cwp_k8s_rule_calculator_cwp_pubsub_ping_message_received_total
    - cwp_k8s_rule_calculator_log_total
    - cwp_life_cycle_events_total
    - cwp_load_instances_duration_seconds_count
    - cwp_log_files_total
    - cwp_log_lines_total
    - cwp_log_processing_latency
    - cwp_log_processing_latency_seconds_bucket
    - cwp_log_processing_latency_seconds_sum
    - cwp_malware_analyzer_action_center_get_data_http_error_codes
    - cwp_malware_analyzer_api_latency_seconds_bucket
    - cwp_malware_analyzer_api_latency_seconds_count
    - cwp_malware_analyzer_api_latency_seconds_sum
    - cwp_malware_analyzer_asset_group_requests_latency
    - cwp_malware_analyzer_asset_group_total_requests
    - cwp_malware_analyzer_assets_published_total
    - cwp_malware_analyzer_assets_publish_errors_total
    - cwp_malware_analyzer_detection_latency_seconds_count
    - cwp_malware_analyzer_detection_latency_seconds_sum
    - cwp_malware_analyzer_detection_requests_total
    - cwp_malware_analyzer_detection_status_codes
    - cwp_malware_analyzer_detection_verdict_total
    - cwp_malware_analyzer_findings_processing_error_total
    - cwp_malware_analyzer_findings_published_total
    - cwp_malware_analyzer_issues_processing_error_total
    - cwp_malware_analyzer_issues_published_total
    - cwp_malware_analyzer_message_latency_seconds_count
    - cwp_malware_analyzer_message_latency_seconds_sum
    - cwp_malware_analyzer_message_total
    - cwp_malwaredetection_cache_hit_ratio_count
    - cwp_malwaredetection_cache_hit_ratio_sum
    - cwp_malwaredetection_cache_hit_total
    - cwp_malwaredetection_cache_verdict_update_error_total
    - cwp_malwaredetection_cache_verdict_update_total
    - cwp_malwaredetection_wildfire_hit_ratio_count
    - cwp_malwaredetection_wildfire_hit_ratio_sum
    - cwp_malwaredetection_wildfire_latency_seconds_bucket
    - cwp_malwaredetection_wildfire_latency_seconds_count
    - cwp_malwaredetection_wildfire_latency_seconds_sum
    - cwp_malwaredetection_wildfire_received_verdicts
    - cwp_malwaredetection_wildfire_status_codes
    - cwp_message_bus_latency_seconds
    - cwp_message_bus_processor_handling_latency_seconds_count
    - cwp_message_bus_processor_post_loading_latency_seconds
    - cwp_message_bus_processor_preprocess_latency_seconds_count
    - cwp_message_bus_processor_request_latency_seconds_bucket
    - cwp_message_bus_processor_request_latency_seconds_sum
    - cwp_message_bus_processor_requests_filtered_total
    - cwp_message_bus_processor_requests_total
    - cwp_message_bus_request_latency_seconds_bucket
    - cwp_message_bus_request_latency_seconds_sum
    - cwp_message_bus_requests_total
    - cwp_object_size_bucket
    - cwp_object_size_sum
    - cwp_operation_latency_seconds_bucket
    - cwp_operation_latency_seconds_count
    - cwp_operation_latency_seconds_sum
    - cwp_operation_total
    - cwp_oldest_instance_age_seconds
    - cwp_operation_duration
    - cwp_outpost_and_region_groups_total
    - cwp_pending_scanning_groups_total
    - cwp_prioritization_best_effort_free_capacity_gauge_ratio
    - cwp_prioritization_best_effort_on_demand_runtime_seconds_gauge_seconds
    - cwp_prioritization_best_effort_on_demand_start_time_seconds_hist_seconds
    - cwp_prioritization_best_effort_prioritized_instances_counter_total
    - cwp_prioritization_best_effort_prioritized_scanned_assets_counter_total
    - cwp_prioritization_best_effort_runtime_seconds_gauge_seconds
    - cwp_prioritization_prioritization_best_effort_low_quota_counter
    - cwp_region_handling_duration
    - cwp_region_pending_handling_duration
    - cwp_registry_orchestrator_asset_scan_status_count_query_errors_total
    - cwp_registry_orchestrator_connector_status_update_errors_total
    - cwp_registry_scan_orchestrator_image_size_bytes_count
    - cwp_requests_state_latency_seconds_bucket
    - cwp_requests_state_latency_seconds_count
    - cwp_requests_state_latency_seconds_sum
    - cwp_requests_total
    - cwp_rpc_client_duration_milliseconds_bucket
    - cwp_rpc_client_duration_milliseconds_count
    - cwp_rpc_client_request_size_bytes_bucket
    - cwp_rpc_client_request_size_bytes_sum
    - cwp_rpc_client_requests_per_rpc_bucket
    - cwp_rpc_client_requests_per_rpc_count
    - cwp_rpc_client_response_size_bytes_bucket
    - cwp_rpc_client_response_size_bytes_sum
    - cwp_rpc_client_responses_per_rpc_bucket
    - cwp_rpc_client_responses_per_rpc_count
    - cwp_rules_cron_job_execution_count_total
    - cwp_rules_cron_job_execution_result_total
    - cwp_rules_cron_job_latency_seconds_count
    - cwp_rules_inserted_to_db_total
    - cwp_rules_management_api_deleted_policies_sent_for_issue_closing
    - cwp_rules_management_api_latency_seconds_bucket
    - cwp_rules_management_asset_groups_query_seconds_bucket
    - cwp_rules_management_asset_groups_query_seconds_count
    - cwp_rules_management_asset_groups_query_seconds_sum
    - cwp_rules_management_issue_closer_policy_deletion_issues_upsert_count
    - cwp_rules_management_issue_closer_policy_deletion_policies_processed
    - cwp_rules_management_issue_closer_policy_deletion_resolved_issues
    - cwp_rules_management_requests_total
    - cwp_run_duration_seconds_count
    - cwp_sbom_analyzer_db_upload_messages_rate
    - cwp_sbom_analyzer_db_upload_packages_rate
    - cwp_sbom_analyzer_dumped_asset_type_count
    - cwp_sbom_analyzer_error_type_count
    - cwp_sbom_analyzer_inbound_rate_per_flow_type
    - cwp_sbom_analyzer_latency_per_flow_type_seconds
    - cwp_sbom_analyzer_payload_request_size_bytes
    - cwp_sbom_analyzer_payload_response_size_bytes
    - cwp_sbom_analyzer_requests_latency_count
    - cwp_sbom_export_db_queries_success_failure
    - cwp_sbom_export_latency_seconds
    - cwp_sbom_export_parsing_errors_total
    - cwp_sbom_export_query_result_size_bytes
    - cwp_scan_duration_seconds_count
    - cwp_scan_duration_seconds_bucket
    - cwp_scan_orchestrator_batcher_batches_fetched
    - cwp_scan_orchestrator_batcher_batches_fetched_duration_seconds_bucket
    - cwp_scan_orchestrator_batcher_batches_fetched_duration_seconds_count
    - cwp_scan_orchestrator_batcher_batches_fetched_duration_seconds_sum
    - cwp_scan_orchestrator_batcher_trigger_total
    - cwp_scan_orchestrator_broker_vm_get_status_action_results_total
    - cwp_scan_orchestrator_broker_vm_get_status_call_execution_duration_seconds_bucket
    - cwp_scan_orchestrator_broker_vm_get_status_call_execution_errors_total
    - cwp_scan_orchestrator_broker_vm_get_status_call_executions_total
    - cwp_scan_orchestrator_broker_vm_get_status_start_run_total
    - cwp_scan_orchestrator_broker_vm_get_status_tasks_totals
    - cwp_scan_orchestrator_component_latency
    - cwp_scan_orchestrator_dispatch_batch_errors_total
    - cwp_scan_orchestrator_dispatch_batch_total
    - cwp_scan_orchestrator_lifecycle_event_task_statuses_total
    - cwp_scan_orchestrator_lifecycle_events_handled_total
    - cwp_scan_orchestrator_lifecycle_events_handling_duration_seconds
    - cwp_scan_platform_outgoing_http_requests_response_time_seconds
    - cwp_scan_platform_outgoing_http_requests_total
    - cwp_scan_request_generator_scanner_version_change_event_handling_duration_seconds
    - cwp_scan_results_enricher_api_latency_seconds_bucket
    - cwp_scan_results_enricher_api_requests_total
    - cwp_scan_results_enricher_asset_enrichment_requests_latency_bucket
    - cwp_scan_results_enricher_asset_enrichment_requests_latency_sum
    - cwp_scan_results_enricher_asset_enrichment_total_requests
    - cwp_scan_results_enricher_message_bus_requests_latency_bucket
    - cwp_scan_results_enricher_handler_skipped_assets
    - cwp_scan_spec_manager_cache_read_errors_total
    - cwp_scan_spec_manager_cache_write_errors_total
    - cwp_scan_task_batcher_batch_creation_time_seconds
    - cwp_scan_task_batcher_batches_created_total
    - cwp_scan_task_ingestor_batch_handling_duration_seconds
    - cwp_scan_task_ingestor_batches_handled_total
    - cwp_scan_task_ingestor_scan_tasks_handled_total
    - cwp_scan_task_producer_errors_total
    - cwp_scan_task_producer_handling_duration_seconds
    - cwp_scan_task_producer_scan_request_batch_errors_total
    - cwp_scan_task_producer_scan_request_batches_total
    - cwp_scan_task_producer_scan_requests_errors_total
    - cwp_scan_task_producer_scan_requests_total
    - cwp_scanning_group_snapshots_total
    - cwp_secret_analyzer_api_latency_seconds_bucket
    - cwp_secret_analyzer_api_latency_seconds_count
    - cwp_secret_analyzer_api_latency_seconds_sum
    - cwp_secret_analyzer_api_requests_total
    - cwp_secret_analyzer_assets_published_total
    - cwp_secret_analyzer_assets_publish_errors_total
    - cwp_secret_analyzer_findings_processing_error_total
    - cwp_secret_analyzer_findings_published_total
    - cwp_secret_analyzer_issues_processing_error_total
    - cwp_secret_analyzer_issues_published_total
    - cwp_secret_analyzer_message_latency_seconds_count
    - cwp_secret_analyzer_message_latency_seconds_sum
    - cwp_secret_analyzer_message_total
    - cwp_serverless_orchestrator_asset_scan_status_count_query_errors_total
    - cwp_serverless_orchestrator_asset_scan_status_bar_count_query_errors_total
    - cwp_serverless_orchestrator_connector_status_bar_update_errors_total
    - cwp_serverless_orchestrator_connector_status_update_errors_total
    - cwp_snapshot_free_capacity_ratio
    - cwp_snapshot_lifetime_seconds_bucket
    - cwp_snapshot_lifetime_seconds_count
    - cwp_snapshot_lifetime_seconds_sum
    - cwp_snapshot_size_bytes
    - cwp_snapshot_size_bytes_bucket
    - cwp_sp_snapshot_csp_requests_total
    - cwp_sp_snapshot_lifetime_seconds_bucket
    - cwp_sp_snapshot_lifetime_seconds_count
    - cwp_sp_snapshot_lifetime_seconds_sum
    - cwp_sp_snapshot_operation_duration_seconds_count
    - cwp_sp_snapshot_operation_duration_seconds_sum
    - cwp_sp_snapshot_request_avg_waiting_time_seconds
    - cwp_sp_snapshot_request_avg_by_type_waiting_time_seconds
    - cwp_sp_snapshot_requests_missed_sla
    - cwp_spec_fetcher_scan_request_batches_total
    - cwp_spec_fetcher_scan_requests_total
    - cwp_spec_fetcher_spec_fetching_duration_seconds
    - cwp_spec_fetcher_specs_fetched_total
    - cwp_storage_events_handler_latency_seconds_bucket
    - cwp_storage_events_handler_latency_seconds_sum
    - cwp_trusted_images_analyzer_issues_published_total
    - cwp_unmanaged_registry_scan_connector_status_bar_validation_errors_total
    - cwp_unmanaged_registry_scan_connector_status_validation_errors_total
    - cwp_vulnerability_analyzer_api_latency_seconds_bucket
    - cwp_vulnerability_analyzer_issues_published_total
    - cwp_vulnerability_analyzer_message_latency_seconds_bucket
    - cwp_vulnerability_analyzer_message_latency_seconds_count
    - cwp_vulnerability_analyzer_message_latency_seconds_sum
    - cwp_vulnerability_analyzer_message_total
    - cwp_vulnerability_analyzer_processed_findings_total
    - cwp_vulnerability_analyzer_processed_packages_total
    - cwp_vulnerability_analyzer_processing_error_total
    - cwp_vulnerability_evaluator_api_latency_seconds_bucket
    - cwp_vulnerability_evaluator_api_requests_total
    - cwp_vulnerability_evaluator_app_watcher_reload_event_triggered_count
    - cwp_vulnerability_evaluator_app_watcher_reload_events_total
    - cwp_vulnerability_evaluator_reader_gcs_bucket_fetch_duration_seconds_bucket
    - cwp_vulnerability_evaluator_reader_gcs_fetch_count
    - cwp_vulnerability_evaluator_reader_gcs_new_generation_identified
    - cwp_vulnerability_evaluator_reader_gcs_watcher_count
    - cwp_vulnerability_evaluator_reader_gcs_watcher_queue_full
    - cwp_vulnerability_evaluator_vulnerability_feed_load_duration_seconds_bucket
    - cwp_vulnerability_evaluator_vulnerability_feed_load_generation_number
    - cwp_waiting_instances_total
    - cwp_workload_supervisor_task_fetching_time_seconds
    - dashboard_api_4xx_failure_total
    - dashboard_api_5xx_failure_total
    - dashboard_engine_.+
    - data_ingestion_health_ingestion_alerts_total
    - dbre_backup_succeeded
    - neo4j_dbms_page_cache_hit_ratio
    - neo4j_dbms_vm_heap_used
    - neo4j_dbms_vm_gc_time_g1_old_generation_total
    - neo4j_dbms_vm_gc_time_g1_young_generation_total
    - dml_.+
    - dms_.+
    - dms_controller_element_duration_seconds
    - dp_asset_associations_pipeline_association_big_query_errors
    - dp_asset_associations_pipeline_association_public_ip_filter
    - dp_asset_associations_pipeline_association_stats
    - dp_asset_associations_pipeline_assets_errors
    - dp_asset_associations_pipeline_assets_total
    - dp_asset_associations_wlm_low_fidelity_asset_cleanup_total
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_associations_pipeline_assets_batch_size_bucket
    - dp_asset_associations_pipeline_assets_batch_size_count
    - dp_asset_associations_pipeline_assets_batch_size_sum
    - dp_asset_associations_pipeline_association_big_query_result_size_bucket
    - dp_asset_associations_pipeline_association_big_query_result_size_count
    - dp_asset_associations_pipeline_association_big_query_result_size_sum
    - dp_asset_associations_pipeline_metablob_errors
    - dp_asset_associations_pipeline_ratelimit_errors
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_pipeline_metablob_errors
    - dp_asset_pipeline_performance_bucket
    - dp_asset_pipeline_performance_count
    - dp_asset_pipeline_performance_sum
    - dp_asset_pipeline_rows_in_metablobs_bucket
    - dp_asset_pipeline_rows_in_metablobs_count
    - dp_asset_pipeline_rows_in_metablobs_sum
    - dp_asset_pipeline_skip_get_deleted_assets
    - dp_asset_pipeline_operation_total
    - dspm_dt_adc_data_writer_success_total
    - dspm_dt_adc_data_writer_total_written_total
    - dspm_dt_bigquery_job_affected_rows_total
    - dspm_dt_bigquery_job_duration_seconds_count
    - dspm_dt_bigquery_job_duration_seconds_max
    - dspm_dt_bigquery_job_duration_seconds_sum
    - dspm_dt_bigquery_job_processed_bytes_total
    - dspm_dt_bigquery_job_processed_partitions_total
    - dspm_dt_bigquery_job_slot_millis_total
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_post_processing_listener_message_successes_total
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_file_analysis_listener_message_successes_total
    - dspm_dt_fda_files_processed_total
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_column_analysis_listener_message_successes_total
    - dspm_dt_fda_columns_processed_total
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_analysis_listener_message_successes_total
    - dspm_dt_fda_assets_processed_total
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_inventory_listener_message_failures_total
    - dspm_dt_fda_asset_inventory_listener_message_successes_total
    - dspm_dt_fda_data_writer_success_total
    - dspm_dt_fda_data_writer_total_written_total
    - dspm_dt_fda_replication_oldest_time_seconds
    - dspm_dt_mac_assets_publish_total
    - dspm_dt_mac_discovery_delete_total
    - dspm_dt_mac_discovery_publish_total
    - dspm_dt_mac_query_execution_duration_seconds_count
    - dspm_dt_mac_query_execution_duration_seconds_max
    - dspm_dt_mac_query_execution_duration_seconds_sum
    - dspm_dt_mac_query_execution_failure_count_total
    - dspm_dt_mac_query_execution_success_count_total
    - dspm_d9_crespo_classification_failed_total
    - dspm_d9_crespo_classification_succeeded_total
    - dspm_d9_crespo_preconditions_failed_total
    - dspm_d9_scar_classification_files_failure_total
    - dspm_d9_scar_classification_files_success_total
    - dspm_d9_scar_file_classification_average_duration_total
    - dss_config_dss_id
    - dss_enabled
    - dss_last_updated_on_last_change
    - dss_sync_running
    - dss_sync_time
    - dss_sync_times_difference
    - edr_.+
    - edr_controller_element_duration_seconds
    - effective_ip_range_monitoring_overlapping_rules
    - egress_aggregator_aggregator_compression_rate
    - egress_aggregator_committed_object_size
    - egress_aggregator_compression_job_status
    - egress_aggregator_delete_objects_count
    - egress_aggregator_processed_bytes_total
    - egress_aggregator_processed_objects_count
    - egress_aggregator_raw_object_size
    - egress_aggregator_spawned_aggregators_count
    - emailsec_alerts_backup_duration_seconds_bucket
    - emailsec_alerts_backup_duration_seconds_count
    - emailsec_alerts_backup_duration_seconds_sum
    - emailsec_alerts_bigquery_operation_duration_seconds_bucket
    - emailsec_alerts_bigquery_operation_duration_seconds_count
    - emailsec_alerts_bigquery_operation_duration_seconds_sum
    - emailsec_alerts_email_notification_received_to_issue_latency_bucket
    - emailsec_alerts_email_notification_received_to_issue_latency_count
    - emailsec_alerts_email_notification_received_to_issue_latency_sum
    - emailsec_alerts_email_post_collection_to_issue_latency_bucket
    - emailsec_alerts_email_post_collection_to_issue_latency_count
    - emailsec_alerts_email_post_collection_to_issue_latency_sum
    - emailsec_alerts_email_to_issue_latency_bucket
    - emailsec_alerts_email_to_issue_latency_count
    - emailsec_alerts_email_to_issue_latency_sum
    - emailsec_alerts_entities_backed_up_total
    - emailsec_alerts_external_call_duration_seconds_bucket
    - emailsec_alerts_external_call_duration_seconds_count
    - emailsec_alerts_external_call_duration_seconds_sum
    - emailsec_alerts_messages_processed
    - emailsec_alerts_messages_received_total
    - emailsec_alerts_missing_data_fetches_total
    - emailsec_alerts_policy_messages_published_total
    - emailsec_alerts_processing_errors_total
    - emailsec_alerts_processing_to_issue_latency_bucket
    - emailsec_alerts_processing_to_issue_latency_count
    - emailsec_alerts_processing_to_issue_latency_sum
    - emailsec_alerts_published_total
    - emailsec_alerts_rules_hits_total
    - emailsec_alerts_with_matching_rule
    - emailsec_bq_streamer_batches_total
    - emailsec_bq_streamer_batch_size
    - emailsec_bq_streamer_rows_total
    - emailsec_dispatcher_batch_processing_duration_seconds_bucket
    - emailsec_dispatcher_batch_processing_duration_seconds_count
    - emailsec_dispatcher_batch_processing_duration_seconds_sum
    - emailsec_dispatcher_consumers_active
    - emailsec_dispatcher_items_processed_total
    - emailsec_dispatcher_messages_nacked_total
    - emailsec_dispatcher_messages_received_total
    - emailsec_dispatcher_preprocessing_errors_total
    - emailsec_distrosync_controller_broadcasts_total
    - emailsec_distrosync_controller_syncs_total
    - emailsec_distrosync_controller_sync_duration_seconds_bucket
    - emailsec_distrosync_controller_sync_duration_seconds_count
    - emailsec_distrosync_controller_sync_duration_seconds_sum
    - emailsec_distrosync_subscriber_data_age_seconds
    - emailsec_distrosync_subscriber_notifications_received_total
    - emailsec_distrosync_subscriber_reloads_total
    - emailsec_pq_operation_duration_seconds_bucket
    - emailsec_pq_operation_duration_seconds_count
    - emailsec_pq_operation_duration_seconds_sum
    - emailsec_pq_queue_size
    - emailsec_response_batch_duration_seconds_bucket
    - emailsec_response_batch_duration_seconds_count
    - emailsec_response_batch_duration_seconds_sum
    - emailsec_response_duration_seconds_bucket
    - emailsec_response_duration_seconds_count
    - emailsec_response_duration_seconds_sum
    - emailsec_response_messages_received_total
    - emailsec_response_retries_scheduled_total
    - emailsec_retry_fetched_batch_size_bucket
    - emailsec_retry_fetched_batch_size_count
    - emailsec_retry_fetched_batch_size_sum
    - emailsec_retry_item_processing_duration_seconds_bucket
    - emailsec_retry_item_processing_duration_seconds_count
    - emailsec_retry_item_processing_duration_seconds_sum
    - emailsec_retry_processing_lag_seconds_bucket
    - emailsec_retry_processing_lag_seconds_count
    - emailsec_retry_processing_lag_seconds_sum
    - email_attachment_missing_file
    - email_attachment_pending_decrypt
    - email_attachment_pending_wf_submit
    - email_attachment_raised_alert
    - email_attachment_submitted_to_wf
    - email_attachment_unsupported_file_type
    - email_relay_attachment_submitted_to_wf_total
    - email_relay_attachment_verdict_total
    - email_relay_attachments_total
    - email_relay_gcs_latency_milliseconds_count
    - email_relay_gcs_latency_milliseconds_sum
    - email_relay_steps_latency_milliseconds_count
    - email_relay_steps_latency_milliseconds_sum
    - email_relay_wildfire_error_codes_total
    - email_relay_wildfire_latency_milliseconds_count
    - email_relay_wildfire_latency_milliseconds_sum
    - email_relay_wildfire_unsupported_files_total
    - email_relay_emails_total
    - email_relay_alerts_total
    - email_relay_pubsub_total
    - email_relay_deleted_attachments_total
    - email_relay_attachments_over_limit_total
    - email_relay_scheduler_task_executions_total
    - emailsec_is_email_module_enabled
    - em_integration_asset_processor_errors_total
    - ext_controller_element_duration_seconds
    - ext_controller_element_duration_seconds_count
    - ext_controller_element_duration_seconds_sum
    - ext_controller_element_status
    - failed_requests_total
    - finding_preprocessing_failures_total
    - finding_pubsub_message_histogram_bucket
    - finding_pubsub_message_histogram_count
    - finding_pubsub_message_histogram_sum
    - findings_table_fetching_time_seconds_bucket
    - findings_table_fetching_time_seconds_created
    - findings_table_fetching_time_seconds_sum
    - finding_operation_total
    - finding_metablob_performance_bucket
    - finding_metablob_performance_count
    - finding_metablob_performance_sum
    - xdr_forensics_sams
    - xdr_forensics_hunt_lag_hours
    - gcs_notification_failed_subscriptions
    - graph_execution_rate_limit_violations_total
    - GnzEdrPipeline_.+
    - GnzGlobal_bigquery_ingester_ingestion_policies_backup_rows_total
    - GnzGlobal_DynamicConfig_get_config_failures_total
    - GnzGlobal_pithos_active_streams
    - GnzGlobal_pithos_aggregated_bytes_total
    - GnzGlobal_pithos_aggregated_objects_total
    - GnzGlobal_pithos_aggregation_duration_seconds
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_count
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_sum
    - GnzGlobal_pithos_committed_objects_total
    - GnzGlobal_pithos_dataset_aggregators
    - GnzGlobal_pithos_streamed_bytes_total
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket
    - GnzMbIngester_.+
    - GnzStoryBuilder_.+
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - gonzo_server_.+
    - hpl_json_.+
    - http_requests_total
    - http_server_requests_seconds_count
    - http_server_duration_milliseconds_bucket
    - http_server_duration_milliseconds_count
    - http_server_duration_milliseconds_sum
    - http_server_request_duration_seconds_bucket
    - http_server_request_duration_seconds_count
    - http_server_request_duration_seconds_sum
    - http_server_response_size_bytes_sum
    - http_server_response_size_bytes_count
    - ingestion_quota_exceeded
    - inline_scanner_asset_change_event_failed_total
    - inline_scanner_asset_change_event_ignored_total
    - inline_scanner_asset_change_event_received_total
    - inline_scanner_asset_change_event_replayed_total
    - inline_scanner_assets_finding
    - inline_scanner_assets_scanned_total
    - inline_scanner_cloud_account_fetched
    - inline_scanner_findings_published_failed_total
    - inline_scanner_findings_published_replayed_total
    - inline_scanner_findings_published_success_total
    - inline_scanner_scanlog_generated
    - inline_scanner_scanlog_exported
    - inline_scanner_scanlog_export_timer_milliseconds_sum
    - inline_scanner_asset_change_scan_timer_milliseconds_sum
    - inline_scanner_scanlog_exported_size_kb_sum
    - itdr_data_pipeline_asset_enrichers_duration_seconds_bucket
    - itdr_data_pipeline_asset_enrichers_duration_seconds_count
    - itdr_data_pipeline_asset_enrichers_duration_seconds_sum
    - itdr_data_pipeline_asset_enrichers_errors_total
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_bucket
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_count
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_sum
    - itdr_data_pipeline_assetless_finding_syncer_errors_total
    - itdr_data_pipeline_single_enricher_duration_seconds_bucket
    - itdr_data_pipeline_single_enricher_duration_seconds_count
    - itdr_data_pipeline_single_enricher_duration_seconds_sum
    - itdr_data_pipeline_single_enricher_error_count_total
    - itdr_data_pipeline_cdc_syncer_duration_seconds_bucket
    - itdr_data_pipeline_cdc_syncer_duration_seconds_count
    - itdr_data_pipeline_cdc_syncer_duration_seconds_sum
    - itdr_data_pipeline_cdc_syncer_errors_total
    - itdr_data_pipeline_uai_asset_delete_count_total
    - itdr_data_pipeline_uai_asset_delete_error_count_total
    - itdr_data_pipeline_finding_ingestion_error_count_total
    - itdr_data_pipeline_assets_relations_duration_seconds_bucket
    - itdr_data_pipeline_assets_relations_duration_seconds_count
    - itdr_data_pipeline_assets_relations_duration_seconds_sum
    - itdr_data_pipeline_assets_relations_count_total
    - itdr_data_pipeline_db_migration_error_counter_total
    - itdr_data_pipeline_asset_metadata_error_count
    - itdr_data_pipeline_asset_metadata_error_count_total
    - itdr_data_pipeline_asset_metadata_id_not_found_count
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_sum
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_sum
    - itdr_data_pipeline_asset_updates_count_total
    - itdr_data_pipeline_asset_updates_duration_seconds_bucket
    - itdr_data_pipeline_asset_updates_duration_seconds_count
    - itdr_data_pipeline_asset_updates_duration_seconds_sum
    - itdr_data_pipeline_asset_updates_error_count_total
    - itdr_data_pipeline_asset_updates_manager_input_count_total
    - itdr_data_pipeline_asset_updates_manager_input_error_count_total
    - itdr_data_pipeline_cdc_error_count
    - itdr_data_pipeline_cdc_error_count_total
    - itdr_data_pipeline_cdc_write_count
    - itdr_data_pipeline_cdc_write_count_total
    - itdr_data_pipeline_cdc_write_duration_seconds_bucket
    - itdr_data_pipeline_cdc_write_duration_seconds_count
    - itdr_data_pipeline_cdc_write_duration_seconds_sum
    - itdr_data_pipeline_cie_read_row_error
    - itdr_data_pipeline_cie_read_row_error_total
    - itdr_data_pipeline_cie_row_errors_processed
    - itdr_data_pipeline_cie_row_errors_processed_total
    - itdr_data_pipeline_cie_rows_processed
    - itdr_data_pipeline_cie_rows_processed_total
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_deleted_assets_create_extended_fields_error
    - itdr_data_pipeline_deleted_assets_create_extended_fields_error_total
    - itdr_data_pipeline_deleted_assets_read_row_error
    - itdr_data_pipeline_deleted_assets_read_row_error_total
    - itdr_data_pipeline_deleted_assets_row_errors_processed
    - itdr_data_pipeline_deleted_assets_row_errors_processed_total
    - itdr_data_pipeline_deleted_assets_rows_processed
    - itdr_data_pipeline_deleted_assets_rows_processed_total
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_dss_sync_input_count
    - itdr_data_pipeline_dss_sync_input_count_total
    - itdr_data_pipeline_dss_sync_input_error_count
    - itdr_data_pipeline_dss_sync_input_error_count_total
    - itdr_data_pipeline_pubsub_output_count
    - itdr_data_pipeline_pubsub_output_count_total
    - itdr_data_pipeline_pubsub_output_error_count
    - itdr_data_pipeline_pubsub_output_error_count_total
    - itdr_data_pipeline_risk_handler_error_count
    - itdr_data_pipeline_risk_handler_read_duration_seconds_bucket
    - itdr_data_pipeline_risk_handler_read_duration_seconds_count
    - itdr_data_pipeline_risk_handler_read_duration_seconds_sum
    - itdr_data_pipeline_uai_asset_ingestion_error_count
    - itdr_data_pipeline_uai_asset_ingestion_error_count_total
    - itdr_data_pipeline_uai_asset_patch_count
    - itdr_data_pipeline_uai_asset_patch_count_total
    - itdr_data_pipeline_uai_asset_patch_error_count
    - itdr_data_pipeline_uai_asset_patch_error_count_total
    - itdr_data_pipeline_assets_retention_duration_seconds_count
    - itdr_data_pipeline_assets_retention_duration_seconds_bucket
    - itdr_data_pipeline_assets_retention_duration_seconds_sum
    - itdr_data_pipeline_retention_assets_read_row_error
    - itdr_data_pipeline_retention_assets_read_row_error_total
    - itdr_data_pipeline_retention_assets_rows_processed
    - itdr_data_pipeline_retention_assets_rows_processed_total
    - itdr_data_pipeline_baseline_read_row_error_total
    - itdr_data_pipeline_baseline_rows_processed_total
    - itdr_data_pipeline_baseline_row_errors_processed_total
    - itdr_data_pipeline_baseline_query_iterator_errors_total
    - itdr_data_pipeline_baseline_outputs_errors_total
    - itdr_data_pipeline_baseline_risk_insights_enrich_error_total
    - itdr_data_pipeline_baseline_risk_insights_enriched_assets_total
    - itdr_data_pipeline_baseline_okta_users_found_total
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_baseline_handler_handled_rows_total
    - itdr_data_pipeline_baseline_metadata_error_counter_total
    - itdr_data_pipeline_baseline_metadata_inserted_counter_total
    - itdr_data_pipeline_cap_data_repository_duration_seconds_count
    - itdr_data_pipeline_cap_data_repository_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_repository_duration_seconds_sum
    - itdr_data_pipeline_cap_data_repository_errors_total
    - itdr_data_pipeline_cap_data_publish_success_total
    - itdr_data_pipeline_cap_data_publish_errors_total
    - itdr_data_pipeline_cap_data_upload_duration_seconds_count
    - itdr_data_pipeline_cap_data_upload_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_upload_duration_seconds_sum
    - itdr_data_pipeline_cap_data_upload_success_total
    - itdr_data_pipeline_cap_data_upload_errors_total
    - itdr_data_pipeline_zip_manager_create_new_zip_file_errors_total
    - itdr_data_pipeline_zip_manager_create_file_in_zip_errors_total
    - itdr_data_pipeline_zip_manager_write_data_to_file_in_zip_errors_total
    - itdr_data_pipeline_zip_manager_close_zip_writer_errors_total
    - itdr_data_pipeline_zip_manager_close_new_zip_file_errors_total
    - itdr_data_pipeline_realtime_updater_duration_seconds_count
    - itdr_data_pipeline_realtime_updater_duration_seconds_bucket
    - itdr_data_pipeline_realtime_updater_duration_seconds_sum
    - itdr_data_pipeline_realtime_updater_rows_processed_total
    - itdr_data_pipeline_realtime_updater_process_errors_total
    - itdr_data_pipeline_realtime_log_insert_rows_handler_error_total
    - itdr_data_pipeline_realtime_log_insert_rows_handler_inserted_rows_total
    - itdr_data_pipeline_password_analyzer_input_count
    - itdr_data_pipeline_password_analyzer_input_count_total
    - itdr_data_pipeline_password_analyzer_input_error_count
    - itdr_data_pipeline_password_analyzer_input_error_count_total
    - itdr_data_pipeline_pwd_analyzer_process_duration_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_bucket
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_bucket
    - itdr_data_pipeline_pwd_analyzer_process_duration_sum
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_sum
    - itdr_data_pipeline_pwd_analyzer_users_proceed
    - itdr_data_pipeline_pwd_analyzer_users_proceed_total
    - itdr_data_pipeline_pwd_analyzer_read_object_error
    - itdr_data_pipeline_pwd_analyzer_read_object_error_total
    - itdr_data_pipeline_pwd_analyzer_delete_object_error
    - itdr_data_pipeline_pwd_analyzer_delete_object_error_total
    - itdr_data_pipeline_pwd_analyzer_password_handler_error
    - itdr_data_pipeline_pwd_analyzer_password_handler_error_total
    - itdr_data_pipeline_pwd_analyzer_mandatory_file_missing
    - itdr_data_pipeline_pwd_analyzer_mandatory_file_missing_total
    - itdr_data_pipeline_pwd_analyzer_user_enricher_error
    - itdr_data_pipeline_pwd_analyzer_user_enricher_error_total
    - itdr_data_pipeline_pwd_analyzer_password_analyzer_error
    - itdr_data_pipeline_pwd_analyzer_password_analyzer_error_total
    - itdr_data_pipeline_pwd_analyzer_handle_open_findings_error
    - itdr_data_pipeline_pwd_analyzer_handle_open_findings_error_total
    - itdr_data_pipeline_pwd_analyzer_handle_close_findings_error
    - itdr_data_pipeline_pwd_analyzer_handle_close_findings_error_total
    - itdr_data_pipeline_pwd_analyzer_dao_read_error
    - itdr_data_pipeline_pwd_analyzer_dao_read_error_total
    - itdr_data_pipeline_pwd_analyzer_matches
    - itdr_data_pipeline_pwd_analyzer_matches_total
    - itdr_data_pipeline_pwd_analyzer_access_salt_error
    - itdr_data_pipeline_pwd_analyzer_access_salt_error_total
    - itdr_data_pipeline_pwd_analyzer_extract_data_from_zip_error
    - itdr_data_pipeline_pwd_analyzer_extract_data_from_zip_error_total
    - itdr_data_pipeline_salt_rotation_count
    - itdr_data_pipeline_salt_rotation_count_total
    - itdr_data_pipeline_salt_rotation_error
    - itdr_data_pipeline_salt_rotation_error_total
    - itdr_data_pipeline_salt_add_version_count
    - itdr_data_pipeline_salt_add_version_count_total
    - itdr_data_pipeline_salt_add_version_error
    - itdr_data_pipeline_salt_add_version_error_total
    - itdr_data_pipeline_salt_get_active_version_error
    - itdr_data_pipeline_salt_get_active_version_error_total
    - itdr_data_pipeline_salt_remove_version_error
    - itdr_data_pipeline_salt_remove_version_error_total
    - itdr_data_pipeline_redis_error_count_total
    - itdr_data_pipeline_agent_pwd_sync
    - itdr_data_pipeline_agent_pwd_sync_total
    - itdr_data_pipeline_cie_enricher_duration_bucket
    - itdr_data_pipeline_cie_enricher_duration_count
    - itdr_data_pipeline_cie_enricher_duration_sum
    - itdr_data_pipeline_cie_enricher_error_count
    - itdr_data_pipeline_risk_handler_table_doesnt_exists_count
    - itdr_data_pipeline_dp_bus_error_count_total
    - itdr_data_pipeline_ad_hygiene_input_count_total
    - itdr_data_pipeline_ad_hygiene_input_error_count_total
    - itdr_data_pipeline_ad_hygiene_dtos_processed_total
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_bucket
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_count
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_sum
    - itdr_risk_processor_bigquery_output_counter
    - itdr_risk_processor_bigquery_output_error_counter
    - itdr_risk_processor_case_update_without_assets_counter
    - itdr_risk_processor_dao_access_duration_seconds_bucket
    - itdr_risk_processor_dao_access_duration_seconds_count
    - itdr_risk_processor_dao_access_duration_seconds_sum
    - itdr_risk_processor_db_migration_error_counter
    - itdr_risk_processor_pubsub_input_counter
    - itdr_risk_processor_pubsub_input_error_counter
    - itdr_risk_processor_pubsub_output_counter
    - itdr_risk_processor_pubsub_output_error_counter
    - itdr_risk_processor_risk_cron_duration_seconds_bucket
    - itdr_risk_processor_risk_cron_duration_seconds_count
    - itdr_risk_processor_risk_cron_duration_seconds_sum
    - itdr_risk_processor_case_update_late_arrival_skip_total
    - itdr_risk_processor_case_update_late_arrival_total
    - itdr_cap_responder_pubsub_input_email_total
    - itdr_cap_responder_pubsub_input_email_error_total
    - itdr_cap_responder_pubsub_input_mfa_total
    - itdr_cap_responder_pubsub_input_mfa_error_total
    - itdr_cap_responder_pubsub_output_total
    - itdr_cap_responder_pubsub_output_error_total
    - itdr_cap_responder_pubsub_input_active_token_async_total
    - itdr_cap_responder_pubsub_input_active_token_async_error_total
    - itdr_cap_responder_throttling_seconds_bucket
    - itdr_cap_responder_throttling_seconds_count
    - itdr_cap_responder_throttling_seconds_sum
    - itdr_cap_responder_throttling_request_total
    - itdr_cap_responder_throttling_response_status_total
    - itdr_cap_responder_xsoar_cache_seconds_bucket
    - itdr_cap_responder_xsoar_cache_seconds_count
    - itdr_cap_responder_xsoar_cache_seconds_sum
    - itdr_cap_responder_xsoar_request_seconds_bucket
    - itdr_cap_responder_xsoar_request_seconds_count
    - itdr_cap_responder_xsoar_request_seconds_sum
    - itdr_cap_responder_xsoar_request_total
    - itdr_cap_responder_xsoar_response_status_total
    - itdr_cap_responder_xsoar_cache_total
    - itdr_cap_responder_block_responder_seconds_bucket
    - itdr_cap_responder_block_responder_seconds_count
    - itdr_cap_responder_block_responder_seconds_sum
    - itdr_cap_responder_block_responder_status_total
    - itdr_cap_responder_block_mfa_verification_status_total
    - itdr_cap_responder_rule_duration_seconds_bucket
    - itdr_cap_responder_rule_duration_seconds_count
    - itdr_cap_responder_rule_duration_seconds_sum
    - itdr_cap_responder_rule_cache_total
    - itdr_cap_responder_mfa_get_tokes_async_duration_seconds_bucket
    - itdr_cap_responder_mfa_get_tokes_async_duration_seconds_count
    - itdr_cap_responder_mfa_get_tokes_async_duration_seconds_sum
    - itdr_cap_responder_delete_expired_tokens_total
    - itdr_cap_responder_delete_expired_tokens_error_total
    - itdr_cap_responder_delete_expired_tokens_seconds_bucket
    - itdr_cap_responder_delete_expired_tokens_seconds_count
    - itdr_cap_responder_delete_expired_tokens_seconds_sum
    - itdr_cap_responder_rate_limiter_request_seconds_bucket
    - itdr_cap_responder_rate_limiter_request_seconds_count
    - itdr_cap_responder_rate_limiter_request_seconds_sum
    - itdr_cap_responder_rate_limiter_request_total
    - itdr_cap_responder_rate_limiter_response_total
    - itdr_cap_audits_audits_total_time_seconds_bucket
    - itdr_cap_audits_audits_total_time_seconds_count
    - itdr_cap_audits_audits_total_time_seconds_sum
    - itdr_cap_audits_audit_response_counter_total
    - itdr_cap_audits_agent_cache_hit_total
    - itdr_cap_audits_agent_cache_miss_total
    - itdr_cap_audits_platform_api_call_total
    - itdr_api_dao_access_duration_seconds_bucket
    - itdr_api_dao_access_duration_seconds_count
    - itdr_api_dao_access_duration_seconds_sum
    - itdr_api_dao_access_errors_total
    - itdr_api_pubsub_output_error_count_total
    - itdr_api_up_to_date_configuration_error_count_total
    - itdr_api_baseline_realtime_error_count_total
    - itdr_api_full_snapshot_error_count_total
    - itdr_api_full_snapshot_all_empty_count_total
    - itdr_api_concurrent_requests
    - itdr_api_latency_seconds_bucket
    - itdr_api_latency_seconds_count
    - itdr_api_latency_seconds_sum
    - itdr_api_requests_total
    - itdr_api_cap_report_message_count_total
    - known_attachment_malwares
    - master_tenant_listener_is_stale
    - memsql_counter_bytes_received
    - memsql_counter_bytes_sent
    - memsql_counter_connections
    - memsql_counter_failed_read_queries
    - memsql_counter_failed_write_queries
    - memsql_counter_successful_read_queries
    - memsql_counter_successful_write_queries
    - memsql_distributed_partitions_offline
    - memsql_distributed_partitions_online
    - memsql_distributed_partitions_total
    - memsql_status_aborted_connects
    - memsql_status_failed_read_queries
    - memsql_status_failed_write_queries
    - memsql_workload_management_total_queries_cancelled_since_startup
    - memsql_workload_management_total_queries_finished_since_startup
    - memsql_workload_management_total_queries_started_since_startup
    - metrics_aggregator_.+
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed_seconds
    - number_of_cloud_accounts
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed
    - neo4j_.*bolt_connections.*
    - neo4j_.*bolt_messages_received.*
    - neo4j_.*bolt_messages_started.*
    - neo4j_dbms_pool_bolt_total_size
    - neo4j_dbms_pool_bolt_total_used
    - neo4j_dbms_pool_bolt_used_heap
    - neo4j_.*check_point.*
    - neo4j_.*cypher_replan_events.*
    - neo4j_.*cypher_cache.*
    - neo4j_.*pool_transaction.*_total_used
    - neo4j_.*pool_transaction.*_used_heap
    - neo4j_.*store_size.*
    - neo4j_.*transaction_active_read
    - neo4j_.*transaction_active_write
    - neo4j_.*transaction_committed.*
    - neo4j_.*transaction_peak_concurrent.*
    - neo4j_.*transaction_rollbacks.*
    - neo4j_.*page_cache_hit.*
    - neo4j_.*page_cache_page_faults.*
    - neo4j_.*page_cache_usage_ratio
    - neo4j_.*vm_file_descriptors_count
    - neo4j_.*vm_gc_time.*
    - neo4j_.*vm_heap_used
    - netscan_authentication_success
    - netscan_avg_target_size
    - netscan_host_properties
    - netscan_hosts_currently_scanning
    - netscan_kms_error_total
    - netscan_processor_cns_host_dead_in_scan
    - netscan_processor_cns_host_max_tuple_batch_size
    - netscan_processor_cns_host_prior_scan_age_bucket
    - netscan_processor_cns_host_unresponsive
    - netscan_processor_dao_access_duration_seconds_bucket
    - netscan_processor_db_migration_error
    - netscan_processor_netscan_results_batch_process_bucket
    - netscan_processor_scan_result_batch_failure
    - netscan_processor_scan_result_batch_size_bucket
    - netscan_processor_scan_result_incompatible
    - netscan_processor_scan_result_match
    - netscan_processor_scan_result_message_count_bucket
    - netscan_processor_scan_result_new_cns_host
    - netscan_processor_scan_task_processed
    - netscan_processor_scan_result_unrelated_hostname
    - netscan_processor_scan_result_with
    - netscan_processor_uai_pubsub_output_error
    - netscan_scan_duration_count
    - netscan_scan_duration_created
    - netscan_scan_duration_sum
    - netscan_scan_timeouts
    - netscan_scans_configured
    - netscan_scans_in_flight
    - netscan_scans_scheduled
    - netscan_scans_status
    - netscan_test_result
    - notification_migration_errors_total
    - notification_migration_result_ratio
    - otelcol_.+
    - oversized_alert_trimming_count
    - oversized_alert_trimmed_field_bucket
    - partyzaurus_.+
    - partyzaurus_controller_element_duration_seconds
    - platform_compliance_active_assessment_profile_count
    - platform_compliance_api_requests_latency_seconds_bucket
    - platform_compliance_api_requests_latency_seconds_count
    - platform_compliance_api_requests_latency_seconds_sum
    - platform_compliance_api_requests_total
    - platform_compliance_calculation_last_run
    - platform_compliance_calculation_run_time
    - platform_compliance_content_hash
    - platform_compliance_controls_table_size
    - platform_compliance_custom_assessment_profile_count
    - platform_compliance_custom_controls_count
    - platform_compliance_custom_controls_not_attached_to_standards
    - platform_compliance_custom_controls_with_no_rules
    - platform_compliance_custom_standards_with_no_controls
    - platform_compliance_custom_standards_count
    - platform_compliance_oob_assessment_profile_count
    - platform_compliance_oob_controls_count
    - platform_compliance_oob_standards_count
    - platform_compliance_reports_downloaded_by_customers_created
    - platform_compliance_reports_downloaded_by_customers_total
    - platform_compliance_scan_log_count_in_last_24_hours
    - platform_compliance_scheduled_reports_count
    - platform_compliance_standards_table_size
    - platform_compliance_tenant
    - platform_compliance_used_assets_count
    - preprocessed_data_batcher_compression_job_status
    - preprocessed_data_batcher_processed_bytes_total
    - preprocessed_data_batcher_processed_objects_count
    - preprocessed_data_batcher_raw_object_size
    - preprocessed_data_batcher_spawned_aggregators_count
    - preprocessed_data_batcher_streaming_object_duration_seconds_bucket
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - prom_gonzo_storage_adapter_.+
    - pz_.+
    - pz_schema_manager_.+
    - policy_migration_result_ratio
    - policy_migration_errors_total
    - risk_calculation_failures_total
    - rule_migration_result
    - rule_migration_errors_total
    - xsoar_podman_cpu
    - xsoar_podman_memory
    - xsoar_podman_uptime
    - xsoar_rc.+
    - spanner_migration_execution_time
    - spanner_migration_rows_migrated
    - spanner_migration_status
    - spanner_migration_total_rows_to_migrate
    - spur_dms_actual_batch_size_bucket
    - spur_dms_actual_batch_size_count
    - spur_dms_actual_batch_size_sum
    - spur_dms_cache_size
    - spur_dms_fetch_duration_seconds_bucket
    - spur_dms_fetch_duration_seconds_count
    - spur_dms_fetch_duration_seconds_sum
    - spur_dms_fetch_errors_count
    - spur_dms_inactive_until_unix_milli
    - spur_dms_ip_parse_error
    - spur_dms_poll_failure_count
    - spur_dms_queried_count
    - spur_dms_request_count
    - spur_dms_skipped_ips_count
    - spur_dms_spur_generation_time_unix_milli
    - spur_pz_actual_batch_size_bucket
    - spur_pz_actual_batch_size_count
    - spur_pz_actual_batch_size_sum
    - spur_pz_cache_size
    - spur_pz_fetch_duration_seconds_bucket
    - spur_pz_fetch_duration_seconds_count
    - spur_pz_fetch_duration_seconds_sum
    - spur_pz_fetch_errors_count
    - spur_pz_inactive_until_unix_milli
    - spur_pz_ip_parse_error
    - spur_pz_poll_failure_count
    - spur_pz_queried_count
    - spur_pz_request_count
    - spur_pz_skipped_ips_count
    - spur_pz_spur_generation_time_unix_milli
    - standard_events_event_dropped_counter_created
    - standard_events_event_dropped_counter_total
    - standard_events_event_failed_sys_error_counter_created
    - standard_events_event_failed_sys_error_counter_total
    - standard_events_event_failed_validation_counter_created
    - standard_events_event_failed_validation_counter_total
    - standard_events_event_processed_success_counter_created
    - standard_events_event_processed_success_counter_total
    - standard_events_queue_size
    - storybuilder_.+
    - support_case_auto_generate_tsf_request_timeout_total
    - support_case_failed_auto_generate_tsf_request_total
    - support_case_failed_upload_files_total
    - support_case_number_of_support_case_successfully_created_total
    - support_case_number_of_support_case_failed_to_create_total
    - support_case_success_auto_generate_tsf_request_total
    - support_case_success_upload_files_total
    - storybuilder_filtered_events_total
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used
    - uai_account_offboarding_deleted_assets_count_total
    - uai_account_offboarding_error_count_total
    - uai_aggregated_assets_cdc_delay_seconds
    - uai_api_errors_count_total
    - uai_api_requests_count_total
    - uai_endpoints_health_check_failed
    - vectorized_matcher_current_content_version_exporting_time_in_seconds
    - vectorized_matcher_current_content_version_loading_time_in_seconds
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_bucket
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_count
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_sum
    - verdict_manager_batches_completed
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_bucket
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_count
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_sum
    - verdict_manager_cspm_listener_ack_cnt
    - verdict_manager_findings_publish_failed
    - verdict_manager_findings_publish_success
    - verdict_manager_findings_created
    - verdict_manager_findings_closed
    - verdict_manager_issues_created
    - verdict_manager_issues_updated
    - verdict_manager_issues_closed
    - verdict_manager_issues_batch_publish_success
    - verdict_manager_issues_batch_publish_failed
    - verdict_manager_reconcile_failed
    - verdict_manager_reconcile_timer_milliseconds_bucket
    - verdict_manager_reconcile_timer_milliseconds_count
    - verdict_manager_reconcile_timer_milliseconds_sum
    - verdict_manager_reconcile_verdicts_cleanup
    - verdict_manager_verdicts_load_job
    - verdict_manager_xspm_listener_ack_cnt
    - verdict_manager_xspm_listener_timer_milliseconds_bucket
    - verdict_manager_xspm_listener_timer_milliseconds_count
    - verdict_manager_xspm_listener_timer_milliseconds_sum
    - verdict_manager_xspm_loader_timer_milliseconds_bucket
    - verdict_manager_xspm_loader_timer_milliseconds_count
    - verdict_manager_xspm_loader_timer_milliseconds_sum
    - verdict_manager_xspm_reconcile_timer_milliseconds_bucket
    - verdict_manager_xspm_reconcile_timer_milliseconds_count
    - verdict_manager_xspm_reconcile_timer_milliseconds_sum
    - vip_api_concurrent_requests
    - vip_api_latency_seconds_bucket
    - vip_api_latency_seconds_count
    - vip_api_latency_seconds_sum
    - vip_api_load_evaluator_total
    - vip_api_message_latency_seconds_bucket
    - vip_api_message_latency_seconds_count
    - vip_api_message_latency_seconds_sum
    - vip_api_message_total
    - vip_api_requests_total
    - vip_api_update_view_total
    - verdict_manager_verdicts_fetched
    - vsg_reconcile_time_bucket
    - vsg_reconcile_time_count
    - vsg_reconcile_time_sum
    - vsg_oom_scaler_applied_memory_resources
    - vsg_oom_scaler_last_applied_timestamp
    - vsg_oom_scaler_last_reconcile_timestamp
    - vsg_oom_scaler_pod_request_memory_max
    - vsg_oom_scaler_reconcile_time_bucket
    - vsg_oom_scaler_reconcile_time_count
    - vsg_oom_scaler_reconcile_time_sum
    - vsg_oom_scaler_resolve_source_metric_bucket
    - vsg_oom_scaler_resolve_source_metric_count
    - vsg_oom_scaler_resolve_source_metric_sum
    - vsg_oom_scaler_target_memory_resources
    - vsg_oom_scaler_under_cooldown_timestamp
    - vsg_vertical_scaler_applied_cpu_resources
    - vsg_vertical_scaler_applied_memory_resources
    - vsg_vertical_scaler_current_step_thresholds
    - vsg_vertical_scaler_hpa_invalid_timestamp
    - vsg_vertical_scaler_last_applied_timestamp
    - vsg_vertical_scaler_last_reconcile_timestamp
    - vsg_vertical_scaler_reconcile_time_bucket
    - vsg_vertical_scaler_reconcile_time_count
    - vsg_vertical_scaler_reconcile_time_sum
    - vsg_vertical_scaler_resolve_source_metric_bucket
    - vsg_vertical_scaler_resolve_source_metric_count
    - vsg_vertical_scaler_resolve_source_metric_sum
    - vsg_vertical_scaler_scaled_replicas
    - vsg_vertical_scaler_source_metric_result
    - vsg_vertical_scaler_target_cpu_resources
    - vsg_vertical_scaler_target_memory_resources
    - vsg_vertical_scaler_target_replicas
    - vsg_vertical_scaler_under_cooldown_timestamp
    - vsg_zero_scaler_last_applied_timestamp
    - vsg_zero_scaler_last_reconcile_timestamp
    - vsg_zero_scaler_manually_downscaled_timestamp
    - vsg_zero_scaler_reconcile_time_bucket
    - vsg_zero_scaler_reconcile_time_count
    - vsg_zero_scaler_reconcile_time_sum
    - vsg_zero_scaler_resolve_source_metric_bucket
    - vsg_zero_scaler_resolve_source_metric_count
    - vsg_zero_scaler_resolve_source_metric_sum
    - vsg_zero_scaler_scaled_replicas
    - vsg_zero_scaler_source_metric_result
    - vsg_zero_scaler_target_replicas
    - vsg_zero_scaler_thresholds
    - vsg_pvc_scaler_reconcile_time_bucket
    - vsg_pvc_scaler_reconcile_time_count
    - vsg_pvc_scaler_reconcile_time_sum
    - vsg_pvc_scaler_last_reconcile_timestamp
    - vsg_pvc_scaler_last_applied_timestamp
    - vsg_pvc_scaler_resolve_source_metric_bucket
    - vsg_pvc_scaler_resolve_source_metric_count
    - vsg_pvc_scaler_resolve_source_metric_sum
    - vsg_pvc_scaler_source_metric_result
    - vsg_pvc_scaler_target_pvc_size
    - vsg_pvc_scaler_applied_pvc_size
    - vuln_action_plan_creation_failure_total
    - vuln_action_plan_creation_metrics
    - vuln_action_plan_recon_failure_total
    - vuln_action_plan_recon_metrics
    - vxp_bigquery_rows_read_total
    - vxp_bigquery_slots_used_total
    - vxp_dyanmic_config_values
    - vxp_http_server_requests_count
    - vxp_http_server_requests_sum
    - vxp_job_run_time_count
    - vxp_issues_published_total
    - vxp_job_run_time_sum
    - vxp_policy_sync_actions_total
    - vxp_policy_sync_findings_total
    - vxp_protofinding_query_time_count
    - vxp_protofinding_query_time_sum
    - vxp_pubsub_errors_total
    - vxp_pubsub_processing_latency_bucket
    - vxp_spanner_protofinding_query_time_count
    - vxp_spanner_protofinding_query_time_sum
    - vxp_redis_operations_count
    - vxp_redis_operations_sum
    - vxp_sync_cve_coverage_job_malformed_rows_total
    - wf_vs_get_verdicts_request_latency_seconds_bucket
    - wf_vs_get_verdicts_request_failed_total
    - wf_vs_get_verdicts_size
    - wf_vs_set_upload_request_failed
    - wf_vs_set_upload_request_success
    - wlm_monitoring_.+
    - xcloud_assets_count
    - xcloud_discovery_hours_delay
    - xcloud_inventory_strategies_count_by_provider_and_status
    - xcloud_onboarding_message
    - xcloud_strategies_delay_hours_by_service
    - xdm_.+
    - xdr_active_lightweight_agents_7d
    - xdr_addons_license_status
    - xdr_agent.+
    - xdr_alert_domain_migration_error
    - xdr_alert_source_delay_time
    - xdr_alert_sync_tags_databases_error_total
    - xdr_alerts_.+
    - xdr_alerts_fetcher.+
    - xdr_api_errors_total
    - xdr_api_waitress_allocated_threads
    - xdr_api_waitress_occupied_threads
    - xdr_api_waitress_queued_requests
    - xdr_asm_mega_join_cache_creation_error_total
    - xdr_asm_mega_join_cache_delay
    - xdr_asm_xpanse_replication_delay
    - xdr_asset_command_.+
    - xdr_asset_score_changes_received_created
    - xdr_asset_score_changes_received_total
    - xdr_asset_score_changes_sent_to_cie_created
    - xdr_asset_score_changes_sent_to_cie_total
    - xdr_bigquery_bytes_processed_count
    - xdr_bigquery_bytes_processed_sum
    - xdr_bigquery_execution_time_count
    - xdr_bigquery_execution_time_sum
    - xdr_bigquery_queue_time_count
    - xdr_bigquery_queue_time_sum
    - xdr_bigquery_zslots_count
    - xdr_bigquery_zslots_sum
    - xdr_biocs_assigned_to_profiles_count
    - xdr_bq_stats_not_delivered_from_pubsub_total
    - xdr_bq_stats_redis_list_length_maxed_total
    - xdr_broker_.+
    - xdr_calc_.+
    - xdr_card_.+
    - xdr_case_.+
    - xdr_cie_risk_score_errors_created
    - xdr_cie_risk_score_errors_total
    - xdr_calc_last_association_replication_time
    - xdr_clcs_counter
    - xdr_clcs_multi_csp_connected
    - xdr_clcs_multi_region_connected_count
    - xdr_clcs_multi_region_enabled
    - xdr_clcs_multi_salesforce_enabled
    - xdr_clcs_subscriptions_data
    - xdr_cloud_agents_count
    - xdr_cloud_license_count
    - xdr_cloud_license_purchased
    - xdr_cloud_pro_agents_count
    - xdr_cold_storage_oldest_raw_object
    - xdr_collection_.+
    - xdr_collector_.+
    - xdr_count_operational_status_and_os_type
    - xdr_count_operational_status_description_and_os_type
    - xdr_cts_active_sessions
    - xdr_cts_active_tokens
    - xdr_cts_cache_hits_total
    - xdr_cts_cache_miss_total
    - xdr_cts_fresh_token_total
    - xdr_cts_request_time_count
    - xdr_cts_request_time_sum
    - xdr_cts_request_total
    - xdr_cts_sts_error_total
    - xdr_cts_sts_unauthorized_total
    - xdr_cts_waitress_allocated_threads
    - xdr_cts_waitress_occupied_threads
    - xdr_cts_waitress_queued_requests
    - xdr_current_connected_agents
    - xdr_current_connected_edr_agents
    - xdr_data_usage_24_hours
    - xdr_device_control_license
    - xdr_dss_.*
    - xdr_dumpster_auto_upload_config
    - xdr_dumpster_auto_upload_dumps_count
    - xdr_edr_active_agents_24h
    - xdr_edr_agents_24_hours
    - xdr_edr_agents_count
    - xdr_edr_license_count
    - xdr_edr_license_expiration
    - xdr_edr_license_purchased
    - xdr_egress_oldest_raw_object
    - xdr_email_notification_received_to_issue_latency_bucket
    - xdr_email_notification_received_to_issue_latency_count
    - xdr_email_notification_received_to_issue_latency_sum
    - xdr_email_post_collection_to_issue_latency_bucket
    - xdr_email_post_collection_to_issue_latency_count
    - xdr_email_post_collection_to_issue_latency_sum
    - xdr_email_service.+
    - xdr_email_to_issue_latency_bucket
    - xdr_email_to_issue_latency_count
    - xdr_email_to_issue_latency_sum
    - xdr_epp_agents_count
    - xdr_epp_license_count
    - xdr_epp_license_expiration
    - xdr_epp_license_purchased
    - xdr_forensics_agents
    - xdr_forensics_licenses
    - xdr_forensics_tenant
    - xdr_get_ai_case_details_total
    - xdr_get_ai_case_details_time_bucket
    - xdr_get_ai_case_details_time_sum
    - xdr_get_ai_case_details_time_count
    - xdr_get_ai_case_details_tokens_total
    - xdr_get_unused_api_key_ids
    - xdr_generate_notification_error_total
    - xdr_gvs_.+
    - xdr_host_insights_is_enabled
    - xdr_hpa_xql_pubsub_metric
    - xdr_hpa_pipeline_pubsub_metric
    - xdr_hpa_dms_pubsub_metric
    - xdr_incident_alert_data_sync_error_total
    - xdr_incidents_by_status_avg
    - xdr_incidents_by_status_count
    - xdr_informative_btp_alerts
    - xdr_init_app_status
    - xdr_init_app_took
    - xdr_invalidate_user_role_failure
    - xdr_investigation_in_progress
    - xdr_ios_large_digest_report_total
    - xdr_issue_evidence_create_count_total
    - xdr_issue_evidence_papi_read_count_total
    - xdr_issue_evidence_read_count_total
    - xdr_issue_evidence_update_count_total
    - xdr_issue_evidence_validation_error_count_total
    - xdr_issue_enricher_resolution_action_template_create_count_total
    - xdr_issue_enricher_resolution_action_template_update_count_total
    - xdr_issue_enricher_resolution_action_send_to_dlq_total
    - xdr_issue_enricher_resolution_action_template_cache_fallback_to_mysql_total
    - xdr_issue_enricher_resolution_action_papi_include_actions_count_total
    - xdr_issue_fetcher_handle_message_time_count
    - xdr_issue_fetcher_handle_message_time_created
    - xdr_issue_fetcher_handle_message_time_sum
    - xdr_issue_fetcher_parse_time_count
    - xdr_issue_fetcher_parse_time_created
    - xdr_issue_fetcher_parse_time_sum
    - xdr_issue_handle_message_time_count
    - xdr_issue_handle_message_time_created
    - xdr_issue_handle_message_time_sum
    - xdr_issue_send_to_dlq_total
    - xdr_issue_rate_limit_total
    - xdr_issue_updater_handle_message_time_count
    - xdr_issue_updater_handle_message_time_sum
    - xdr_issue_updater_handle_message_time_created
    - xdr_issue_updater_optimistic_lock_total
    - xdr_kpi_.+
    - xdr_license_fetch_failures
    - xdr_logging_.*
    - xdr_mail_event_processor_.+
    - xdr_mailing_queue_count
    - xdr_managed_tenant_monitor_info
    - xdr_matching_service_detection_queue_depth
    - xdr_mdr_license
    - xdr_forensics_migration_status
    - xdr_mssp_license
    - xdr_mth_license
    - xdr_mysql_.*
    - xdr_nfr_license
    - xdr_notifcation_mail_queue_count
    - xdr_notification_dead_letter_queue_table_count
    - xdr_p2p_discovery_table_count
    - xdr_p2p_scanable_agents_count
    - xdr_phase1_installers_flag
    - xdr_platform_migration_completed
    - xdr_platform_migration_failed
    - xdr_platform_migration_start
    - xdr_platform_migration_status
    - xdr_platform_migration_time_risk
    - xdr_preprocessed_data_batcher_oldest_object
    - xdr_prisma_pairing_status
    - xdr_pz_schema_auto_detector_latency_bucket
    - xdr_pz_schema_auto_detector_latency_count
    - xdr_pz_schema_detect_dataset_counter_total
    - xdr_pz_schema_detector_observer_mysql_latency_bucket
    - xdr_pz_schema_detector_observer_mysql_latency_count
    - xdr_pz_schema_waitress_occupied_threads
    - xdr_pz_schema_is_dataset_max_keys_reached
    - xdr_pz_schema_bq_external_raw_table_column_count
    - xdr_queries_by_status_count
    - xdr_redis_.+
    - xdr_reports_generator_.+
    - xdr_request_processing_seconds_count
    - xdr_request_processing_seconds_sum
    - xdr_request_response_size_count
    - xdr_request_response_size_sum
    - xdr_retention_enforcement_status
    - xdr_retention_enforcement_task_run
    - xdr_retention_service_sync_retention_stats_failures_total
    - xdr_retention_simulation_status
    - xdr_retryable_grouping_alerts_.+
    - xdr_sbac_enabled
    - xdr_sbac_enabled.+
    - xdr_sbac_mode
    - xdr_schd_task_delay_histogram_bucket
    - xdr_scheduler_wlm_working
    - xdr_search_index.+
    - xdr_story_builder_cpu_utilization
    - xdr_story_builder_max_delay
    - xdr_scouter_to_group_calculation_count
    - xdr_scouter_to_group_calculation_duration
    - xdr_stuck_triage_records
    - xdr_tags_count
    - xdr_tags_sync_not_running
    - xdr_task_processor_process_message_time_count
    - xdr_task_processor_process_message_time_created
    - xdr_task_processor_process_message_time_sum
    - xdr_tenant_configuration_csp
    - xdr_tenant_configuration_subdomain
    - xdr_tenant_configuration_tenant_type
    - xdr_tenant_distribution_list
    - xdr_tenant_configuration_is_migrated_to_platform
    - xdr_tenant_configuration_is_platform
    - xdr_tim_.+
    - xdr_total_agents_by_content_status
    - xdr_total_size_of_stored_edr
    - xdr_unknown_email_issue_type_total
    - xdr_unused_profile_count
    - xdr_upload_management_audit_to_gcs
    - xdr_users_access_to_tenant_count
    - xdr_users_login_to_tenant
    - xdr_va_.+
    - xdr_vulnerability_assessment_.+
    - xdr_wec_.+
    - xdr_wildfire_submit_url_low_priority_req_counter_total
    - xdr_num_of_disabled_rules
    - xdr_num_of_partially_disabled_rules
    - xdr_num_of_enabled_rules
    - xdr_platform_migration_tf_completed
    - xdr_platform_migration_tf_start
    - xdr_whitelist_activation_status
    - xdr_whitelist_activation_status
    - xdr_wildfire_submit_url_req_counter_total
    - xdr_wildfire_submit_url_res_status_total
    - xdr_wlm_count_by_status
    - xdr_wlm_count_table_rows
    - xdr_wlm_oldest_task.+
    - xdr_wlm_pending_tasks.+
    - xdr_wlm_task_delay.+
    - xdr_wlm_task_received_total
    - xdr_wlm_task_started_total
    - xdr_xdr_license_count
    - xdr_xdr_license_expiration
    - xdr_xpanse_alerts_resolver_queue
    - xdr_xpanse_incident_context_injection_failure_total
    - xdr_xsiam_gb_license_count
    - xdr_xsiam_users_license_count
    - xpanse_alert_fetcher.+
    - xpanse_asset_tag_rules_error_total
    - xpanse_compliance_frameworks.+
    - xpanse_data_migration.+
    - xpanse_explorer_ratings_cache_error_total
    - xpanse_global_lookup_request_time_count
    - xpanse_global_lookup_request_time_sum
    - xpanse_global_lookup_request_errors_total
    - xpanse_incident_context_injection_succeed_total
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts
    - xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts
    - XPANSE_MANUAL_SCAN_REQUEST_FAILED_COUNTER_total
    - XPANSE_MANUAL_SCAN_REQUESTED_COUNT_total
    - XPANSE_WEBSITES_DO_NOT_EXIST_ERROR_total
    - XPANSE_WEBSITE_FAILED_LOADING_ITEM_ERROR_total
    - XPANSE_WEBSITE_DETAILS_UNEXPECTED_RESULT_total
    - xpanse_policies.+
    - xpanse_policy.+
    - xpanse_rcs_result_processor_results_processed_count_total
    - xpanse_score_recalculation.+
    - xpanse_scoring_context.+
    - xpanse_tags_to_mysql_sync_error_total
    - xpanse_technology_metadata.+
    - xpanse_threat_events.+
    - xpanse_websites.+
    - xpanse_widget.+
    - xql_controller_element_duration_seconds
    - xql_controller_element_duration_seconds_count
    - xql_controller_element_duration_seconds_sum
    - xql_controller_element_status
    - xql_dataset_sbac_validation_errors_total
    - xql_email_errors_total
    - xql_engine_dataset_columns_count
    - xql_engine_fetch_duration_count
    - xql_engine_fetch_duration_sum
    - xql_engine_insert_rows_sqlite_seconds_count
    - xql_engine_insert_rows_sqlite_seconds_sum
    - xql_engine_output_counter
    - xql_engine_query_duration_count
    - xql_engine_query_duration_sum
    - xql_engine_send_detect_counter
    - xql_engine_xdm_persistent_error_notify_pz_failed_gauge
    - xql_fusion_eal_read_count
    - xql_fusion_eal_write_count
    - xql_fusion_ingested_events
    - xql_inflight_api_errors_total
    - xql_inflight_api_response_seconds_count
    - xql_inflight_api_response_seconds_sum
    - xql_inflight_cancel_api_response_seconds_bucket
    - xql_ingestion_gnz_xql_bytes_dropped_bucket
    - xql_ingestion_gnz_xql_bytes_dropped_count
    - xql_ingestion_gnz_xql_bytes_ingested_bucket
    - xql_ingestion_gnz_xql_bytes_ingested_count
    - xql_ingestion_logs_count
    - xql_ingestion_raw_size_bytes
    - xql_logs_iterator_parser_panics_total
    - xql_non_archived_queries_gauge
    - xql_query_center_migration_bigquery_count
    - xql_query_center_migration_errors_failed_counter_total
    - xql_query_center_migration_mysql_count
    - xql_query_center_migration_status
    - xql_xql_enrichment_enrichment_cache_hit
    - xql_xql_enrichment_enrichment_cache_miss
    - xspm_bigquery_query_duration_seconds_count
    - xspm_bigquery_query_duration_seconds_sum
    - xspm_pubsub_publish_duration_seconds_count
    - xspm_pubsub_publish_duration_seconds_sum
    - xspm_rules_processing_duration_seconds_count
    - xspm_rules_processing_duration_seconds_sum
    - xspm_system_filesystem_utilization_ratio
    - xspm_threadpool_active_count
    - xspm_threadpool_max_pool_size
    - xsoar_archive_error
    - xsoar_silent_playbooks_counter
    - xsoar_auto_extract_enrich_all_indicators_bucket
    - xsoar_auto_extract_enrich_indicator_command_bucket
    - xsoar_auto_extract_entry_processing_bucket
    - xsoar_auto_extract_find_indicator_bucket
    - xsoar_auto_extract_format_indicator_bucket
    - xsoar_auto_extract_indicator_enriched_command
    - xsoar_auto_extract_indicator_enrichment_timeout
    - xsoar_auto_extract_indicator_extracted
    - xsoar_auto_extract_indicator_formatted
    - xsoar_auto_extract_indicator_mapped
    - xsoar_auto_extract_map_indicator_bucket
    - xsoar_automation_executions_avg_duration_seconds
    - xsoar_automation_executions_count
    - xsoar_command_executions_avg_duration_seconds
    - xsoar_command_executions_count
    - xsoar_content_backup_snapshot_status
    - xsoar_engine_disconnections
    - xsoar_engine_health_status
    - xsoar_engine_last_health_timestamp
    - xsoar_fetch_execution_fetch_duration_seconds
    - xsoar_fetch_execution_health_status
    - xsoar_fetch_execution_ingestion_duration_seconds
    - xsoar_fetch_execution_pulled_incidents_count
    - xsoar_fetch_execution_pulled_indicators_count
    - xsoar_ha_lock_hijacks_total
    - xsoar_ha_mirroring_error
    - xsoar_ha_secondary_pb_executions_total
    - xsoar_migration_status
    - xsoar_msg_bus_publisher
    - xsoar_msg_bus_messages
    - xsoar_msg_bus_total_active
    - xsoar_msg_bus_oldest
    - xsoar_msg_bus_oldest_nth
    - xsoar_msg_bus_oldest_unhandled_nth
    - xsoar_msg_bus_total
    - xsoar_msg_bus_max_hpa
    - xsoar_msg_bus_min_hpa
    - xsoar_msg_bus_replicas
    - xsoar_msg_bus_reserved_replicas
    - xsoar_msg_bus_subscriber_pull
    - xsoar_msg_bus_subscriber_ack
    - xsoar_msg_bus_subscriber_nack
    - xsoar_msg_bus_dlq_subscriber_pull
    - xsoar_msg_bus_lock_latency_bucket
    - xsoar_msg_bus_insert_latency_bucket
    - xsoar_msg_bus_subscriber_pull_latency_bucket
    - xsoar_msg_bus_subscriber_ack_latency_bucket
    - xsoar_msg_bus_subscriber_nack_latency_bucket
    - xsoar_msg_bus_fetch_metrics_latency_bucket
    - xsoar_msg_bus_oldest_unhandled
    - xsoar_num_archived
    - xsoar_num_data_loss_alert
    - xsoar_num_integrations_updated
    - xsoar_num_of_current_integrations_installed
    - xsoar_num_oversized_alert
    - xsoar_num_panics
    - xsoar_num_sql_errors
    - xsoar_num_un_archived
    - xsoar_num_warroom_errors
    - xsoar_reminder_queue_push_error
    - xsoar_reminder_queue_counter
    - xsoar_reminder_queue_timed_out_entries
    - xsoar_stuck_inv_playbook_detected_total
    - xsoar_un_archive_error
    - xsoar_completed_inv_playbook_total
    - xsoar_inv_playbook_ack_nack_messages_total
    - workflow_failed
    - workflow_success
    - workflow_timeout
    - persistence_requests
    - logback_events_total
    - mongo_polaris_inserts_total
    - controller_runtime_reconcile_panics_total
    - controller_runtime_reconcile_errors_total
    - controller_runtime_terminal_reconcile_errors_total
    - qr_code_image_reader_calls_total
    - qr_code_image_cache_hits_total
    - qr_code_image_cache_size
    - qr_code_image_reader_call_duration_sum
    - qr_code_image_reader_call_duration_count
    - qr_code_image_reader_call_duration_bucket
  metro-resident-regex:
    - active_cloud_connectors
    - active_cloud_outposts
    - agent_mgmt_processor_pubsub_consumer_errors_total
    - agent_mgmt_processor_pubsub_consumer_inflight_messages
    - agent_mgmt_processor_pubsub_consumer_latency_seconds
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_count
    - agent_mgmt_processor_pubsub_consumer_latency_seconds_sum
    - agent_mgmt_processor_pubsub_consumer_messages_total
    - agent_to_group_calculation_count
    - agent_to_group_calculation_duration
    - alert_monitoring_metrics_bigquery_alert_status_counts
    - alert_monitoring_metrics_mysql_alert_status_counts
    - alert_monitoring_metrics_close_incident_with_open_alert_counts
    - alert_monitoring_metrics_open_alerts_per_incident_counts
    - alert_monitoring_metrics_alert_internal_ids_per_external_id
    - alert_monitoring_metrics_mysql_incident_status_counts
    - alert_monitoring_metrics_bigquery_incident_status_counts
    - alert_monitoring_metrics_duplicate_alert_counts
    - alert_monitoring_metrics_incident_alert_count_mismatch
    - alert_monitoring_metrics_incident_with_no_alert_counts
    - alert_monitoring_metrics_open_alert_older_than_10_days_counts
    - alyx_prefix_cache
    - alyx_rdb_block_cache_stats
    - alyx_rdb_compaction_time_micros
    - alyx_rdb_compression_time
    - alyx_rdb_full_bloom_filter_ratio
    - alyx_rdb_non_last_level_seek
    - alyx_rdb_op_bytes
    - alyx_rdb_operation_latency_micros
    - alyx_rdb_prefix_bloom_filter_ratio
    - alyx_rdb_write_stall_micros
    - alyx_request_duration_seconds_bucket
    - alyx_request_duration_seconds_count
    - alyx_server_connect_events_count
    - alyx_server_connection_failures
    - alyx_server_connections_total
    - alyx_server_request_failures
    - alyx_client_latency_seconds_bucket
    - alyx_client_latency_seconds_count
    - alyx_client_requests_failed_total
    - alyx_client_requests_total
    - alyx_dao_eal_num_of_items_total
    - alyx_dao_eal_operation_latency_seconds_total
    - alyx_dao_eal_requests_count_total
    - alyx_dss_ad_fetch_batch_size_bucket
    - alyx_dss_ad_fetch_duration_seconds_bucket
    - analytics__count_events_that_were_fetched_from_bq_total
    - analytics__count_events_that_were_fetched_from_gcs_total
    - analytics__tenant_have_reached_enrichment_limit
    - analytics_active_hosts
    - analytics_content_delta_between_requested_sync_to_sync_finished
    - analytics_content_delta_time_from_insertion
    - analytics_content_loader_data_loader_provider_data_updater_last_update_time
    - analytics_content_loader_data_loader_provider_last_update_time
    - analytics_content_loader_data_updater_failed_update_provider
    - analytics_content_loader_data_updater_fatal_error
    - analytics_content_loader_data_updater_last_execution_time
    - analytics_content_loader_data_updater_provider_failed_update_entity
    - analytics_content_loader_data_updater_provider_last_update_time
    - analytics_content_loader_data_updater_provider_total_time
    - analytics_correlations_.+
    - analytics_cycle_completed_analytics_product_count
    - analytics_cycle_profile_time_spent
    - analytics_cycle_total_analytics_product_count
    - analytics_cycle_running
    - analytics_de_v2__batches_processed_wall_time_count
    - analytics_de_v2__batches_processed_wall_time_sum
    - analytics_de_v2__events_processed_count
    - analytics_de_v2__events_processed_per_type_sum
    - analytics_de_v2__events_processed_sum
    - analytics_de_v2_detection_component_wall_time_sum
    - analytics_de_v2_detection_processing_part_wall_time_sum
    - analytics_de_v2_internal_queue_size
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total
    - analytics_de_v2_profile_engine_api_keys_count_sum
    - analytics_de_v2_profile_engine_api_request_time_sum
    - analytics_de_v2_rocks_keys_count_count
    - analytics_de_v2_rocks_keys_count_sum
    - analytics_de_v2_rocks_request_time_count
    - analytics_de_v2_rocks_request_time_sum
    - analytics_de_v2_vectorized_matcher_compile_detectors_sum
    - analytics_de_v2_vectorized_matcher_layer_wall_time_sum
    - analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total
    - analytics_de_v2_vectorized_matcher_wall_time_sum
    - analytics_decay_time_per_staging_table_count
    - analytics_decay_time_per_staging_table_sum
    - analytics_decision_time_per_staging_table_count
    - analytics_decision_time_per_staging_table_sum
    - analytics_delta_time_from_last_successful_decider_calculation
    - analytics_detection_component_process_time_count
    - analytics_detection_component_process_time_sum
    - analytics_detection_emitted_alerts_count
    - analytics_detection_emitted_alerts_sum
    - analytics_detection_engine_consumer_nacks_total
    - analytics_detection_hit_publish_time_count
    - analytics_detection_hit_publish_time_sum
    - analytics_detection_num_of_analytics_product_access_total
    - analytics_detection_outer_udf_execution_time_count
    - analytics_detection_outer_udf_execution_time_sum
    - analytics_detection_profile_matcher_get_profile_from_db_time_count
    - analytics_detection_profile_matcher_get_profile_from_db_time_sum
    - analytics_detection_state_populator_ingestion_rows_total
    - analytics_detection_state_populator_ingestion_time_count
    - analytics_detection_state_populator_ingestion_time_sum
    - analytics_detection_udf_execution_time_count
    - analytics_detection_udf_execution_time_sum
    - analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time
    - analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time
    - analytics_dss_last_updated_on_last_change
    - analytics_dss_sync_times_difference
    - analytics_dynamic_profile_updater_time_count
    - analytics_dynamic_profile_updater_time_sum
    - analytics_enabled
    - analytics_flood_stuck_alerts_total
    - analytics_flood_tasks_total
    - analytics_global_flood_total
    - analytics_global_profiles_contribution_failure
    - analytics_installed_content_version
    - analytics_llm_max_daily_requests_reached
    - analytics_llm_requests_bucket
    - analytics_llm_requests_count
    - analytics_llm_requests_sum
    - analytics_llm_task_success_rate_total
    - analytics_llm_task_success_rate_created
    - analytics_local_flood_total
    - analytics_objects_table_last_creation_time
    - analytics_product_consecutive_stage_failures
    - analytics_product_last_successful_api_table_creation_timestamp
    - analytics_profile_.+
    - analytics_rocksdb_num_keys_query
    - analytics_rocksdb_num_requests
    - analytics_rocksdb_response_time_count
    - analytics_rocksdb_response_time_sum
    - analytics_rocksdb_backup_download_failed
    - analytics_rocksdb_compaction_tasks
    - analytics_role_calculation_delay
    - analytics_role_count
    - analytics_task_processor_parse_metablob_error_created
    - analytics_task_processor_parse_metablob_error_total
    - analytics_task_processor_process_message_time_count
    - app_hub_prometheus_ingester_edr_errors_total
    - app_hub_prometheus_ingester_xql_errors_total
    - application_hub_permanent_errors_total
    - application_hub_public_api_requests_total
    - application_hub_temporary_errors_total
    - asm_alerts_mitre_backfill_sync_error_total
    - asm_etl_complete
    - asm_etl_count
    - asm_etl_duration
    - asm_export_assets_gauge
    - asm_incidents_mitre_backfill_sync_error_total
    - asm_mitre_mappings_sync_error_total
    - asset_mgmt_assoc_engine_acquire_lock_count_total
    - asset_mgmt_assoc_engine_association_conflicts_count
    - asset_mgmt_assoc_engine_association_process_time_count
    - asset_mgmt_assoc_engine_association_process_time_sum
    - asset_mgmt_diff_maker_last_exec_time_sec
    - asset_mgmt_general_assets_count_per_cloud_provider
    - asset_mgmt_general_assets_count_per_source
    - asset_mgmt_general_total_assets_count
    - asset_mgmt_ingester_assets_processed_count_total
    - asset_mgmt_reducer_last_exec_time_sec
    - asset_mgmt_snapshot_mgr_acquire_lock_total
    - archive_storage_aggregator_aggregator_compression_rate
    - archive_storage_aggregator_aggregator_process_duration
    - archive_storage_aggregator_committed_object_size
    - archive_storage_aggregator_compression_job_status
    - archive_storage_aggregator_delete_objects_count
    - archive_storage_aggregator_parse_error_count
    - archive_storage_aggregator_process_object_duration_micro_seconds_bucket
    - archive_storage_aggregator_processed_bytes_total
    - archive_storage_aggregator_processed_objects_count
    - archive_storage_aggregator_raw_object_size
    - argo_workflows_.+
    - attack_path_rules
    - attack_path_start_total
    - attack_path_success_total
    - attack_path_verdicts
    - attack_path_failure
    - auto_suggest_failure_total
    - batch_scanner_assets
    - batch_scanner_assets_scanned_milliseconds_bucket
    - batch_scanner_assets_scanned_milliseconds_count
    - batch_scanner_assets_scanned_milliseconds_sum
    - batch_scanner_rules_processed_total
    - batch_scanner_verdict_generated
    - batch_scanner_scanlog_generated
    - batch_scanner_scanlog_export_timer_milliseconds_sum
    - batch_scanner_scanlog_page_exported_total
    - batch_scanner_scanlog_exported_size_kb_sum
    - bigquery_adapter_.+
    - cas_actions_handler_issues_upsert_total
    - cas_actions_handler_pr_action_duration_seconds_bucket
    - cas_actions_handler_pr_action_duration_seconds_sum
    - cas_actions_handler_pr_action_duration_seconds_count
    - cas_actions_handler_pr_action_error_total
    - cas_actions_handler_update_pr_status_total
    - cas_actions_handler_generate_issues_duration_seconds_bucket
    - cas_actions_handler_generate_issues_duration_seconds_sum
    - cas_actions_handler_generate_issues_duration_seconds_total
    - cas_actions_handler_generate_issues_error_total
    - cas_actions_handler_watchdog_took_action_total
    - cas_applications_job_application_functions_seconds_bucket
    - cas_applications_job_application_functions_seconds_count
    - cas_applications_job_application_functions_seconds_sum
    - cas_dashboards_api_service_duration_seconds_bucket
    - cas_dashboards_api_service_duration_seconds_count
    - cas_dashboards_api_service_duration_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_count
    - cas_persistence_enrichment_flow_event_handling_cli_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_cli_error_total
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_count
    - cas_persistence_enrichment_flow_event_handling_periodic_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_periodic_error_total
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_bucket
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_count
    - cas_persistence_enrichment_flow_event_handling_pr_seconds_sum
    - cas_persistence_enrichment_flow_event_handling_pr_error_total
    - cas_persistence_policies_engine_unsupported_operator_total
    - cas_product_analytics_send_to_cortex_seconds_bucket
    - cas_product_analytics_send_to_cortex_seconds_count
    - cas_product_analytics_send_to_cortex_seconds_sum
    - cas_persistence_app_genesis_errors_total
    - cas_persistence_app_lens_errors_total
    - cas_persistence_app_stream_errors_total
    - cas_persistence_app_code_errors_total
    - cas_persistence_scai_errors_total
    - cas_persistence_scan_ops_errors_total
    - cas_persistence_core_errors_total
    - cas_persistence_flow_errors_total
    - cas_persistence_issues_errors_total
    - cas_persistence_lilo_errors_total
    - cas_persistence_stitch_errors_total
    - cas_persistence_manager_persistence_manager_event_handling_seconds_bucket
    - cas_persistence_manager_persistence_manager_event_handling_seconds_count
    - cas_persistence_manager_persistence_manager_event_handling_seconds_sum
    - cas_scans_management_scan_duration_seconds_bucket
    - cas_scans_management_scan_duration_seconds_count
    - cas_scans_management_scan_duration_seconds_sum
    - manager_persistence_manager_event_handling_error_total
    - cas_http_request_duration_seconds_bucket
    - cas_http_request_duration_seconds_count
    - cas_http_request_duration_seconds_sum
    - cas_http_request_size_bytes_count
    - cas_http_request_size_bytes_sum
    - cas_http_response_size_bytes_count
    - cas_http_response_size_bytes_sum
    - cas_job_duration_seconds_bucket
    - cas_job_duration_seconds_count
    - cas_job_duration_seconds_sum
    - cas_job_artifacts_size_bytes_count
    - cas_job_artifacts_size_bytes_sum
    - cas_actions_handler_pr_scan_until_status_duration_seconds_bucket
    - cas_actions_handler_pr_scan_until_status_duration_seconds_count
    - cas_actions_handler_pr_scan_until_status_duration_seconds_sum
    - cas_unified_cli_command_count_total
    - cas_unified_cli_os_total
    - cas_unified_cli_scan_result_total
    - cas_policies_api_policy_created_total
    - cloud_connectors_templates_created_total
    - cloud_onboarding_errors_total
    - cloud_outposts_templates_created_total
    - cloud_assets_collection_csp_api_request_duration_seconds_bucket
    - cloud_assets_collection_csp_api_request_duration_seconds_count
    - cloud_assets_collection_csp_api_request_duration_seconds_sum
    - cloud_assets_collection_num_failed_tasks_total
    - cloud_assets_collection_num_successful_tasks_total
    - cloud_assets_collection_num_content_archive_files_download_failures_total
    - cloud_assets_collection_num_rit_files_yaml_parsing_failure_total
    - cloud_assets_collection_num_rits_content_version_failures_total
    - cloud_assets_collection_num_wrong_format_content_files_total
    - cloud_assets_collection_message_processing_duration_seconds_bucket
    - cloud_assets_collection_message_processing_duration_seconds_count
    - cloud_assets_collection_message_processing_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum
    - cloud_assets_collection_platform_api_request_duration_seconds_bucket
    - cloud_assets_collection_platform_api_request_duration_seconds_count
    - cloud_assets_collection_platform_api_request_duration_seconds_sum
    - cloud_assets_collection_rit_files_duplications
    - cc_cache_update_key_failure_total
    - cc_cache_update_time_seconds_bucket
    - cc_cache_update_time_seconds_count
    - cc_cache_update_time_seconds_sum
    - classification_mgmt_bigquery_write_error_total
    - classification_mgmt_pubsub_publish_error_total
    - classification_mgmt_content_delivery_error_total
    - classification_mgmt_dashboard_error_total
    - classification_mgmt_dashboard_business_total
    - compliance_cleanup_results_catalog_mapping
    - compliance_cleanup_results_error_total
    - compliance_cleanup_results_records
    - compliance_cleanup_results_scores
    - compliance_cleanup_results_time_taken
    - compliance_cleanup_scan_logs
    - compliance_cleanup_scan_logs_error_total
    - compliance_cleanup_scan_logs_time_taken
    - cns_asset_conversion_errors_total
    - cns_engine_errors_total
    - cns_invalid_policies
    - cns_invalid_rules
    - cns_issues_emitter_duration_seconds_count
    - cns_issues_emitter_errors_total
    - cns_issues_emitter_queue_size
    - cns_job_duration_seconds
    - cns_job_duration_seconds_sum
    - cns_job_init_failed
    - cns_num_assets
    - cns_num_policies
    - cns_num_rules
    - cns_policy_issues_generated
    - cns_rule_duration_seconds
    - cns_rule_findings_generated
    - cold_storage_aggregator_.+.
    - cold_storage_datasets_aggregator_aggregator_compression_rate
    - cold_storage_datasets_aggregator_aggregator_process_duration
    - cold_storage_datasets_aggregator_committed_object_size
    - cold_storage_datasets_aggregator_compression_job_status
    - cold_storage_datasets_aggregator_dataset_errors_count
    - cold_storage_datasets_aggregator_delete_objects_count
    - cold_storage_datasets_aggregator_processed_bytes_total
    - cold_storage_datasets_aggregator_processed_objects_count
    - cold_storage_datasets_aggregator_raw_object_size
    - cold_storage_datasets_aggregator_spawned_aggregators_count
    - cold_tables_sync_job_failure_total
    - contextual_search_graph_neo4j_query_execution_time_millis_bucket
    - contextual_search_graph_neo4j_query_execution_time_millis_count
    - contextual_search_graph_neo4j_query_execution_time_millis_created
    - contextual_search_graph_neo4j_query_execution_time_millis_sum
    - cortex_gw_messages_processor_.+
    - cortex_cdl_to_clcs_migration_failed_total
    - cortex_cdl_to_clcs_migration_succeeded_total
    - cortex_platform_http_request_duration_highr_seconds_count
    - cortex_platform_http_request_duration_highr_seconds_sum
    - cortex_platform_http_request_duration_seconds_count
    - cortex_platform_http_request_duration_seconds_sum
    - cortex_platform_http_request_size_bytes_count
    - cortex_platform_http_request_size_bytes_sum
    - cortex_platform_http_requests_total
    - cortex_platform_http_response_size_bytes_count
    - cortex_platform_http_response_size_bytes_sum
    - cronus_active_connections_total
    - cronus_client_client_roundtrip_latency_sec_bucket
    - cronus_client_connection_wait_duration_seconds_bucket
    - cronus_client_rate_limited_requests_total
    - cronus_client_requests_total
    - cronus_dao_inserts_total
    - cronus_db_repair_dropped_entries_total
    - cronus_handler_wait_time_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_count
    - cronus_hotkeys_count_delay_duration_seconds_sum
    - cronus_hotkeys_count_duration_seconds_bucket
    - cronus_hotkeys_count_duration_seconds_count
    - cronus_hotkeys_count_duration_seconds_sum
    - cronus_hotkeys_count_in_time_window
    - cronus_hotkeys_events_count_in_time_window
    - cronus_hotkeys_threshold_in_time_window
    - cronus_last_processed_index
    - cronus_log_cache_get_ops
    - cronus_operator_cluster_hotkeys_count_in_time_window
    - cronus_operator_cluster_hotkeys_events_count_in_time_window
    - cronus_rebalance_download_bytes_total
    - cronus_rebalance_read_bytes_total
    - cronus_rebalance_upload_bytes_total
    - cronus_request_duration_seconds_bucket
    - cronus_request_process_duration_seconds_bucket
    - cronus_requests_queue_stream_feed_wait_duration_seconds_bucket
    - cronus_requests_total
    - cronus_rows_index_latency_seconds_bucket
    - cronus_storage_latency_seconds_bucket
    - cronus_throttled_write_requests_total
    - cronus_tree_index_compaction_duration_seconds_bucket
    - cs_migration_result_ratio
    - cs_migration_errors_total
    - custom_rules_counter_total
    - dashboard_api_4xx_failure_total
    - dashboard_api_5xx_failure_total
    - dashboard_engine_.+
    - data_ingestion_health_ingestion_alerts_total
    - dbre_backup_succeeded
    - neo4j_dbms_page_cache_hit_ratio
    - neo4j_dbms_vm_heap_used
    - neo4j_dbms_vm_gc_time_g1_old_generation_total
    - neo4j_dbms_vm_gc_time_g1_young_generation_total
    - dml_.+
    - dms_.+
    - dms_controller_element_duration_seconds
    - dp_asset_associations_pipeline_association_big_query_errors
    - dp_asset_associations_pipeline_association_public_ip_filter
    - dp_asset_associations_pipeline_association_stats
    - dp_asset_associations_pipeline_assets_errors
    - dp_asset_associations_pipeline_assets_total
    - dp_asset_associations_wlm_low_fidelity_asset_cleanup_total
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_associations_pipeline_assets_batch_size_bucket
    - dp_asset_associations_pipeline_assets_batch_size_count
    - dp_asset_associations_pipeline_assets_batch_size_sum
    - dp_asset_associations_pipeline_association_big_query_result_size_bucket
    - dp_asset_associations_pipeline_association_big_query_result_size_count
    - dp_asset_associations_pipeline_association_big_query_result_size_sum
    - dp_asset_associations_pipeline_metablob_errors
    - dp_asset_associations_pipeline_ratelimit_errors
    - dp_asset_pipeline_assets_errors
    - dp_asset_pipeline_assets_total
    - dp_asset_pipeline_metablob_errors
    - dp_asset_pipeline_performance_bucket
    - dp_asset_pipeline_performance_count
    - dp_asset_pipeline_performance_sum
    - dp_asset_pipeline_rows_in_metablobs_bucket
    - dp_asset_pipeline_rows_in_metablobs_count
    - dp_asset_pipeline_rows_in_metablobs_sum
    - dp_asset_pipeline_skip_get_deleted_assets
    - dp_asset_pipeline_operation_total
    - dspm_dt_adc_data_writer_success_total
    - dspm_dt_adc_data_writer_total_written_total
    - dspm_dt_bigquery_job_affected_rows_total
    - dspm_dt_bigquery_job_duration_seconds_count
    - dspm_dt_bigquery_job_duration_seconds_max
    - dspm_dt_bigquery_job_duration_seconds_sum
    - dspm_dt_bigquery_job_processed_bytes_total
    - dspm_dt_bigquery_job_processed_partitions_total
    - dspm_dt_bigquery_job_slot_millis_total
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_post_processing_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_post_processing_listener_message_successes_total
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_file_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_file_analysis_listener_message_successes_total
    - dspm_dt_fda_files_processed_total
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_column_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_column_analysis_listener_message_successes_total
    - dspm_dt_fda_columns_processed_total
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_analysis_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_analysis_listener_message_successes_total
    - dspm_dt_fda_assets_processed_total
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_count
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_sum
    - dspm_dt_fda_asset_inventory_listener_message_duration_seconds_max
    - dspm_dt_fda_asset_inventory_listener_message_failures_total
    - dspm_dt_fda_asset_inventory_listener_message_successes_total
    - dspm_dt_fda_data_writer_success_total
    - dspm_dt_fda_data_writer_total_written_total
    - dspm_dt_fda_replication_oldest_time_seconds
    - dspm_dt_mac_assets_publish_total
    - dspm_dt_mac_discovery_delete_total
    - dspm_dt_mac_discovery_publish_total
    - dspm_dt_mac_query_execution_duration_seconds_count
    - dspm_dt_mac_query_execution_duration_seconds_max
    - dspm_dt_mac_query_execution_duration_seconds_sum
    - dspm_dt_mac_query_execution_failure_count_total
    - dspm_dt_mac_query_execution_success_count_total
    - dspm_d9_crespo_classification_failed_total
    - dspm_d9_crespo_classification_succeeded_total
    - dspm_d9_crespo_preconditions_failed_total
    - dspm_d9_scar_classification_files_failure_total
    - dspm_d9_scar_classification_files_success_total
    - dspm_d9_scar_file_classification_average_duration_total
    - dss_config_dss_id
    - dss_enabled
    - dss_last_updated_on_last_change
    - dss_sync_running
    - dss_sync_time
    - dss_sync_times_difference
    - edr_.+
    - edr_controller_element_duration_seconds
    - effective_ip_range_monitoring_overlapping_rules
    - egress_aggregator_aggregator_compression_rate
    - egress_aggregator_committed_object_size
    - egress_aggregator_compression_job_status
    - egress_aggregator_delete_objects_count
    - egress_aggregator_processed_bytes_total
    - egress_aggregator_processed_objects_count
    - egress_aggregator_raw_object_size
    - egress_aggregator_spawned_aggregators_count
    - emailsec_alerts_backup_duration_seconds_bucket
    - emailsec_alerts_backup_duration_seconds_count
    - emailsec_alerts_backup_duration_seconds_sum
    - emailsec_alerts_bigquery_operation_duration_seconds_bucket
    - emailsec_alerts_bigquery_operation_duration_seconds_count
    - emailsec_alerts_bigquery_operation_duration_seconds_sum
    - emailsec_alerts_email_notification_received_to_issue_latency_bucket
    - emailsec_alerts_email_notification_received_to_issue_latency_count
    - emailsec_alerts_email_notification_received_to_issue_latency_sum
    - emailsec_alerts_email_post_collection_to_issue_latency_bucket
    - emailsec_alerts_email_post_collection_to_issue_latency_count
    - emailsec_alerts_email_post_collection_to_issue_latency_sum
    - emailsec_alerts_email_to_issue_latency_bucket
    - emailsec_alerts_email_to_issue_latency_count
    - emailsec_alerts_email_to_issue_latency_sum
    - emailsec_alerts_entities_backed_up_total
    - emailsec_alerts_external_call_duration_seconds_bucket
    - emailsec_alerts_external_call_duration_seconds_count
    - emailsec_alerts_external_call_duration_seconds_sum
    - emailsec_alerts_messages_processed
    - emailsec_alerts_messages_received_total
    - emailsec_alerts_missing_data_fetches_total
    - emailsec_alerts_policy_messages_published_total
    - emailsec_alerts_processing_errors_total
    - emailsec_alerts_processing_to_issue_latency_bucket
    - emailsec_alerts_processing_to_issue_latency_count
    - emailsec_alerts_processing_to_issue_latency_sum
    - emailsec_alerts_published_total
    - emailsec_alerts_rules_hits_total
    - emailsec_alerts_with_matching_rule
    - emailsec_bq_streamer_batches_total
    - emailsec_bq_streamer_batch_size
    - emailsec_bq_streamer_rows_total
    - emailsec_dispatcher_batch_processing_duration_seconds_bucket
    - emailsec_dispatcher_batch_processing_duration_seconds_count
    - emailsec_dispatcher_batch_processing_duration_seconds_sum
    - emailsec_dispatcher_consumers_active
    - emailsec_dispatcher_items_processed_total
    - emailsec_dispatcher_messages_nacked_total
    - emailsec_dispatcher_messages_received_total
    - emailsec_dispatcher_preprocessing_errors_total
    - emailsec_distrosync_controller_broadcasts_total
    - emailsec_distrosync_controller_syncs_total
    - emailsec_distrosync_controller_sync_duration_seconds_bucket
    - emailsec_distrosync_controller_sync_duration_seconds_count
    - emailsec_distrosync_controller_sync_duration_seconds_sum
    - emailsec_distrosync_subscriber_data_age_seconds
    - emailsec_distrosync_subscriber_notifications_received_total
    - emailsec_distrosync_subscriber_reloads_total
    - emailsec_pq_operation_duration_seconds_bucket
    - emailsec_pq_operation_duration_seconds_count
    - emailsec_pq_operation_duration_seconds_sum
    - emailsec_pq_queue_size
    - emailsec_response_batch_duration_seconds_bucket
    - emailsec_response_batch_duration_seconds_count
    - emailsec_response_batch_duration_seconds_sum
    - emailsec_response_duration_seconds_bucket
    - emailsec_response_duration_seconds_count
    - emailsec_response_duration_seconds_sum
    - emailsec_response_messages_received_total
    - emailsec_response_retries_scheduled_total
    - emailsec_retry_fetched_batch_size_bucket
    - emailsec_retry_fetched_batch_size_count
    - emailsec_retry_fetched_batch_size_sum
    - emailsec_retry_item_processing_duration_seconds_bucket
    - emailsec_retry_item_processing_duration_seconds_count
    - emailsec_retry_item_processing_duration_seconds_sum
    - emailsec_retry_processing_lag_seconds_bucket
    - emailsec_retry_processing_lag_seconds_count
    - emailsec_retry_processing_lag_seconds_sum
    - email_attachment_missing_file
    - email_attachment_pending_decrypt
    - email_attachment_pending_wf_submit
    - email_attachment_raised_alert
    - email_attachment_submitted_to_wf
    - email_attachment_unsupported_file_type
    - email_relay_attachment_submitted_to_wf_total
    - email_relay_attachment_verdict_total
    - email_relay_attachments_total
    - email_relay_gcs_latency_milliseconds_count
    - email_relay_gcs_latency_milliseconds_sum
    - email_relay_steps_latency_milliseconds_count
    - email_relay_steps_latency_milliseconds_sum
    - email_relay_wildfire_error_codes_total
    - email_relay_wildfire_latency_milliseconds_count
    - email_relay_wildfire_latency_milliseconds_sum
    - email_relay_wildfire_unsupported_files_total
    - email_relay_emails_total
    - email_relay_alerts_total
    - email_relay_pubsub_total
    - email_relay_deleted_attachments_total
    - email_relay_attachments_over_limit_total
    - email_relay_scheduler_task_executions_total
    - emailsec_is_email_module_enabled
    - em_integration_asset_processor_errors_total
    - ext_controller_element_duration_seconds
    - ext_controller_element_duration_seconds_count
    - ext_controller_element_duration_seconds_sum
    - ext_controller_element_status
    - failed_requests_total
    - finding_pubsub_message_histogram_bucket
    - finding_pubsub_message_histogram_count
    - finding_pubsub_message_histogram_sum
    - findings_table_fetching_time_seconds_bucket
    - findings_table_fetching_time_seconds_created
    - findings_table_fetching_time_seconds_sum
    - finding_operation_total
    - finding_metablob_performance_bucket
    - finding_metablob_performance_count
    - finding_metablob_performance_sum
    - xdr_forensics_sams
    - xdr_forensics_hunt_lag_hours
    - gcs_notification_failed_subscriptions
    - GnzEdrPipeline_.+
    - GnzGlobal_DynamicConfig_get_config_failures_total
    - GnzGlobal_pithos_active_streams
    - GnzGlobal_pithos_aggregated_bytes_total
    - GnzGlobal_pithos_aggregated_objects_total
    - GnzGlobal_pithos_aggregation_duration_seconds
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_count
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_sum
    - GnzGlobal_pithos_committed_objects_total
    - GnzGlobal_pithos_dataset_aggregators
    - GnzGlobal_pithos_streamed_bytes_total
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket
    - GnzMbIngester_.+
    - GnzStoryBuilder_.+
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - gonzo_server_.+
    - hpl_json_.+
    - http_requests_total
    - http_server_requests_seconds_count
    - http_server_duration_milliseconds_bucket
    - http_server_duration_milliseconds_count
    - http_server_duration_milliseconds_sum
    - http_server_request_duration_seconds_bucket
    - http_server_request_duration_seconds_count
    - http_server_request_duration_seconds_sum
    - http_server_response_size_bytes_sum
    - http_server_response_size_bytes_count
    - ingestion_quota_exceeded
    - inline_scanner_asset_change_event_failed_total
    - inline_scanner_asset_change_event_ignored_total
    - inline_scanner_asset_change_event_received_total
    - inline_scanner_asset_change_event_replayed_total
    - inline_scanner_assets_finding
    - inline_scanner_assets_scanned_total
    - inline_scanner_cloud_account_fetched
    - inline_scanner_findings_published_failed_total
    - inline_scanner_findings_published_replayed_total
    - inline_scanner_findings_published_success_total
    - inline_scanner_scanlog_generated
    - inline_scanner_scanlog_exported
    - inline_scanner_scanlog_export_timer_milliseconds_sum
    - inline_scanner_asset_change_scan_timer_milliseconds_sum
    - inline_scanner_scanlog_exported_size_kb_sum
    - known_attachment_malwares
    - master_tenant_listener_is_stale
    - memsql_counter_bytes_received
    - memsql_counter_bytes_sent
    - memsql_counter_connections
    - memsql_counter_failed_read_queries
    - memsql_counter_failed_write_queries
    - memsql_counter_successful_read_queries
    - memsql_counter_successful_write_queries
    - memsql_distributed_partitions_offline
    - memsql_distributed_partitions_online
    - memsql_distributed_partitions_total
    - memsql_status_aborted_connects
    - memsql_status_failed_read_queries
    - memsql_status_failed_write_queries
    - memsql_workload_management_total_queries_cancelled_since_startup
    - memsql_workload_management_total_queries_finished_since_startup
    - memsql_workload_management_total_queries_started_since_startup
    - metrics_aggregator_.+
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed_seconds
    - number_of_cloud_accounts
    - metrics_aggregator_last_processed_batch
    - migration_time_consumed
    - neo4j_.*bolt_connections.*
    - neo4j_.*bolt_messages_received.*
    - neo4j_.*bolt_messages_started.*
    - neo4j_dbms_pool_bolt_total_size
    - neo4j_dbms_pool_bolt_total_used
    - neo4j_dbms_pool_bolt_used_heap
    - neo4j_.*check_point.*
    - neo4j_.*cypher_replan_events.*
    - neo4j_.*cypher_cache.*
    - neo4j_.*pool_transaction.*_total_used
    - neo4j_.*pool_transaction.*_used_heap
    - neo4j_.*store_size.*
    - neo4j_.*transaction_active_read
    - neo4j_.*transaction_active_write
    - neo4j_.*transaction_committed.*
    - neo4j_.*transaction_peak_concurrent.*
    - neo4j_.*transaction_rollbacks.*
    - neo4j_.*page_cache_hit.*
    - neo4j_.*page_cache_page_faults.*
    - neo4j_.*page_cache_usage_ratio
    - neo4j_.*vm_file_descriptors_count
    - neo4j_.*vm_gc_time.*
    - neo4j_.*vm_heap_used
    - netscan_authentication_success
    - netscan_avg_target_size
    - netscan_host_properties
    - netscan_hosts_currently_scanning
    - netscan_kms_error_total
    - netscan_processor_cns_host_dead_in_scan
    - netscan_processor_cns_host_max_tuple_batch_size
    - netscan_processor_cns_host_prior_scan_age_bucket
    - netscan_processor_cns_host_unresponsive
    - netscan_processor_dao_access_duration_seconds_bucket
    - netscan_processor_db_migration_error
    - netscan_processor_netscan_results_batch_process_bucket
    - netscan_processor_scan_result_batch_failure
    - netscan_processor_scan_result_batch_size_bucket
    - netscan_processor_scan_result_incompatible
    - netscan_processor_scan_result_match
    - netscan_processor_scan_result_message_count_bucket
    - netscan_processor_scan_result_new_cns_host
    - netscan_processor_scan_task_processed
    - netscan_processor_scan_result_unrelated_hostname
    - netscan_processor_scan_result_with
    - netscan_processor_uai_pubsub_output_error
    - netscan_scan_duration_count
    - netscan_scan_duration_created
    - netscan_scan_duration_sum
    - netscan_scan_timeouts
    - netscan_scans_configured
    - netscan_scans_in_flight
    - netscan_scans_scheduled
    - netscan_scans_status
    - netscan_test_result
    - notification_migration_errors_total
    - notification_migration_result_ratio
    - otelcol_.+
    - oversized_alert_trimming_count
    - oversized_alert_trimmed_field_bucket
    - partyzaurus_.+
    - partyzaurus_controller_element_duration_seconds
    - platform_compliance_active_assessment_profile_count
    - platform_compliance_api_requests_latency_seconds_bucket
    - platform_compliance_api_requests_latency_seconds_count
    - platform_compliance_api_requests_latency_seconds_sum
    - platform_compliance_api_requests_total
    - platform_compliance_calculation_last_run
    - platform_compliance_calculation_run_time
    - platform_compliance_content_hash
    - platform_compliance_controls_table_size
    - platform_compliance_custom_assessment_profile_count
    - platform_compliance_custom_controls_count
    - platform_compliance_custom_controls_not_attached_to_standards
    - platform_compliance_custom_controls_with_no_rules
    - platform_compliance_custom_standards_with_no_controls
    - platform_compliance_custom_standards_count
    - platform_compliance_oob_assessment_profile_count
    - platform_compliance_oob_controls_count
    - platform_compliance_oob_standards_count
    - platform_compliance_reports_downloaded_by_customers_created
    - platform_compliance_reports_downloaded_by_customers_total
    - platform_compliance_scan_log_count_in_last_24_hours
    - platform_compliance_scheduled_reports_count
    - platform_compliance_standards_table_size
    - platform_compliance_tenant
    - platform_compliance_used_assets_count
    - preprocessed_data_batcher_compression_job_status
    - preprocessed_data_batcher_processed_bytes_total
    - preprocessed_data_batcher_processed_objects_count
    - preprocessed_data_batcher_raw_object_size
    - preprocessed_data_batcher_spawned_aggregators_count
    - preprocessed_data_batcher_streaming_object_duration_seconds_bucket
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - prom_gonzo_storage_adapter_.+
    - pz_.+
    - pz_schema_manager_.+
    - policy_migration_result_ratio
    - policy_migration_errors_total
    - rule_migration_result
    - rule_migration_errors_total
    - xsoar_podman_cpu
    - xsoar_podman_memory
    - xsoar_podman_uptime
    - xsoar_rc.+
    - spanner_migration_execution_time
    - spanner_migration_rows_migrated
    - spanner_migration_status
    - spanner_migration_total_rows_to_migrate
    - spur_dms_actual_batch_size_bucket
    - spur_dms_actual_batch_size_count
    - spur_dms_actual_batch_size_sum
    - spur_dms_cache_size
    - spur_dms_fetch_duration_seconds_bucket
    - spur_dms_fetch_duration_seconds_count
    - spur_dms_fetch_duration_seconds_sum
    - spur_dms_fetch_errors_count
    - spur_dms_inactive_until_unix_milli
    - spur_dms_ip_parse_error
    - spur_dms_poll_failure_count
    - spur_dms_queried_count
    - spur_dms_request_count
    - spur_dms_skipped_ips_count
    - spur_dms_spur_generation_time_unix_milli
    - spur_pz_actual_batch_size_bucket
    - spur_pz_actual_batch_size_count
    - spur_pz_actual_batch_size_sum
    - spur_pz_cache_size
    - spur_pz_fetch_duration_seconds_bucket
    - spur_pz_fetch_duration_seconds_count
    - spur_pz_fetch_duration_seconds_sum
    - spur_pz_fetch_errors_count
    - spur_pz_inactive_until_unix_milli
    - spur_pz_ip_parse_error
    - spur_pz_poll_failure_count
    - spur_pz_queried_count
    - spur_pz_request_count
    - spur_pz_skipped_ips_count
    - spur_pz_spur_generation_time_unix_milli
    - standard_events_event_dropped_counter_created
    - standard_events_event_dropped_counter_total
    - standard_events_event_failed_sys_error_counter_created
    - standard_events_event_failed_sys_error_counter_total
    - standard_events_event_failed_validation_counter_created
    - standard_events_event_failed_validation_counter_total
    - standard_events_event_processed_success_counter_created
    - standard_events_event_processed_success_counter_total
    - standard_events_queue_size
    - storybuilder_.+
    - support_case_auto_generate_tsf_request_timeout_total
    - support_case_failed_auto_generate_tsf_request_total
    - support_case_failed_upload_files_total
    - support_case_number_of_support_case_successfully_created_total
    - support_case_number_of_support_case_failed_to_create_total
    - support_case_success_auto_generate_tsf_request_total
    - support_case_success_upload_files_total
    - storybuilder_filtered_events_total
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used
    - uai_account_offboarding_deleted_assets_count_total
    - uai_account_offboarding_error_count_total
    - uai_aggregated_assets_cdc_delay_seconds
    - uai_api_errors_count_total
    - uai_api_requests_count_total
    - uai_endpoints_health_check_failed
    - vectorized_matcher_current_content_version_exporting_time_in_seconds
    - vectorized_matcher_current_content_version_loading_time_in_seconds
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_bucket
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_count
    - verdict_manager_attackPathScanner_batch_reconcile_timer_milliseconds_sum
    - verdict_manager_batches_completed
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_bucket
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_count
    - verdict_manager_configScanner_batch_reconcile_timer_milliseconds_sum
    - verdict_manager_cspm_listener_ack_cnt
    - verdict_manager_findings_publish_failed
    - verdict_manager_findings_publish_success
    - verdict_manager_findings_created
    - verdict_manager_findings_closed
    - verdict_manager_issues_created
    - verdict_manager_issues_updated
    - verdict_manager_issues_closed
    - verdict_manager_issues_batch_publish_success
    - verdict_manager_issues_batch_publish_failed
    - verdict_manager_reconcile_failed
    - verdict_manager_reconcile_timer_milliseconds_bucket
    - verdict_manager_reconcile_timer_milliseconds_count
    - verdict_manager_reconcile_timer_milliseconds_sum
    - verdict_manager_reconcile_verdicts_cleanup
    - verdict_manager_verdicts_load_job
    - verdict_manager_xspm_listener_ack_cnt
    - verdict_manager_xspm_listener_timer_milliseconds_bucket
    - verdict_manager_xspm_listener_timer_milliseconds_count
    - verdict_manager_xspm_listener_timer_milliseconds_sum
    - verdict_manager_xspm_loader_timer_milliseconds_bucket
    - verdict_manager_xspm_loader_timer_milliseconds_count
    - verdict_manager_xspm_loader_timer_milliseconds_sum
    - verdict_manager_xspm_reconcile_timer_milliseconds_bucket
    - verdict_manager_xspm_reconcile_timer_milliseconds_count
    - verdict_manager_xspm_reconcile_timer_milliseconds_sum
    - verdict_manager_verdicts_fetched
    - vsg_reconcile_time_bucket
    - vsg_reconcile_time_count
    - vsg_reconcile_time_sum
    - vsg_oom_scaler_applied_memory_resources
    - vsg_oom_scaler_last_applied_timestamp
    - vsg_oom_scaler_last_reconcile_timestamp
    - vsg_oom_scaler_pod_request_memory_max
    - vsg_oom_scaler_reconcile_time_bucket
    - vsg_oom_scaler_reconcile_time_count
    - vsg_oom_scaler_reconcile_time_sum
    - vsg_oom_scaler_resolve_source_metric_bucket
    - vsg_oom_scaler_resolve_source_metric_count
    - vsg_oom_scaler_resolve_source_metric_sum
    - vsg_oom_scaler_target_memory_resources
    - vsg_oom_scaler_under_cooldown_timestamp
    - vsg_vertical_scaler_applied_cpu_resources
    - vsg_vertical_scaler_applied_memory_resources
    - vsg_vertical_scaler_current_step_thresholds
    - vsg_vertical_scaler_hpa_invalid_timestamp
    - vsg_vertical_scaler_last_applied_timestamp
    - vsg_vertical_scaler_last_reconcile_timestamp
    - vsg_vertical_scaler_reconcile_time_bucket
    - vsg_vertical_scaler_reconcile_time_count
    - vsg_vertical_scaler_reconcile_time_sum
    - vsg_vertical_scaler_resolve_source_metric_bucket
    - vsg_vertical_scaler_resolve_source_metric_count
    - vsg_vertical_scaler_resolve_source_metric_sum
    - vsg_vertical_scaler_scaled_replicas
    - vsg_vertical_scaler_source_metric_result
    - vsg_vertical_scaler_target_cpu_resources
    - vsg_vertical_scaler_target_memory_resources
    - vsg_vertical_scaler_target_replicas
    - vsg_vertical_scaler_under_cooldown_timestamp
    - vsg_zero_scaler_last_applied_timestamp
    - vsg_zero_scaler_last_reconcile_timestamp
    - vsg_zero_scaler_manually_downscaled_timestamp
    - vsg_zero_scaler_reconcile_time_bucket
    - vsg_zero_scaler_reconcile_time_count
    - vsg_zero_scaler_reconcile_time_sum
    - vsg_zero_scaler_resolve_source_metric_bucket
    - vsg_zero_scaler_resolve_source_metric_count
    - vsg_zero_scaler_resolve_source_metric_sum
    - vsg_zero_scaler_scaled_replicas
    - vsg_zero_scaler_source_metric_result
    - vsg_zero_scaler_target_replicas
    - vsg_zero_scaler_thresholds
    - vsg_pvc_scaler_reconcile_time_bucket
    - vsg_pvc_scaler_reconcile_time_count
    - vsg_pvc_scaler_reconcile_time_sum
    - vsg_pvc_scaler_last_reconcile_timestamp
    - vsg_pvc_scaler_last_applied_timestamp
    - vsg_pvc_scaler_resolve_source_metric_bucket
    - vsg_pvc_scaler_resolve_source_metric_count
    - vsg_pvc_scaler_resolve_source_metric_sum
    - vsg_pvc_scaler_source_metric_result
    - vsg_pvc_scaler_target_pvc_size
    - vsg_pvc_scaler_applied_pvc_size
    - vuln_action_plan_creation_failure_total
    - vuln_action_plan_creation_metrics
    - vuln_action_plan_recon_failure_total
    - vuln_action_plan_recon_metrics
    - vxp_bigquery_rows_read_total
    - vxp_bigquery_slots_used_total
    - vxp_dyanmic_config_values
    - vxp_http_server_requests_count
    - vxp_http_server_requests_sum
    - vxp_job_run_time_count
    - vxp_issues_published_total
    - vxp_job_run_time_sum
    - vxp_policy_sync_actions_total
    - vxp_policy_sync_findings_total
    - vxp_protofinding_query_time_count
    - vxp_protofinding_query_time_sum
    - vxp_pubsub_errors_total
    - vxp_pubsub_processing_latency_bucket
    - vxp_spanner_protofinding_query_time_count
    - vxp_spanner_protofinding_query_time_sum
    - vxp_redis_operations_count
    - vxp_redis_operations_sum
    - vxp_sync_cve_coverage_job_malformed_rows_total
    - wf_vs_get_verdicts_request_latency_seconds_bucket
    - wf_vs_get_verdicts_request_failed_total
    - wf_vs_get_verdicts_size
    - wf_vs_set_upload_request_failed
    - wf_vs_set_upload_request_success
    - wlm_monitoring_.+
    - xcloud_assets_count
    - xcloud_discovery_hours_delay
    - xcloud_inventory_strategies_count_by_provider_and_status
    - xcloud_onboarding_message
    - xcloud_strategies_delay_hours_by_service
    - xdm_.+
    - xdr_active_lightweight_agents_7d
    - xdr_addons_license_status
    - xdr_agent.+
    - xdr_alert_domain_migration_error
    - xdr_alert_source_delay_time
    - xdr_alert_sync_tags_databases_error_total
    - xdr_alerts_.+
    - xdr_alerts_fetcher.+
    - xdr_api_errors_total
    - xdr_api_waitress_allocated_threads
    - xdr_api_waitress_occupied_threads
    - xdr_api_waitress_queued_requests
    - xdr_asm_mega_join_cache_creation_error_total
    - xdr_asm_mega_join_cache_delay
    - xdr_asm_xpanse_replication_delay
    - xdr_asset_command_.+
    - xdr_asset_score_changes_received_created
    - xdr_asset_score_changes_received_total
    - xdr_asset_score_changes_sent_to_cie_created
    - xdr_asset_score_changes_sent_to_cie_total
    - xdr_bigquery_bytes_processed_count
    - xdr_bigquery_bytes_processed_sum
    - xdr_bigquery_execution_time_count
    - xdr_bigquery_execution_time_sum
    - xdr_bigquery_queue_time_count
    - xdr_bigquery_queue_time_sum
    - xdr_bigquery_zslots_count
    - xdr_bigquery_zslots_sum
    - xdr_biocs_assigned_to_profiles_count
    - xdr_bq_stats_not_delivered_from_pubsub_total
    - xdr_bq_stats_redis_list_length_maxed_total
    - xdr_broker_.+
    - xdr_calc_.+
    - xdr_card_.+
    - xdr_case_.+
    - xdr_cie_risk_score_errors_created
    - xdr_cie_risk_score_errors_total
    - xdr_calc_last_association_replication_time
    - xdr_clcs_counter
    - xdr_clcs_multi_csp_connected
    - xdr_clcs_multi_region_connected_count
    - xdr_clcs_multi_region_enabled
    - xdr_clcs_multi_salesforce_enabled
    - xdr_clcs_subscriptions_data
    - xdr_cloud_agents_count
    - xdr_cloud_license_count
    - xdr_cloud_license_purchased
    - xdr_cloud_pro_agents_count
    - xdr_cold_storage_oldest_raw_object
    - xdr_collection_.+
    - xdr_collector_.+
    - xdr_count_operational_status_and_os_type
    - xdr_count_operational_status_description_and_os_type
    - xdr_cts_active_sessions
    - xdr_cts_active_tokens
    - xdr_cts_cache_hits_total
    - xdr_cts_cache_miss_total
    - xdr_cts_fresh_token_total
    - xdr_cts_request_time_count
    - xdr_cts_request_time_sum
    - xdr_cts_request_total
    - xdr_cts_sts_error_total
    - xdr_cts_sts_unauthorized_total
    - xdr_cts_waitress_allocated_threads
    - xdr_cts_waitress_occupied_threads
    - xdr_cts_waitress_queued_requests
    - xdr_current_connected_agents
    - xdr_current_connected_edr_agents
    - xdr_data_usage_24_hours
    - xdr_device_control_license
    - xdr_dss_.*
    - xdr_dumpster_auto_upload_config
    - xdr_dumpster_auto_upload_dumps_count
    - xdr_edr_active_agents_24h
    - xdr_edr_agents_24_hours
    - xdr_edr_agents_count
    - xdr_edr_license_count
    - xdr_edr_license_expiration
    - xdr_edr_license_purchased
    - xdr_egress_oldest_raw_object
    - xdr_email_notification_received_to_issue_latency_bucket
    - xdr_email_notification_received_to_issue_latency_count
    - xdr_email_notification_received_to_issue_latency_sum
    - xdr_email_post_collection_to_issue_latency_bucket
    - xdr_email_post_collection_to_issue_latency_count
    - xdr_email_post_collection_to_issue_latency_sum
    - xdr_email_service.+
    - xdr_email_to_issue_latency_bucket
    - xdr_email_to_issue_latency_count
    - xdr_email_to_issue_latency_sum
    - xdr_epp_agents_count
    - xdr_epp_license_count
    - xdr_epp_license_expiration
    - xdr_epp_license_purchased
    - xdr_forensics_agents
    - xdr_forensics_licenses
    - xdr_forensics_tenant
    - xdr_get_unused_api_key_ids
    - xdr_generate_notification_error_total
    - xdr_gvs_.+
    - xdr_host_insights_is_enabled
    - xdr_hpa_xql_pubsub_metric
    - xdr_hpa_pipeline_pubsub_metric
    - xdr_hpa_dms_pubsub_metric
    - xdr_incident_alert_data_sync_error_total
    - xdr_incidents_by_status_avg
    - xdr_incidents_by_status_count
    - xdr_informative_btp_alerts
    - xdr_init_app_status
    - xdr_init_app_took
    - xdr_invalidate_user_role_failure
    - xdr_investigation_in_progress
    - xdr_ios_large_digest_report_total
    - xdr_issue_evidence_create_count_total
    - xdr_issue_evidence_papi_read_count_total
    - xdr_issue_evidence_read_count_total
    - xdr_issue_evidence_update_count_total
    - xdr_issue_evidence_validation_error_count_total
    - xdr_issue_enricher_resolution_action_.*
    - xdr_issue_fetcher_handle_message_time_count
    - xdr_issue_fetcher_handle_message_time_created
    - xdr_issue_fetcher_handle_message_time_sum
    - xdr_issue_fetcher_parse_time_count
    - xdr_issue_fetcher_parse_time_created
    - xdr_issue_fetcher_parse_time_sum
    - xdr_issue_handle_message_time_count
    - xdr_issue_handle_message_time_created
    - xdr_issue_handle_message_time_sum
    - xdr_issue_send_to_dlq_total
    - xdr_issue_rate_limit_total
    - xdr_issue_updater_handle_message_time_count
    - xdr_issue_updater_handle_message_time_sum
    - xdr_issue_updater_handle_message_time_created
    - xdr_issue_updater_optimistic_lock_total
    - xdr_kpi_.+
    - xdr_license_fetch_failures
    - xdr_logging_.*
    - xdr_mail_event_processor_.+
    - xdr_mailing_queue_count
    - xdr_managed_tenant_monitor_info
    - xdr_matching_service_detection_queue_depth
    - xdr_mdr_license
    - xdr_forensics_migration_status
    - xdr_mssp_license
    - xdr_mth_license
    - xdr_mysql_.*
    - xdr_nfr_license
    - xdr_notifcation_mail_queue_count
    - xdr_notification_dead_letter_queue_table_count
    - xdr_p2p_discovery_table_count
    - xdr_p2p_scanable_agents_count
    - xdr_phase1_installers_flag
    - xdr_platform_migration_completed
    - xdr_platform_migration_failed
    - xdr_platform_migration_start
    - xdr_platform_migration_status
    - xdr_platform_migration_time_risk
    - xdr_preprocessed_data_batcher_oldest_object
    - xdr_prisma_pairing_status
    - xdr_pz_schema_waitress_occupied_threads
    - xdr_pz_schema_is_dataset_max_keys_reached
    - xdr_pz_schema_bq_external_raw_table_column_count
    - xdr_queries_by_status_count
    - xdr_redis_.+
    - xdr_reports_generator_.+
    - xdr_request_processing_seconds_count
    - xdr_request_processing_seconds_sum
    - xdr_request_response_size_count
    - xdr_request_response_size_sum
    - xdr_retention_enforcement_status
    - xdr_retention_enforcement_task_run
    - xdr_retention_service_sync_retention_stats_failures_total
    - xdr_retention_simulation_status
    - xdr_retryable_grouping_alerts_.+
    - xdr_sbac_enabled
    - xdr_sbac_enabled.+
    - xdr_sbac_mode
    - xdr_schd_task_delay_histogram_bucket
    - xdr_scheduler_wlm_working
    - xdr_search_index.+
    - xdr_story_builder_cpu_utilization
    - xdr_story_builder_max_delay
    - xdr_scouter_to_group_calculation_count
    - xdr_scouter_to_group_calculation_duration
    - xdr_stuck_triage_records
    - xdr_tags_count
    - xdr_tags_sync_not_running
    - xdr_task_processor_process_message_time_count
    - xdr_task_processor_process_message_time_created
    - xdr_task_processor_process_message_time_sum
    - xdr_tenant_configuration_csp
    - xdr_tenant_configuration_subdomain
    - xdr_tenant_configuration_tenant_type
    - xdr_tenant_distribution_list
    - xdr_tenant_configuration_is_migrated_to_platform
    - xdr_tenant_configuration_is_platform
    - xdr_tim_.+
    - xdr_total_agents_by_content_status
    - xdr_total_size_of_stored_edr
    - xdr_unknown_email_issue_type_total
    - xdr_unused_profile_count
    - xdr_upload_management_audit_to_gcs
    - xdr_users_access_to_tenant_count
    - xdr_users_login_to_tenant
    - xdr_va_.+
    - xdr_vulnerability_assessment_.+
    - xdr_wec_.+
    - xdr_wildfire_submit_url_low_priority_req_counter_total
    - xdr_num_of_disabled_rules
    - xdr_num_of_partially_disabled_rules
    - xdr_num_of_enabled_rules
    - xdr_platform_migration_tf_completed
    - xdr_platform_migration_tf_start
    - xdr_whitelist_activation_status
    - xdr_whitelist_activation_status
    - xdr_wildfire_submit_url_req_counter_total
    - xdr_wildfire_submit_url_res_status_total
    - xdr_wlm_count_by_status
    - xdr_wlm_count_table_rows
    - xdr_wlm_oldest_task.+
    - xdr_wlm_pending_tasks.+
    - xdr_wlm_task_delay.+
    - xdr_wlm_task_received_total
    - xdr_wlm_task_started_total
    - xdr_xdr_license_count
    - xdr_xdr_license_expiration
    - xdr_xpanse_alerts_resolver_queue
    - xdr_xpanse_incident_context_injection_failure_total
    - xdr_xsiam_gb_license_count
    - xdr_xsiam_users_license_count
    - xpanse_alert_fetcher.+
    - xpanse_asset_tag_rules_error_total
    - xpanse_compliance_frameworks.+
    - xpanse_data_migration.+
    - xpanse_explorer_ratings_cache_error_total
    - xpanse_global_lookup_request_time_count
    - xpanse_global_lookup_request_time_sum
    - xpanse_global_lookup_request_errors_total
    - xpanse_incident_context_injection_succeed_total
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts
    - xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts
    - XPANSE_MANUAL_SCAN_REQUEST_FAILED_COUNTER_total
    - XPANSE_MANUAL_SCAN_REQUESTED_COUNT_total
    - XPANSE_WEBSITES_DO_NOT_EXIST_ERROR_total
    - XPANSE_WEBSITE_FAILED_LOADING_ITEM_ERROR_total
    - XPANSE_WEBSITE_DETAILS_UNEXPECTED_RESULT_total
    - xpanse_policies.+
    - xpanse_policy.+
    - xpanse_rcs_result_processor_results_processed_count_total
    - xpanse_score_recalculation.+
    - xpanse_scoring_context.+
    - xpanse_tags_to_mysql_sync_error_total
    - xpanse_technology_metadata.+
    - xpanse_threat_events.+
    - xpanse_websites.+
    - xpanse_widget.+
    - xql_controller_element_duration_seconds
    - xql_controller_element_duration_seconds_count
    - xql_controller_element_duration_seconds_sum
    - xql_controller_element_status
    - xql_dataset_sbac_validation_errors_total
    - xql_email_errors_total
    - xql_engine_dataset_columns_count
    - xql_engine_fetch_duration_count
    - xql_engine_fetch_duration_sum
    - xql_engine_insert_rows_sqlite_seconds_count
    - xql_engine_insert_rows_sqlite_seconds_sum
    - xql_engine_output_counter
    - xql_engine_query_duration_count
    - xql_engine_query_duration_sum
    - xql_engine_send_detect_counter
    - xql_engine_xdm_persistent_error_notify_pz_failed_gauge
    - xql_fusion_eal_read_count
    - xql_fusion_eal_write_count
    - xql_fusion_ingested_events
    - xql_inflight_api_errors_total
    - xql_inflight_api_response_seconds_count
    - xql_inflight_api_response_seconds_sum
    - xql_inflight_cancel_api_response_seconds_bucket
    - xql_ingestion_gnz_xql_bytes_dropped_bucket
    - xql_ingestion_gnz_xql_bytes_dropped_count
    - xql_ingestion_gnz_xql_bytes_ingested_bucket
    - xql_ingestion_gnz_xql_bytes_ingested_count
    - xql_ingestion_logs_count
    - xql_ingestion_raw_size_bytes
    - xql_logs_iterator_parser_panics_total
    - xql_non_archived_queries_gauge
    - xql_query_center_migration_bigquery_count
    - xql_query_center_migration_errors_failed_counter_total
    - xql_query_center_migration_mysql_count
    - xql_query_center_migration_status
    - xql_xql_enrichment_enrichment_cache_hit
    - xql_xql_enrichment_enrichment_cache_miss
    - xsoar_archive_error
    - xsoar_silent_playbooks_counter
    - xsoar_auto_extract_enrich_all_indicators_bucket
    - xsoar_auto_extract_enrich_indicator_command_bucket
    - xsoar_auto_extract_entry_processing_bucket
    - xsoar_auto_extract_find_indicator_bucket
    - xsoar_auto_extract_format_indicator_bucket
    - xsoar_auto_extract_indicator_enriched_command
    - xsoar_auto_extract_indicator_enrichment_timeout
    - xsoar_auto_extract_indicator_extracted
    - xsoar_auto_extract_indicator_formatted
    - xsoar_auto_extract_indicator_mapped
    - xsoar_auto_extract_map_indicator_bucket
    - xsoar_automation_executions_avg_duration_seconds
    - xsoar_automation_executions_count
    - xsoar_command_executions_avg_duration_seconds
    - xsoar_command_executions_count
    - xsoar_content_backup_snapshot_status
    - xsoar_engine_disconnections
    - xsoar_engine_health_status
    - xsoar_engine_last_health_timestamp
    - xsoar_fetch_execution_fetch_duration_seconds
    - xsoar_fetch_execution_health_status
    - xsoar_fetch_execution_ingestion_duration_seconds
    - xsoar_fetch_execution_pulled_incidents_count
    - xsoar_fetch_execution_pulled_indicators_count
    - xsoar_ha_lock_hijacks_total
    - xsoar_ha_mirroring_error
    - xsoar_ha_secondary_pb_executions_total
    - xsoar_migration_status
    - xsoar_msg_bus_publisher
    - xsoar_msg_bus_messages
    - xsoar_msg_bus_total_active
    - xsoar_msg_bus_oldest
    - xsoar_msg_bus_oldest_nth
    - xsoar_msg_bus_oldest_unhandled_nth
    - xsoar_msg_bus_total
    - xsoar_msg_bus_max_hpa
    - xsoar_msg_bus_min_hpa
    - xsoar_msg_bus_replicas
    - xsoar_msg_bus_reserved_replicas
    - xsoar_msg_bus_subscriber_pull
    - xsoar_msg_bus_subscriber_ack
    - xsoar_msg_bus_subscriber_nack
    - xsoar_msg_bus_dlq_subscriber_pull
    - xsoar_msg_bus_lock_latency_bucket
    - xsoar_msg_bus_insert_latency_bucket
    - xsoar_msg_bus_subscriber_pull_latency_bucket
    - xsoar_msg_bus_subscriber_ack_latency_bucket
    - xsoar_msg_bus_subscriber_nack_latency_bucket
    - xsoar_msg_bus_fetch_metrics_latency_bucket
    - xsoar_msg_bus_oldest_unhandled
    - xsoar_num_archived
    - xsoar_num_data_loss_alert
    - xsoar_num_integrations_updated
    - xsoar_num_of_current_integrations_installed
    - xsoar_num_oversized_alert
    - xsoar_num_panics
    - xsoar_num_sql_errors
    - xsoar_num_un_archived
    - xsoar_num_warroom_errors
    - xsoar_reminder_queue_push_error
    - xsoar_reminder_queue_counter
    - xsoar_reminder_queue_timed_out_entries
    - xsoar_stuck_inv_playbook_detected_total
    - xsoar_un_archive_error
    - xsoar_completed_inv_playbook_total
    - xsoar_inv_playbook_ack_nack_messages_total
    - workflow_failed
    - workflow_success
    - workflow_timeout
    - persistence_requests
    - logback_events_total
    - mongo_polaris_inserts_total
    - controller_runtime_reconcile_panics_total
    - controller_runtime_reconcile_errors_total
    - controller_runtime_terminal_reconcile_errors_total
    - qr_code_image_reader_calls_total
    - qr_code_image_cache_hits_total
    - qr_code_image_cache_size
    - qr_code_image_reader_call_duration_sum
    - qr_code_image_reader_call_duration_count
    - qr_code_image_reader_call_duration_bucket
    - cwp_.+
    - itdr_.*
    - ciem_.*
    - apisec_.*

  debug:
    - analytics_de_v2_vectorized_matcher_detector_count_per_layer_total
    - analytics_de_v2_vectorized_matcher_udf_process_time_count
    - analytics_de_v2_vectorized_matcher_udf_process_time_sum
    - GnzGlobal_DAO_operation_latency_seconds_bucket
    - GnzGlobal_DynamicConfig_get_config_failures_total
    - GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket
    - GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket
    - GnzGlobal_Quantum_last_insert_time_seconds
    - GnzGlobal_Throttler_bucket_tokens
    - GnzGlobal_Throttler_throttled_total
    - GnzGlobal_hotkeys_tracker_current_hot_keys
    - GnzGlobal_hotkeys_tracker_hot_rows_total
    - GnzGlobal_pithos_active_streams
    - GnzGlobal_pithos_aggregated_bytes_total
    - GnzGlobal_pithos_aggregated_objects_total
    - GnzGlobal_pithos_aggregation_duration_seconds
    - GnzGlobal_pithos_client_send_on_stream_latency_seconds_bucket
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_count
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_sum
    - GnzGlobal_pithos_client_stream_init_latency_seconds_bucket
    - GnzGlobal_pithos_committed_objects_total
    - GnzGlobal_pithos_dataset_aggregators
    - GnzGlobal_pithos_rate_limited_requests_total
    - GnzGlobal_pithos_streamed_bytes_total
    - GnzGlobal_storage_write_api_append_rows_duration_ms_count
    - GnzGlobal_storage_write_api_append_rows_duration_ms_sum
    - GnzGlobal_storage_write_api_status_total
    - GnzGlobal_transport_snowflake_bytes_written_total
    - GnzGlobal_transport_snowflake_channel_open_total
    - GnzGlobal_transport_snowflake_heartbeat_latency_seconds_bucket
    - GnzGlobal_transport_snowflake_heartbeat_latency_seconds_count
    - GnzGlobal_transport_snowflake_http_errors

prometheus:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - net_conntrack_dialer_conn_failed_total
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - prometheus_build_info
    - prometheus_config_last_reload_success_timestamp_seconds
    - prometheus_config_last_reload_successful
    - prometheus_engine_query_duration_seconds_sum
    - prometheus_http_request_duration_seconds_count
    - prometheus_http_request_duration_seconds_sum
    - prometheus_notifications_alertmanagers_discovered
    - prometheus_remote_storage_.+
    - prometheus_rule_evaluation_duration_seconds_count
    - prometheus_rule_evaluation_duration_seconds_sum
    - prometheus_rule_evaluation_failures_total
    - prometheus_rule_group_duration_seconds_sum
    - prometheus_rule_group_interval_seconds
    - prometheus_rule_group_iterations_missed_total
    - prometheus_rule_group_last_duration_seconds
    - prometheus_rule_group_rules
    - prometheus_sd_consul_rpc_failures_total
    - prometheus_sd_dns_lookup_failures_total
    - prometheus_target_interval_length_seconds
    - prometheus_target_interval_length_seconds_count
    - prometheus_target_scrape_pool_sync_total
    - prometheus_target_scrape_pool_targets
    - prometheus_target_scrapes_exceeded_sample_limit_total
    - prometheus_target_scrapes_sample_duplicate_timestamp_total
    - prometheus_target_scrapes_sample_out_of_bounds_total
    - prometheus_target_scrapes_sample_out_of_order_total
    - prometheus_target_sync_length_seconds_sum
    - prometheus_treecache_zookeeper_failures_total
    - prometheus_tsdb_blocks_loaded
    - prometheus_tsdb_checkpoint_creations_failed_total
    - prometheus_tsdb_checkpoint_deletions_failed_total
    - prometheus_tsdb_compaction_chunk_range_seconds_count
    - prometheus_tsdb_compaction_chunk_range_seconds_sum
    - prometheus_tsdb_compaction_chunk_samples_count
    - prometheus_tsdb_compaction_chunk_samples_sum
    - prometheus_tsdb_compaction_chunk_size_bytes_sum
    - prometheus_tsdb_compaction_duration_seconds_sum
    - prometheus_tsdb_compactions_failed_total
    - prometheus_tsdb_compactions_total
    - prometheus_tsdb_compactions_triggered_total
    - prometheus_tsdb_head_active_appenders
    - prometheus_tsdb_head_chunks
    - prometheus_tsdb_head_chunks_created_total
    - prometheus_tsdb_head_chunks_removed_total
    - prometheus_tsdb_head_gc_duration_seconds_sum
    - prometheus_tsdb_head_max_time
    - prometheus_tsdb_head_min_time
    - prometheus_tsdb_head_samples_appended_total
    - prometheus_tsdb_head_series
    - prometheus_tsdb_head_series_created_total
    - prometheus_tsdb_head_series_removed_total
    - prometheus_tsdb_reloads_failures_total
    - prometheus_tsdb_reloads_total
    - prometheus_tsdb_size_retentions_total
    - prometheus_tsdb_storage_blocks_bytes
    - prometheus_tsdb_symbol_table_size_bytes
    - prometheus_tsdb_time_retentions_total
    - prometheus_tsdb_wal_corruptions_total
    - prometheus_tsdb_wal_fsync_duration_seconds_count
    - prometheus_tsdb_wal_fsync_duration_seconds_sum
    - prometheus_tsdb_wal_truncate_duration_seconds_sum

stackdriver:
  regex:
    - stackdriver_bigquery_dataset_bigquery_googleapis_com_storage_uploaded_bytes
    - stackdriver_k_8_s_container_logging_googleapis_com_user_k_8_s_logs_count_by_application
    - stackdriver_bigquery_dataset_bigquery_googleapis_com_storage_uploaded_row_count
    - stackdriver_exporter_build_info
    - stackdriver_global_bigquery_googleapis_com_query_count
    - stackdriver_global_bigquery_googleapis_com_slots_allocated_for_project_and_job_type
    - stackdriver_k_8_s_container_logging_googleapis_com_byte_count
    - stackdriver_k_8_s_container_logging_googleapis_com_log_entry_count
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age
    - stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age_by_region
    - stackdriver_pubsub_topic_pubsub_googleapis_com_topic_send_message_operation_count
    - stackdriver_uptime_url_monitoring_googleapis_com_uptime_check_check_passed
    - stackdriver_gce_instance_compute_googleapis_com_instance_disk_read_bytes_count
    - stackdriver_gce_instance_compute_googleapis_com_instance_disk_write_bytes_count
    - stackdriver_gce_instance_compute_googleapis_com_instance_disk_write_ops_count
    - stackdriver_gce_instance_compute_googleapis_com_instance_disk_read_ops_count
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_cpu_utilization
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_cpu_utilization_by_operation_type
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_edition_feature_usage
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_processing_units
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_storage_utilization
    - stackdriver_spanner_instance_spanner_googleapis_com_api_request_count
    - stackdriver_spanner_instance_spanner_googleapis_com_api_received_bytes_count
    - stackdriver_spanner_instance_spanner_googleapis_com_api_sent_bytes_count
    - stackdriver_spanner_instance_spanner_googleapis_com_query_stat_total_failed_execution_count
    - stackdriver_spanner_instance_spanner_googleapis_com_query_stat_total_query_latencies
    - stackdriver_spanner_instance_spanner_googleapis_com_lock_stat_total_lock_wait_time
    - stackdriver_spanner_instance_spanner_googleapis_com_transaction_stat_total_transaction_latencies
    - stackdriver_spanner_instance_spanner_googleapis_com_instance_cpu_utilization_by_priority

kubernetes-apiservers:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - kubernetes_build_info
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds

kubernetes-nodes:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - kubelet_node_name
    - kubelet_volume_stats_available_bytes
    - kubelet_volume_stats_capacity_bytes
    - kubelet_volume_stats_used_bytes
    - kubernetes_build_info
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds

es-xsoar-exporter:
  regex:
    - elasticsearch_cluster_health_active_primary_shards
    - elasticsearch_cluster_health_active_shards
    - elasticsearch_cluster_health_delayed_unassigned_shards
    - elasticsearch_cluster_health_initializing_shards
    - elasticsearch_cluster_health_number_of_data_nodes
    - elasticsearch_cluster_health_number_of_nodes
    - elasticsearch_cluster_health_number_of_pending_tasks
    - elasticsearch_cluster_health_relocating_shards
    - elasticsearch_cluster_health_status
    - elasticsearch_cluster_health_total_scrapes
    - elasticsearch_cluster_health_unassigned_shards
    - elasticsearch_filesystem_data_available_bytes
    - elasticsearch_filesystem_data_size_bytes
    - elasticsearch_filesystem_io_stats_device_read_operations_count
    - elasticsearch_filesystem_io_stats_device_read_size_kilobytes_sum
    - elasticsearch_filesystem_io_stats_device_write_operations_count
    - elasticsearch_filesystem_io_stats_device_write_size_kilobytes_sum
    - elasticsearch_indices_deleted_docs_primary
    - elasticsearch_indices_docs
    - elasticsearch_indices_docs_deleted
    - elasticsearch_indices_docs_primary
    - elasticsearch_indices_fielddata_memory_size_bytes
    - elasticsearch_indices_indexing_index_time_seconds_total
    - elasticsearch_indices_indexing_index_total
    - elasticsearch_indices_indexing_is_throttled
    - elasticsearch_indices_merges_docs_total
    - elasticsearch_indices_merges_total_size_bytes_total
    - elasticsearch_indices_merges_total_time_seconds_total
    - elasticsearch_indices_query_cache_memory_size_bytes
    - elasticsearch_indices_refresh_time_seconds_total
    - elasticsearch_indices_request_cache_memory_size_bytes
    - elasticsearch_indices_search_fetch_time_seconds
    - elasticsearch_indices_search_query_time_seconds
    - elasticsearch_indices_search_query_total
    - elasticsearch_indices_search_scroll_time_seconds
    - elasticsearch_indices_search_suggest_time_seconds
    - elasticsearch_indices_segment_count_primary
    - elasticsearch_indices_segment_count_total
    - elasticsearch_indices_segment_memory_bytes_primary
    - elasticsearch_indices_segments_count
    - elasticsearch_indices_segments_doc_values_memory_in_bytes
    - elasticsearch_indices_segments_fixed_bit_set_memory_in_bytes
    - elasticsearch_indices_segments_index_writer_memory_in_bytes
    - elasticsearch_indices_segments_memory_bytes
    - elasticsearch_indices_segments_norms_memory_in_bytes
    - elasticsearch_indices_segments_points_memory_in_bytes
    - elasticsearch_indices_segments_stored_fields_memory_in_bytes
    - elasticsearch_indices_segments_term_vectors_memory_in_bytes
    - elasticsearch_indices_segments_terms_memory_in_bytes
    - elasticsearch_indices_segments_version_map_memory_in_bytes
    - elasticsearch_indices_shared_docs
    - elasticsearch_indices_store_size_bytes
    - elasticsearch_indices_store_size_bytes_primary
    - elasticsearch_indices_store_size_bytes_total
    - elasticsearch_indices_store_throttle_time_seconds_total
    - elasticsearch_indices_translog_operations
    - elasticsearch_indices_translog_size_in_bytes
    - elasticsearch_jvm_gc_collection_seconds_count
    - elasticsearch_jvm_gc_collection_seconds_sum
    - elasticsearch_jvm_memory_max_bytes
    - elasticsearch_jvm_memory_pool_max_bytes
    - elasticsearch_jvm_memory_used_bytes
    - elasticsearch_nodes_roles
    - elasticsearch_os_cpu_percent
    - elasticsearch_os_load1
    - elasticsearch_os_load15
    - elasticsearch_os_load5
    - elasticsearch_process_cpu_percent
    - elasticsearch_process_max_files_descriptors
    - elasticsearch_process_open_files_count
    - elasticsearch_thread_pool_active_count
    - elasticsearch_thread_pool_completed_count
    - elasticsearch_thread_pool_largest_count
    - elasticsearch_thread_pool_queue_count
    - elasticsearch_thread_pool_rejected_count
    - elasticsearch_thread_pool_threads_count
    - elasticsearch_transport_rx_size_bytes_total
    - elasticsearch_transport_tx_size_bytes_total

kubernetes-service-endpoints:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - http_request_duration_seconds_bucket
    - kube_cronjob_info
    - kube_cronjob_spec_suspend
    - kube_cronjob_status_active
    - kube_cronjob_status_last_schedule_time
    - kube_deployment_labels
    - kube_deployment_spec_replicas
    - kube_deployment_status_replicas_available
    - kube_horizontalpodautoscaler_spec_max_replicas
    - kube_horizontalpodautoscaler_spec_min_replicas
    - kube_horizontalpodautoscaler_status_current_replicas
    - kube_job_complete
    - kube_job_failed
    - kube_job_owner
    - kube_job_status_completion_time
    - kube_job_status_active
    - kube_job_status_succeeded
    - kube_job_status_start_time
    - kube_namespace_created
    - kube_node_info
    - kube_node_labels
    - kube_node_status_condition
    - kube_node_status_allocatable
    - kube_persistentvolumeclaim_info
    - kube_persistentvolumeclaim_resource_requests_storage_bytes
    - kube_pod_container_info
    - kube_pod_container_resource_limits
    - kube_pod_container_resource_requests
    - kube_pod_container_status_last_terminated_reason
    - kube_pod_container_status_restarts_total
    - kube_pod_container_status_running
    - kube_pod_owner
    - kube_pod_status_scheduled
    - kube_pod_created
    - kube_replicaset_owner
    - kube_statefulset_labels
    - kube_statefulset_replicas
    - kube_statefulset_status_replicas_ready
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - flux_resource_info
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used

proxy-vm:
  regex:
    - go_gc_duration_seconds_sum
    - go_goroutines
    - go_memstats_alloc_bytes
    - go_memstats_alloc_bytes_total
    - go_memstats_buck_hash_sys_bytes
    - go_memstats_gc_sys_bytes
    - go_memstats_heap_alloc_bytes
    - go_memstats_heap_idle_bytes
    - go_memstats_heap_inuse_bytes
    - go_memstats_heap_released_bytes
    - go_memstats_heap_sys_bytes
    - go_memstats_mcache_inuse_bytes
    - go_memstats_mcache_sys_bytes
    - go_memstats_mspan_inuse_bytes
    - go_memstats_mspan_sys_bytes
    - go_memstats_next_gc_bytes
    - go_memstats_other_sys_bytes
    - go_memstats_stack_inuse_bytes
    - go_memstats_stack_sys_bytes
    - go_memstats_sys_bytes
    - node_cpu_seconds_total
    - node_disk_io_time_seconds_total
    - node_disk_read_bytes_total
    - node_disk_read_time_seconds_total
    - node_disk_reads_completed_total
    - node_disk_write_time_seconds_total
    - node_disk_writes_completed_total
    - node_disk_written_bytes_total
    - node_exporter_build_info
    - node_filesystem_avail_bytes
    - node_filesystem_size_bytes
    - node_load1
    - node_memory_Buffers_bytes
    - node_memory_Cached_bytes
    - node_memory_MemAvailable_bytes
    - node_memory_MemFree_bytes
    - node_memory_MemTotal_bytes
    - node_network_receive_bytes_total
    - node_network_receive_packets_total
    - node_network_transmit_bytes_total
    - node_nf_conntrack_entries
    - node_nf_conntrack_entries_limit
    - node_systemd_unit_state
    - node_uname_info
    - node_vmstat_pgpgin
    - node_vmstat_pgpgout
    - node_vmstat_pswpin
    - node_vmstat_pswpout
    - process_cpu_seconds_total
    - process_max_fds
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - stackdriver_uptime_url_monitoring_googleapis_com_uptime_check_check_passed
