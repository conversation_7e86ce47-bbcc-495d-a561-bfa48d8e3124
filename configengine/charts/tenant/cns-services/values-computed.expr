_component_names:
  cns-api: "'cns-api'"
  cns-evaluator: "'cns-evaluator'"
  cns-graph-engine: "'cns-graph-engine'"
  cns-graph-ingester: "'cns-graph-ingester'"

namespaceOverride: "globals.cwp_namespace"

_images:
  cns_api:
    component: "get(images.components, 'cns-api')"
    family: "get(images.families, local._images.cns_api.component.family)"
  cns_evaluator:
    component: "get(images.components, 'cns-evaluator')"
    family: "get(images.families, local._images.cns_evaluator.component.family)"
  cns_graph_engine:
    component: "get(images.components, 'cns-graph-engine')"
    family: "get(images.families, local._images.cns_graph_engine.component.family)"
  cns_graph_ingester:
    component: "get(images.components, 'cns-graph-ingester')"
    family: "get(images.families, local._images.cns_graph_ingester.component.family)"

images:
  cns_api:
    registry: "globals.gcr"
    repository: "local._images.cns_api.component.repository ?? local._images.cns_api.family.repository"
    tag: "local._images.cns_api.component.tag ?? local._images.cns_api.family.tag"
  cns_graph_engine:
    registry: "globals.gcr"
    repository: "local._images.cns_graph_engine.component.repository ?? local._images.cns_graph_engine.family.repository"
    tag: "local._images.cns_graph_engine.component.tag ?? local._images.cns_graph_engine.family.tag"
  cns_graph_ingester:
    registry: "globals.gcr"
    repository: "local._images.cns_graph_ingester.component.repository ?? local._images.cns_graph_ingester.family.repository"
    tag: "local._images.cns_graph_ingester.component.tag ?? local._images.cns_graph_ingester.family.tag"
  cns_evaluator:
    registry: "globals.gcr"
    repository: "local._images.cns_evaluator.component.repository ?? local._images.cns_evaluator.family.repository"
    tag: "local._images.cns_evaluator.component.tag ?? local._images.cns_evaluator.family.tag"

cronjobs:
  evaluator:
    name: '"xdr-st-" + tenant.lcaas_id + "-cns-evaluator-cronjob"'

  graph_ingester:
    name: '"xdr-st-" + tenant.lcaas_id + "-cns-graph-ingester-cronjob"'

deployments:
  api:
    name: '"xdr-st-" + tenant.lcaas_id + "-cns-api"'
    service_name: '"xdr-st-" + tenant.lcaas_id + "-cns-api-svc"'
  graph_engine:
    name: '"xdr-st-" + tenant.lcaas_id + "-cns-graph-engine"'

cns_configmap_name: '"xdr-st-" + tenant.lcaas_id + "-cns-configmap"'
cns_configmap_data:
  CNS_API_ENDPOINT: 'globals.st_resource_prefix + "-cns-api-svc"'
  CNS_GRAPH_ENGINE_ENDPOINT: 'globals.st_resource_prefix + "-cns-graph-engine"'
  POSTGRESQLCONF_HOST: globals.env.PGHOST
  POSTGRESQLCONF_PORT: "'5432'"
  CNS_API_PORT: "'5000'"
  CNS_GRAPH_ENGINE_PORT: "'8000'"
  POSTGRESQLCONF_DB_NAME: "'cwp'"
  POSTGRESQLCONF_USERNAME: "'root'"
  POSTGRESQLCONF_SEARCH_PATH: "'public'"
  CORTEX_CONTEXT_IGNORE_SBAC_ERRORS: "'true'"
  FINDINGS_REDIS_ADDRESS: globals.env.REDIS_CWP_URI
  FINDINGS_REDIS_PORT: "'6379'"
  CLOUDSEC_CLOUD_API_SERVICE_URL: 'globals.st_resource_prefix + "-cloud-api-service." + globals.cloudsec_namespace + ":8080"'
  CLOUDSEC_RULES_API_SERVICE_URL: 'globals.st_resource_prefix + "-rule-management." + globals.cloudsec_namespace  + ":8080"'
  CNS_NEO4J_CONF_BOLT_PORT: "'7687'"
  CNS_NEO4J_CONF_HOST: "'cns-neo4j.' + globals.cwp_namespace"
  SECURITY_CONTROLS_UPDATE_TOPIC_NAME: "'dp-security-controls-' + tenant.lcaas_id"
  ENABLE_CLOUD_POSTURE: "'true'"  # compatibility with the Metro chart

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false
  - configMapRef:
      name: "local.cns_configmap_name"
      optional: false

env:
  POSTGRESQLCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cwp-secrets"'
        key: "'postgres_password'"
        optional: false
  FINDINGS_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id+"-cwp-secrets"'
        key: "'cwp_redis_password'"
        optional: false
  CNS_NEO4J_CONF_AUTH:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id+"-cns-secrets"'
        key: "'cns_neo4j_user_password'"
        optional: false

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"cwp-cna" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  evaluator_cronjob:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "1"'
      memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "2Gi"'
    limits:
      cpu: '"4"'
      memory: '"4Gi"'
  graph_ingester_cronjob:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "1"'
      memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "2Gi"'
    limits:
      cpu: '"4"'
      memory: '"4Gi"'
  graph_engine:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
      memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
    limits:
      cpu: '"1"'
      memory: '"1Gi"'
  api:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
      memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
    limits:
      cpu: '"1"'
      memory: '"1Gi"'
