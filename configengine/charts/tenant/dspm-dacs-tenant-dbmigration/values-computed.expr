_component_image: "'dspm-data-classification-settings'"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

_jobs_names:
  dbMigration: '"dspm-dacs-db-migration"'

_db:
  name: '"dspm_" + tenant.lcaas_id'
  user: '"root"'
  schema: '"data_classification_settings"'
  url: '"jdbc:postgresql://" + globals.env.PGHOST + "/" + local._db.name + "?currentSchema=" + local._db.schema'

dbMigration:
  configuration:
    metro:
      config:
        activate:
          on-tenant: tenant.lcaas_id
      tenants:
        - id: "tenant.lcaas_id"
      datasource:
        url: 'local._db.url'
        username: '"${DB_USER}"'
        password: '"${DB_PASSWORD}"'
        hikari:
          poolName: "'Hikari'"
          auto-commit: "'false'"

  fullnameOverride: 'local._jobs_names.dbMigration'
  name: 'local._jobs_names.dbMigration'
  containerName: '"job-liquibase-dacs-" + tenant.lcaas_id'
  backoffLimit: '"60"'
  activeDeadlineSeconds: '"600"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"20"'
  image:
    fullName: 'local._images.registry + "/" + local._images.repository  +":" +  local._images.tag'
  restartPolicy: '"Never"'
  automountServiceAccountToken: true
  env:
    LCAAS_ID: "tenant.lcaas_id"
    METRO_CONTEXT_HOLDER_STRATEGY: "'STATIC'"
    MAIN_CLASS: '"security.dig.dacs.DbMigrationApp"'
    SPRING_PROFILES_ACTIVE: '"dbMigration"'
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    SPRING_LIQUIBASE_DEFAULT_SCHEMA: "local._db.schema"
    DB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: '"postgres_password"'
          optional: false
    DB_USER: 'local._db.user'

  resources:
    limits:
      cpu: '"3"'
      memory: '"512Mi"'
    requests:
      cpu: '"250m"'
      memory: '"256Mi"'