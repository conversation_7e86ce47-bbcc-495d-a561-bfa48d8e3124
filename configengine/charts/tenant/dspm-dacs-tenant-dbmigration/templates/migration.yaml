{{ with .Values.dbMigration }}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .jobAnnotations }}
  annotations: {{ toYaml . | nindent 4 }}
    {{- include "common.sre.panw.annotations" $ | nindent 4 }}
  {{- end }}
  labels:
    {{- include "common.sre.panw.labels" $ | nindent 4 }}
    {{- include "chart.labels" $ | nindent 4 }}
    {{- with .jobLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ .name | trunc 58 | trimSuffix "-" }}-{{ .name | sha256sum | trunc 5 }}
  namespace: {{ .namespace }}
spec:
  {{- with .backoffLimit }}
  backoffLimit: {{ . | int }}
  {{- end }}
  {{- with .activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ . | int }}
  {{- end }}
  {{- with .ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ . | int }}
  {{- end }}
  selector:
    matchLabels:
      job-name: {{ .name | trunc 58 | trimSuffix "-" }}-{{ .name | sha256sum | trunc 5 }}
  template:
    metadata:
      {{- with .podAnnotations }}
      annotations: {{ toYaml . | nindent 8 }}
        {{- include "common.sre.panw.annotations" $ | nindent 8 }}
      {{- end }}
      labels:
        {{- include "common.sre.panw.labels" $ | nindent 8 }}
        job-name: {{ .name | trunc 58 | trimSuffix "-" }}-{{ .name | sha256sum | trunc 5 }}
        {{- include "chart.labels" $ | nindent 8 }}
        {{- with .podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .automountServiceAccountToken }}
      automountServiceAccountToken: {{ . }}
      {{- end }}
      containers:
        - name: {{ .containerName }}
          {{if .command}}
          command: {{- toYaml .command | nindent 12 }}
          {{end}}
          {{if .args}}
          args: {{- toYaml .args | nindent 12 }}
          {{end}}
          image: {{ .image.fullName }}
          {{- with .image.pullPolicy }}
          imagePullPolicy: {{ . }}
          {{- end }}
          {{- with .env }}
          env: {{ (include "common.renderEnv" .) | nindent 12 }}
          {{- end }}
          {{- with .envFrom }}
          envFrom: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .startupProbe }}
          startupProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .livenessProbe }}
          livenessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}         
          {{- with .containerSecurityContext }}
          securityContext: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .resources }}
          resources: {{ toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: serviceconfig
              mountPath: /config/config.yaml
              subPath: config.yaml
            {{- with .volumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with .extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
      {{- with .priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- with .nodeSelector }}
      nodeSelector: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .affinity }}
      affinity: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .tolerations }}
      tolerations: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .restartPolicy }}
      restartPolicy: {{ . }}
      {{- end }}
      {{- with .podSecurityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . | int }}
      {{- end }}
      volumes:
        - name: serviceconfig
          secret:
            secretName: {{ .name  }}-serviceconfig
        {{- with .volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
{{- end}}
