{{/*
Expand the name of the chart.
*/}}
{{- define "chart.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "chart.fullname" -}}
{{- if .Values.fullnameOverride -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- $name := default .Chart.Name .Values.nameOverride -}}
{{- if contains $name .Release.Name -}}
{{- .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{/*
Create a fully qualified app name by combining the chart name and release name.
The result is truncated to 63 characters and any trailing "-" is removed.
*/}}
{{- printf "%s-%s" $name .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "chart.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "chart.labels" -}}
{{ include "chart.selectorLabels" . }}
app.kubernetes.io/name: {{ include "chart.fullname" . }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "chart.selectorLabels" -}}
app: {{ include "chart.fullname" . }}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "chart.serviceAccountName" -}}
{{- if .Values.serviceAccount.create -}}
    {{ default .Chart.Name .Values.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "chart.serviceName" -}}
{{ default (include "chart.fullname" .) .Values.service.name }}
{{- end -}}

{{- define "common.namespace" -}}
{{- if .Values.namespaceOverride -}}
  {{- .Values.namespaceOverride -}}
{{- else -}}
  {{- .Release.Namespace -}}
{{- end  -}}
{{- end -}}

{{- define "common.renderEnv" }}
{{- $envList := list -}}
{{- range $name, $env := . }}
  {{- if kindIs "string" $env -}}
    {{ $envList = append $envList (dict "name" $name "value" $env ) }}
  {{- else if (kindIs "float64" $env) -}}
    {{ $envList = append $envList (dict "name" $name "value" (toString ($env | int))) }}
  {{- else if (kindIs "bool" $env) -}}
    {{ $envList = append $envList (dict "name" $name "value" (toString $env) ) }}
  {{- else if (kindIs "map" $env)}}
    {{- if $env.disabled -}}
    {{- else if hasKey $env "value" -}}
      {{ $envList = append $envList (dict "name" $name "value" $env.value ) }}
    {{- else if hasKey $env "valueFrom" -}}
      {{ $envList = append $envList (dict "name" $name "valueFrom" $env.valueFrom ) }}
    {{- else -}}
      {{ fail (printf "missing value or valueFrom attributes: key=%s value=%s" $name $env)  }}
    {{- end -}}
  {{- else -}}
    {{ fail (printf "unsupported type %s: key=%s value=%s" (kindOf $env) $name $env) }}
  {{- end -}}
{{- end -}}
{{ $envList | toYaml }}
{{- end -}}

{{- define "common.lookupValueFromRef" }}
{{- $namespace := (include "common.namespace" .context) -}}
{{- $lookupValues := dict -}}
{{- range $name, $env := .env }}
  {{- if hasKey $env.valueFrom "secretKeyRef" -}}
    {{- $secretRef := $env.valueFrom.secretKeyRef -}}
    {{- $secret := (default dict (lookup "v1" "Secret" $namespace $secretRef.name)) -}}
    {{- $_ := set $lookupValues $name ((dig "data" $secretRef.key ("!! MISSING SECRET !!" | b64enc) $secret) | b64dec) -}}
  {{- end -}}
  {{- if hasKey $env.valueFrom "configMapKeyRef" -}}
    {{- $configMapKeyRef := $env.valueFrom.configMapKeyRef -}}
    {{- $configMap := (default dict (lookup "v1" "ConfigMap" $namespace $configMapKeyRef.name)) -}}
    {{- $_ := set $lookupValues $name (dig "data" $configMapKeyRef.key "!! MISSING CONFIGMAP !!" $configMap) -}}
  {{- end -}}
{{- end -}}
{{ $lookupValues | toYaml }}
{{- end -}}

{{- define "common.deployment.replicas" }}
{{- if and .Values (hasKey .Values "replicas") (eq (.Values.replicas | toString) "0") -}}
0
{{- else if and (hasKey . "replicas") (eq (.replicas | toString) "0") -}}
0
{{- else if and .Values .Values.hpa .Values.hpa.enabled -}}
{{- else if and .Values .Values.vsg .Values.vsg.create .Values.vsg.enabled .Values.vsg.oomScaling.enabled -}}
{{- else if and .hpa .hpa.enabled -}}
{{- else if and .vsg .vsg.create .vsg.enabled .vsg.oomScaling.enabled -}}
{{- else if and .Values .Values.replicas -}}
  {{- .Values.replicas | int -}}
{{- else if .replicas -}}
  {{- .replicas | int -}}
{{- else -}}
1
{{- end -}}
{{- end -}}

{{/*
{{- if and .Values.vsg.create .Values.vsg.enabled (or .Values.vsg.verticalScaling.enabled .Values.vsg.oomScaling.enabled) -}}
  {{- $lookupChecksumResources := ((include "common.deployment.lookupChecksumResources" .) | fromYaml) -}}
  {{- $_ := set .Values "resources" $lookupChecksumResources.resources -}}
  {{- $_ := set .Values "deploymentAnnotations" (merge .Values.deploymentAnnotations $lookupChecksumResources.annotations) -}}
{{- end -}}
*/}}

{{- define "common.deployment.lookupChecksumResources" }}
{{- $resources := .Values.resources -}}
{{- $name := (include "chart.fullname" .) -}}
{{- $namespace := (include "common.namespace" .) -}}
{{- $raw_checksum := (toYaml $resources | sha256sum) -}}
{{- $annotation_name := "checksum/resources" -}}
{{- $annotations := (dict $annotation_name $raw_checksum) -}}
{{- with (lookup "apps/v1" "Deployment" $namespace $name | default dict) -}}
  {{- $live_checksum := (dig "annotations" $annotation_name $raw_checksum .metadata) -}}
  {{- if eq $raw_checksum $live_checksum -}}
    {{- $resources = (index .spec.template.spec.containers 0 "resources") -}}
  {{- end -}}
{{- end -}}
{{- toYaml (dict "resources" $resources "annotations" $annotations) -}}
{{- end -}}

{{/*
{{- if and .Values.vsg.create .Values.vsg.enabled (or .Values.vsg.verticalScaling.enabled .Values.vsg.oomScaling.enabled) -}}
  {{- $lookupChecksumResources := ((include "common.sts.lookupChecksumResources" .) | fromYaml) -}}
  {{- $_ := set .Values "resources" $lookupChecksumResources.resources -}}
  {{- $_ := set .Values "statefulsetAnnotations" (merge .Values.statefulsetAnnotations $lookupChecksumResources.annotations) -}}
{{- end -}}
*/}}

{{- define "common.sts.lookupChecksumResources" }}
{{- $resources := .Values.resources -}}
{{- $name := (include "chart.fullname" .) -}}
{{- $namespace := (include "common.namespace" .) -}}
{{- $raw_checksum := (toYaml $resources | sha256sum) -}}
{{- $annotation_name := "checksum/resources" -}}
{{- $annotations := (dict $annotation_name $raw_checksum) -}}
{{- with (lookup "apps/v1" "StatefulSet" $namespace $name | default dict) -}}
  {{- $live_checksum := (dig "annotations" $annotation_name $raw_checksum .metadata) -}}
  {{- if eq $raw_checksum $live_checksum -}}
    {{- $resources = (index .spec.template.spec.containers 0 "resources") -}}
  {{- end -}}
{{- end -}}
{{- toYaml (dict "resources" $resources "annotations" $annotations) -}}
{{- end -}}

{{- define "common.cronjob.suspend" }}
{{- $cronjob := (default dict (lookup "batch/v1" "CronJob" .namespace .cronjobName)) -}}
{{- dig "spec" "suspend" .suspend $cronjob -}}
{{- end -}}

{{- define "common.convertToBytes" }}
{{- $value := (mustRegexFind "^[0-9]+(\\.[0-9]+)?" .) | float64 -}}
{{- $unit := mustRegexFind "(Ki?|Mi?|Gi?|Ti?)$" . -}}
{{- if eq $unit "Ki" -}}
  {{/* Ki to bytes (1024^1) */}}
  {{- (mulf $value 1024) | int }}
{{- else if eq $unit "Mi" -}}
  {{/* Mi to bytes (1024^2) */}}
  {{- (mulf $value 1048576) | int }}
{{- else if eq $unit "Gi" -}}
  {{/* Gi to bytes (1024^3) */}}
  {{- (mulf $value 1073741824)| int }}
{{- else if eq $unit "Ti" -}}
  {{/* Ti to bytes (1024^4) */}}
  {{- (mulf $value 1099511627776) | int }}
{{- else if eq $unit "K" -}}
  {{/* K to bytes (1000^1) */}}
  {{- (mulf $value 1000) | int }}
{{- else if eq $unit "M" -}}
  {{/* M to bytes (1000^2) */}}
  {{- (mulf $value 1000000) | int }}
{{- else if eq $unit "G" -}}
  {{/* G to bytes (1000^3) */}}
  {{- (mulf $value 1000000000) | int }}
{{- else if eq $unit "T" -}}
  {{/* T to bytes (1000^4) */}}
  {{- (mulf $value 1000000000000) | int }}
{{- else -}}
  {{- fail (printf "Unsupported unit: %s" $unit) -}}
{{- end -}}
{{- end -}}

{{- define "common.pvc.size" }}
{{- $pvcSize := .pvcSize -}}
{{- $pvc := (lookup "v1" "PersistentVolumeClaim" .namespace .pvcName | default dict) -}}
{{- $pvcSizeBytes := (include "common.convertToBytes" $pvcSize) | int -}}
{{- $pvcCapacitySize := (dig "status" "capacity" "storage" $pvcSize $pvc) -}}
{{- $pvcCapacitySizeBytes := (include "common.convertToBytes" $pvcCapacitySize) | int -}}
{{- (ge $pvcCapacitySizeBytes $pvcSizeBytes) | ternary $pvcCapacitySize $pvcSize -}}
{{- end -}}

{{- define "common.pvc.storageClassName" }}
{{- $pvcSize := .storageClassName -}}
{{- $pvc := (lookup "v1" "PersistentVolumeClaim" .namespace .pvcName | default dict) -}}
{{- dig "spec" "storageClassName" .storageClassName $pvc -}}
{{- end -}}

{{- define "common.sts.pvc.size" }}
{{- $sts := (lookup "apps/v1" "StatefulSet" .namespace .stsName | default dict) -}}
{{- $pvc := (first (dig "spec" "volumeClaimTemplates" (list dict) $sts)) -}}
{{- dig "spec" "resources" "requests" "storage" .pvcSize $pvc -}}
{{- end -}}

{{- define "common.sts.pvc.storageClassName" }}
{{- $sts := (lookup "apps/v1" "StatefulSet" .namespace .stsName | default dict) -}}
{{- $pvc := (first (dig "spec" "volumeClaimTemplates" (list dict) $sts)) -}}
{{- dig "spec" "storageClassName" .storageClassName $pvc -}}
{{- end -}}

{{-  define "common.configmap.render.data" }}
{{- range $key, $value := . }}
{{- if (not (kindIs "invalid" $value)) }}
  {{- (printf "%s: %s" $key ($value | quote)) | nindent 2 }}
{{- end }}
{{- end }}
{{- end -}}

{{- define "common.sre.panw.annotations" -}}
{{- $override := $.Values.overrideChartAnnotationsKeys | default list -}}
{{- $keys := ternary $override (list 
    "owner.panw/service-name"
    "owner.panw/group"
    "owner.panw/source-code-repository-url"
    "owner.panw/source-code-ops-helm-chart-url"
    "owner.panw/team"
    "owner.panw/documentation-url"
    "owner.panw/team-slack-handle"
    "owner.panw/people-slack-handle-team-lead"
    "owner.panw/people-slack-handle-owners-group"
) (gt (len $override) 0) -}}
{{- $out := dict -}}
{{- range $k, $v := $.Chart.Annotations }}
  {{- if has $k $keys }}
    {{- $_ := set $out $k $v }}
  {{- end }}
{{- end }}
{{- if gt (len $out) 0 }}
{{- toYaml $out }}
{{- end }}
{{- end }}


{{- define "common.sre.panw.labels" -}}
{{- $override := $.Values.overrideChartLabelsKeys | default list -}}
{{- $keys := ternary $override (list 
  "owner.panw/team-slack-handle"
  "owner.panw/group" 
  "owner.panw/people-slack-handle-team-lead"
  "owner.panw/team" 
  "owner.panw/people-slack-handle-owners-group"
) (gt (len $override) 0) -}}
{{- $regex := `([A-Za-z0-9][-A-Za-z0-9_.]*[A-Za-z0-9])` -}}
{{- $out := dict -}}
{{- range $k, $v := $.Chart.Annotations }}
  {{- if has $k $keys }}
    {{- $val := regexFind $regex (lower $v) }}
    {{- if $val }}
      {{- $_ := set $out $k $val }}
    {{- end }}
  {{- end }}
{{- end }}
{{- if gt (len $out) 0 }}
{{- toYaml $out }}
{{- end }}
{{- end }}



{{- define "common.snapshots.annotations" -}}
  {{- if and .Values .Values.snapshots -}}
    {{- if .Values.snapshots.dailyEnabled -}}
      {{- with .Values.snapshots.daily }}
disk-snapshot/daily: {{ . | toJson | quote }}
      {{- end -}}
    {{- end -}}
    {{- if .Values.snapshots.hourlyEnabled -}}
      {{- with .Values.snapshots.hourly }}
disk-snapshot/hourly: {{ . | toJson | quote }}
      {{- end -}}
    {{- end -}}
  {{- end -}}
{{- end -}}

{{- define "common.snapshots.labels" -}}
  {{- if and .Values .Values.snapshots (or .Values.snapshots.hourlyEnabled .Values.snapshots.dailyEnabled) -}}
    {{- if not .Values.persistence.labels -}}
      {{- $_ := set .Values.persistence "labels" dict -}}
    {{- end -}}
    {{- if or .Values.snapshots.dailyEnabled .Values.snapshots.hourlyEnabled -}}
      {{- $_ := merge .Values.persistence.labels (dict "disk-snapshot/required" "true") -}}
    {{- end -}}
  {{- end -}}
{{- end -}}
