apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: dp-ipl-alyx
  namespace: "{{ .globals.dp_ipl_alyx_namespace }}"
spec:
  chart:
    spec:
      chart: tenant/dp-ipl-alyx
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
        interval: "{{ .globals.flux.chart_pull_interval }}"
  driftDetection:
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    createNamespace: true
    disableWait: {{ .globals.alyx_readiness }}
{{- if .infra_ff.enable_alyx_migration }}
    remediation:
      retries: -1
{{- end }}
  interval: 1m0s
  rollback:
    disableWait: true
  targetNamespace: "{{ .globals.dp_ipl_alyx_namespace }}"
  uninstall:
    disableWait: true
  upgrade:
    disableWait: {{ .globals.alyx_readiness }}
{{- if .infra_ff.enable_alyx_migration }}
    remediation:
      retries: -1
{{- end }}
  values: {}
  dependsOn:
    - name: dp-ipl-alyx-operator
      namespace: "{{ .globals.dp_ipl_alyx_namespace }}"