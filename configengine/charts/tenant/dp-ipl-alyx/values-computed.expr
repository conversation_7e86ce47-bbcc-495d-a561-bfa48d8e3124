_component_name: "'dp-ipl-alyx'"

namespace: "globals.dp_ipl_alyx_namespace"
namespaceOverride: "globals.dp_ipl_alyx_namespace"
fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

lcaas_id: 'tenant.lcaas_id'

replicas: 'infra_ff.enable_alyx ? globals.scylla_nodes_count : 0'

gcsBucket: 'tenant.project_id + "-dp-ipl-alyx"'

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: '"xdr-pool"'
              operator: '"In"'
              values:
                - 'infra_ff.is_enable_prod_spec ? "alyx" : "wi-static"'
tolerations:
  - key: "'xdr-pool'"
    operator: "'Equal'"
    value: "'alyx'"

resources:
  limits:
    cpu: |-
      tenant.is_metro_tenant ? "300m" :
      infra_ff.is_enable_prod_spec ? "3" :
      "2"
    memory: |-
      tenant.is_metro_tenant ? "2Gi" :
      !infra_ff.is_enable_prod_spec ? "12Gi" :
      "26Gi"
  requests:
    cpu: |-
      tenant.is_metro_tenant ? "300m" :
      infra_ff.is_enable_prod_spec ? "3" :
      "2"
    memory: |-
      tenant.is_metro_tenant ? "2Gi" :
      !infra_ff.is_enable_prod_spec ? "12Gi" :
      "26Gi"

serviceAccount:
  name: "'dp-ipl-alyx-sa'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

persistence:
  size: 'tenant.is_metro_tenant ? "20Gi" : "256Gi"'
nodeStorage:
  capacity: 'tenant.is_metro_tenant || !infra_ff.is_enable_prod_spec ? "50Gi" : "350Gi"'
  maxCapacity: 'tenant.is_metro_tenant || !infra_ff.is_enable_prod_spec ? "150Gi" : "700Gi"'
  scalingStep: 'tenant.is_metro_tenant || !infra_ff.is_enable_prod_spec ? "20Gi" : "50Gi"'

env:
  RUST_LOG: '"INFO"'
  ALYX__CLUSTER__OBJECT_STORE_URL: '"gs://" + local.gcsBucket'
  ALYX_RDB_BLOCK_CACHE_BYTES: |-
      tenant.is_metro_tenant ? "*********" :
      !infra_ff.is_enable_prod_spec ? "**********" :
      "**********"
