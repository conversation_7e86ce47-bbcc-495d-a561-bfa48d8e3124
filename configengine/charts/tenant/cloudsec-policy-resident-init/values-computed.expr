_component_name: "'cloudsec-policy-resident-init'"

namespaceOverride: globals.st_namespace
fullnameOverride: local.namespaceOverride + "-cloudsec-policy-resident-init"
tenant_id: tenant.lcaas_id

image:
  repository: globals.gcr + "/prismacloud/cloud-api-service-resident-init"
  tag: '"ebdd512fc18c761781a48e1f3dfe6b9f9351aca2"'

env:
  RESIDENT: tenant.lcaas_id
  TENANT_ID: tenant.lcaas_id
  COMMON_INIT_ROAD_UNBLOCKED_FLAG_NAME: "'COMMON_INIT_ROAD_UNBLOCKED'"
  COMMON_INIT_VERSION: "'1.0.0'"
  CLOUDSEC_API_INIT_VERSION: "'1.0.0'"
  CLOUDSEC_RW_POSTGRES_USER:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres"'
        key: "'POSTGRES_CSPM_USERNAME'"
        optional: false
  CLOUDSEC_RW_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres-ps"'
        key: "'POSTGRES_CSPM_PASSWORD'"
        optional: false
  CLOUDSEC_COMMON_POSTGRES_API_SCHEMA: "'cloudsec_policy_management'"
  CLOUDSEC_COMMON_POSTGRES_SVC: "'postgres.xdr-mt.svc.cluster.local'"
  CLOUDSEC_COMMON_POSTGRES_PORT: "'5432'"
  CLOUDSEC_COMMON_POSTGRES_USER: "'root'"
  CLOUDSEC_COMMON_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cloudsec-secrets"'
        key: "'CLOUDSEC_COMMON_POSTGRES_PASSWORD'"
        optional: false