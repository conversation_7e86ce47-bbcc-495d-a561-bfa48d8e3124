apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: pipeline
  namespace: "{{ .globals.st_namespace }}"
spec:
  chart:
    spec:
      chart: tenant/pipeline
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
      interval: "{{ .globals.flux.chart_pull_interval }}"
{{- if not .tenant.is_metro_tenant }}
  dependsOn:
    - name: vsg
{{- end }}
  driftDetection:
    ignore:
      - paths:
          - /spec/replicas
        target:
          kind: Deployment
      - paths:
          - /spec/template/spec/containers/0/resources
        target:
          kind: Deployment
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    remediation:
      retries: -1
    disableWait: true
    createNamespace: true
  interval: 1m0s
  rollback:
    disableWait: true
  targetNamespace: "{{ .globals.st_namespace }}"
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
  values: {}
