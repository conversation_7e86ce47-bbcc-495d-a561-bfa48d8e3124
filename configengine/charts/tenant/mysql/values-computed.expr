# mysql standard
_mysql_cpu_lim_standard: '"5"'
_mysql_cpu_req_standard: '"5"'
_mysql_mem_lim_standard: '"32Gi"'
_mysql_mem_req_standard: '"32Gi"'
# mysql small epp
_mysql_cpu_lim_small: '"1"'
_mysql_cpu_req_small: '"200m"'
_mysql_mem_lim_small: '"10Gi"'
_mysql_mem_req_small: '"4Gi"'
# mysql non epp
_mysql_mem_req_non_epp: '"37Gi"'
_mysql_mem_lim_non_epp: '"37Gi"'
# mysql dev env
_mysql_mem_req_dev: '"6Gi"'
_mysql_mem_lim_dev: '"8Gi"'
_mysql_cpu_req_dev: '"400m"'
_mysql_cpu_lim_dev: '"1200m"'

_xpanse_aum_mysql_resource_map:
  default_resources:
    disk: '"250Gi"'
    memory_req: '"30Gi"'
    memory_limit: '"30Gi"'
    cpu_req: '"5"'
    cpu_limit: '"5"'
    innodb_buffer_pool_size: '"21Gi"'
  small_resources:
    disk: '"100Gi"'
    memory_req: '"20Gi"'
    memory_limit: '"20Gi"'
    cpu_req: '"3"'
    cpu_limit: '"3"'
    innodb_buffer_pool_size: '"14Gi"'
  small_medium_resources:
    disk: '"150Gi"'
    memory_req: '"25Gi"'
    memory_limit: '"25Gi"'
    cpu_req: '"4"'
    cpu_limit: '"4"'
    innodb_buffer_pool_size: '"18Gi"'
  medium_resources:
    disk: '"250Gi"'
    memory_req: '"30Gi"'
    memory_limit: '"30Gi"'
    cpu_req: '"5"'
    cpu_limit: '"5"'
    innodb_buffer_pool_size: '"21Gi"'
  medium_large_resources:
    disk: '"350Gi"'
    memory_req: '"35Gi"'
    memory_limit: '"35Gi"'
    cpu_req: '"6"'
    cpu_limit: '"6"'
    innodb_buffer_pool_size: '"22Gi"'

# mysql big pro
_mysql_mem_req_big_pro: 'infra_ff.enable_big_tenant ? "115Gi" : region.is_fedramp ? "55Gi" : "56Gi"'
_mysql_mem_lim_big_pro: 'infra_ff.enable_big_tenant ? "115Gi" : region.is_fedramp ? "55Gi" : "56Gi"'
_mysql_cpu_req_big_pro: 'infra_ff.enable_big_tenant ? "14" : region.is_fedramp ? "6" : "7"'
_mysql_cpu_lim_big_pro: 'infra_ff.enable_big_tenant ? "14" : region.is_fedramp ? "6" : "7"'

_mysql_dev_spec_enabled: "!(license.is_small_epp || infra_ff.enable_mysql_prod_spec || infra_ff.is_enable_prod_spec)"

_xpanse_aum_mysql_resource_requirements: |-
  license.xpanse_aum_count == 0 ? local._xpanse_aum_mysql_resource_map.default_resources : 
  license.xpanse_aum_count <= 10000 ? local._xpanse_aum_mysql_resource_map.small_resources : 
  license.xpanse_aum_count <= 100000 ? local._xpanse_aum_mysql_resource_map.small_medium_resources :
  license.xpanse_aum_count <= 500000 ? local._xpanse_aum_mysql_resource_map.medium_resources :
  license.xpanse_aum_count > 500001 ? local._xpanse_aum_mysql_resource_map.medium_large_resources : 
  local._xpanse_aum_mysql_resource_map.default_resources

_mysql_max_connections: |-
  license.is_small_epp || (region.viso_env == "dev" && !(infra_ff.enable_mysql_prod_spec || infra_ff.is_enable_prod_spec)) ? 500 :
  infra_ff.enable_megatron_xdr ? 8000 :
  infra_ff.enable_big_tenant ? 5000 : 4000

# resources
resources:
  requests:
    cpu: |-
      local._mysql_dev_spec_enabled ? local._mysql_cpu_req_dev :
      infra_ff.enable_megatron_xdr ? "30" : 
      license.pro_agents_for_scaling > 20000 ? local._mysql_cpu_req_big_pro :
      infra_ff.enable_megatron_xsoar ? 7 :
      license.is_xpanse ? local._xpanse_aum_mysql_resource_requirements.cpu_req :
      license.is_small_epp ? local._mysql_cpu_req_small :
      local._mysql_cpu_req_standard

    memory: |-
      local._mysql_dev_spec_enabled ? local._mysql_mem_req_dev :
      infra_ff.enable_megatron_xdr ? "230Gi" : 
      license.pro_agents_for_scaling > 20000 ? local._mysql_mem_req_big_pro :
      infra_ff.enable_megatron_xsoar ? "55Gi" :
      license.is_xpanse ? local._xpanse_aum_mysql_resource_requirements.memory_req :
      infra_ff.enable_pipeline ? local._mysql_mem_req_non_epp :
      license.is_small_epp ? local._mysql_mem_req_small :
      local._mysql_mem_req_standard

  limits:
    cpu: |-
      local._mysql_dev_spec_enabled ? local._mysql_cpu_lim_dev :
      infra_ff.enable_megatron_xdr ? "30" : 
      license.pro_agents_for_scaling > 20000 ? local._mysql_cpu_lim_big_pro :
      infra_ff.enable_megatron_xsoar ? 7 :
      license.is_xpanse ? local._xpanse_aum_mysql_resource_requirements.cpu_limit :
      license.is_small_epp ? local._mysql_cpu_lim_small :
      local._mysql_cpu_lim_standard

    memory: |-
      local._mysql_dev_spec_enabled ? local._mysql_mem_lim_dev :
      infra_ff.enable_megatron_xdr ? "230Gi" : 
      license.pro_agents_for_scaling > 20000 ? local._mysql_mem_lim_big_pro :
      infra_ff.enable_megatron_xsoar ? "55Gi" :
      license.is_xpanse ? local._xpanse_aum_mysql_resource_requirements.memory_limit :
      infra_ff.enable_pipeline ? local._mysql_mem_lim_non_epp :
      license.is_small_epp ? local._mysql_mem_lim_small :
      local._mysql_mem_lim_standard

fullnameOverride: 'globals.st_resource_prefix + "-mysql"'
namespaceOverride: globals.st_namespace

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "8.0.44-debian" : "8.0.43-debian"'
serviceAccount:
  name: "'mysql8'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

env:
  MYSQL_ROOT_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'mysql_password'"
        optional: false

persistence:
  storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
  size: |-
    infra_ff.is_enable_prod_spec && license.is_xpanse ? 
    local._xpanse_aum_mysql_resource_requirements.disk : 
    license.is_small_epp ? "20Gi" : 
    license.total_agents < 5000 ? "100Gi" : 
    "200Gi"

args:
  binlog_expire_logs_seconds: |-
    infra_ff.enable_megatron_xdr ? "7200" :
    (region.viso_env == "dev" && !infra_ff.is_enable_prod_spec) ? "60" :
    "10800"
  max_connections: local._mysql_max_connections
  innodb_buffer_pool_size: |-
    license.is_small_epp || (region.viso_env == "dev" && !(infra_ff.enable_mysql_prod_spec || infra_ff.is_enable_prod_spec)) ? "3Gi" :
    infra_ff.enable_megatron_xdr ? "180Gi" :
    infra_ff.enable_megatron_xsoar ? "38Gi" :
    (region.viso_env == "dev" && !infra_ff.is_enable_prod_spec && license.is_xsoar) ? "4Gi" :
    license.pro_agents_for_scaling > 20000 ? (infra_ff.enable_big_tenant ? "100Gi" : "39Gi") :
    license.is_xsoar ? "22Gi" :
    license.is_xpanse ? local._xpanse_aum_mysql_resource_requirements.innodb_buffer_pool_size : "22Gi"
  gtid-mode: 'infra_ff.enable_mysql_slave ? "ON" : nil'
  binlog-format: 'infra_ff.enable_mysql_slave ? "ROW" : nil'
  enforce-gtid-consistency: 'infra_ff.enable_mysql_slave ? "ON" : nil'
  binlog-do-db: 'infra_ff.enable_mysql_slave ? [tenant.lcaas_id + "_main", tenant.lcaas_id + "_user_data"] : nil'
  innodb_redo_log_capacity: |-
    infra_ff.enable_megatron_xdr ? "**********" :
    infra_ff.enable_megatron_xsoar ? "**********" :
    nil
  table-open-cache: 'infra_ff.enable_megatron_xdr ? "8000" : nil'
  innodb_log_buffer_size: |-
    (infra_ff.enable_megatron_xdr || infra_ff.enable_megatron_xsoar) ? "64M" :
    (license.is_small_epp || (region.viso_env == "dev" && !(infra_ff.enable_mysql_prod_spec || infra_ff.is_enable_prod_spec))) ? "6M" :
    "32M"
  tmp_table_size: '(infra_ff.enable_megatron_xdr || infra_ff.enable_megatron_xsoar) ? "102M" : "64M"'
  max_heap_table_size: '(infra_ff.enable_megatron_xdr || infra_ff.enable_megatron_xsoar) ? "102M" : "64M"'
  # 8+(max_connections/100) is the mysql default
  thread_cache_size: "(infra_ff.enable_megatron_xdr || infra_ff.enable_megatron_xsoar) ? 1000 : 8+(local._mysql_max_connections/100)"
  max_allowed_packet: 'license.is_small_epp ? "64M" : "180M"'
snapshots:
  hourlyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
  dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'

  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"mysql-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"mysql-hourly-" + tenant.lcaas_id'
