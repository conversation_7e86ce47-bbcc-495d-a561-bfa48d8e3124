_component_name: "'dspm-worker'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '"50m"'
    memory: '"256Mi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
    memory: '"512Mi"'

initContainers:
  initTemporal:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-worker"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-worker-feature-flags"'
      optional: false
