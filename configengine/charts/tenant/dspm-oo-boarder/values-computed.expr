_component_name: "'dspm-oo-boarder'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"
_temporal_url: 'globals.env.TEMPORAL_URL'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "100m" : "100m"'
    memory: '!infra_ff.is_enable_prod_spec ? "512Mi" : "512Mi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "300m" : "2"'
    memory: '!infra_ff.is_enable_prod_spec ? "768Mi" : "768Mi"'

env:
  ST_RESIDENT_ID: 'tenant.lcaas_id'
  TEMPORAL_URL: local._temporal_url
  QUEUE_ID: '"projects/" + tenant.project_id + "/subscriptions/dspm-oo-boarder-cloud-accounts-" + tenant.lcaas_id + "-sub"'
  SIS_INTERNAL_URL: '"http://classi-sis." + local.namespaceOverride + ".svc.cluster.local:8000"'
  JOSE_INTERNAL_URL: '"http://dspm-crespo-jose-svc." + local.namespaceOverride + ".svc.cluster.local"'
  CORTEX_BASE_PATH: 'globals.env.CORTEX_PLATFORM_URI'
  DATASOURCE_BASE_URI: '"http://dspm-datasource-svc." + local.namespaceOverride + ".svc.cluster.local"'
  HEALTH_MONITORING_STATUSES_TOPIC_NAME: '"projects/" + tenant.project_id + "/topics/cloud-health-monitoring-statuses-" + tenant.lcaas_id'
  DB_URL:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'mongodb_connection_string'"
        optional: false
  AZURE_SECRET:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'azure_secret'"
        optional: false
  DB_SECRET_ARN:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'db_secret_arn'"
        optional: false
envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: local._temporal_url
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: local._temporal_url
      - name: "'TEMPORAL_NAMESPACE'"
        value: '"dspm-outpost-orchestrator"'
