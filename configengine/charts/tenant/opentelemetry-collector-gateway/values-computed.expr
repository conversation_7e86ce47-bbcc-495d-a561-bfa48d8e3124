namespaceOverride: globals.monitoring_namespace
fullnameOverride: '"opentelemetry-collector"'

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "0.115.1-amd64" : "0.115.1-amd64"'

configmap:
  nameOverride: '"monitoring-" + tenant.lcaas_id + "-configmap"'

deploymentAnnotations:
  configmap.reloader.stakater.com/reload: '"monitoring-" + tenant.lcaas_id + "-configmap-opentelemetry-collector"'

serviceAccount:
  name: local.fullnameOverride
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

extraVolumes:
  - name: '"opentelemetry-collector-configmap"'
    configMap:
      name: '"monitoring-" + tenant.lcaas_id + "-configmap-opentelemetry-collector"'
      defaultMode: 511
      optional: false

rbac:
  nameOverride: 'local.fullnameOverride + "-" + tenant.lcaas_id'
