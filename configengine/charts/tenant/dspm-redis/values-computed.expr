_redis_large_tenant: "license.total_agents > 10000"
_redis_mem_req_large: 'infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi"'
_redis_mem_lim_large: 'infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi"'
_dspm_redis_memory_limit: 'local._redis_large_tenant ? local._redis_mem_lim_large : "2Gi"'

_securityContext:
  fedramp:
    runAsUser: 999
    runAsGroup: 1000
    fsGroup: 1000

fullnameOverride: 'globals.st_resource_prefix + "-dspm-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  DSPM_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'globals.tenant_secrets'
        key: "'dspm_redis_password'"
        optional: false

resources:
  requests:
    cpu: '"0.3"'
    memory: '"2Gi"'
  limits:
    cpu: 'infra_ff.enable_megatron_xdr ? "1.2" : "1"'
    memory: local._dspm_redis_memory_limit

securityContext: "region.is_fedramp ? local._securityContext.fedramp : nil"
persistence:
  storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
  size: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "50Gi" : "16Gi") : "6Gi"'
