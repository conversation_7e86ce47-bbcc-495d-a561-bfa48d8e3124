_redis_large_tenant: "license.total_agents > 10000"

_podSecurityContext:
  runAsUser: 999
  runAsGroup: 1000
  fsGroup: 1000
  runAsNonRoot: false

_nodeSelector:
  xdr-pool: "'wi-static'"

_tolerations:
  - key: "'xdr-pool'"
    operator: "'Equal'"
    value: "'wi-static'"

_image:
  registry: "globals.gcr"

namespaceOverride: globals.st_namespace

redis:
  image: "local._image"
  fullnameOverride: 'globals.st_resource_prefix + "-redis"'
  namespaceOverride: local.namespaceOverride
  env:
    REDISCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: "globals.tenant_secrets"
          key: "'redis_password'"
          optional: false
  resources:
    requests:
      cpu: 'license.is_small_epp || tenant.is_metro_tenant ? "100m" : "0.2"'
      memory: |
        license.is_small_epp ? "1Gi" :
        local._redis_large_tenant || (infra_ff.enable_megatron_xsoar && license.is_xsoar) ? "8Gi" :
        tenant.is_metro_tenant ? "1.6G" :
        license.is_xsoar ? (!infra_ff.is_enable_prod_spec ? "3Gi" : "4Gi") :
        "2Gi"
    limits:
      cpu: 'infra_ff.enable_megatron_xdr ? "1.2" : "1"'
      memory: |
        local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") :
        (infra_ff.enable_megatron_xsoar && license.is_xsoar) ? "8Gi" :
        license.is_xsoar ? (!infra_ff.is_enable_prod_spec ? "3Gi" : "4Gi") :
        "2Gi"
  nodeSelector: local._nodeSelector
  tolerations: local._tolerations
  podSecurityContext: local._podSecurityContext
  persistence:
    storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
    size: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "50Gi" : "16Gi") : "6Gi"'
  args:
    maxclients: 'infra_ff.enable_megatron_xdr ? "30000" : "20000"'
    client-output-buffer-limit: 'license.is_xsoar ? "pubsub 256MB 128MB 240" : nil'

  snapshots:
    dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
    hourlyEnabled: 'region.viso_env != "dev"'
    daily:
      app_label: local.redis.fullnameOverride
      retention_days: 30
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"redis-daily-" + tenant.lcaas_id'
    hourly:
      app_label: local.redis.fullnameOverride
      retention_days: 2
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"redis-hourly-" + tenant.lcaas_id'

_analytics-redis:
  enabled: true
  image: "local._image"
  fullnameOverride: 'globals.st_resource_prefix + "-analytics-redis"'
  namespaceOverride: local.namespaceOverride
  env:
    REDISCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: "globals.tenant_secrets"
          key: "'analytics_redis_password'"
          optional: false
  resources:
    requests:
      cpu: 'license.is_small_epp || tenant.is_metro_tenant ? "100m" : "0.3"'
      memory: 'license.is_small_epp ? "1Gi" : local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2Gi"'
    limits:
      cpu: 'infra_ff.enable_megatron_xdr ? "1.2" : "1"'
      memory: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2Gi"'
  nodeSelector: local._nodeSelector
  tolerations: local._tolerations
  podSecurityContext: local._podSecurityContext
  persistence:
    storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
    size: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "50Gi" : "16Gi") : "6Gi"'

  snapshots:
    dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
    hourlyEnabled: 'region.viso_env != "dev"'
    daily:
      app_label: local["_analytics-redis"].fullnameOverride
      retention_days: 30
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"analytics-redis-daily-" + tenant.lcaas_id'
    hourly:
      app_label: local["_analytics-redis"].fullnameOverride
      retention_days: 2
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"analytics-redis-hourly-" + tenant.lcaas_id'

analytics-redis: 'infra_ff.enable_redis_split ? local["_analytics-redis"] : nil'

_gonzo-redis:
  enabled: true
  image: "local._image"
  fullnameOverride: 'globals.st_resource_prefix + "-gonzo-redis"'
  namespaceOverride: local.namespaceOverride
  env:
    REDISCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: "globals.tenant_secrets"
          key: "'gonzo_redis_password'"
          optional: false
  resources:
    requests:
      cpu: 'license.is_small_epp || tenant.is_metro_tenant ? "100m" : "0.3"'
      memory: 'license.is_small_epp ? "1Gi" : local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2Gi"'
    limits:
      cpu: 'infra_ff.enable_megatron_xdr ? "1.2" : "1"'
      memory: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2Gi"'
  nodeSelector: local._nodeSelector
  tolerations: local._tolerations
  podSecurityContext: local._podSecurityContext
  persistence:
    storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
    size: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "50Gi" : "16Gi") : "6Gi"'

  snapshots:
    dailyEnabled: false
    hourlyEnabled: false
    daily:
      app_label: local["_gonzo-redis"].fullnameOverride
      retention_days: 30
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"gonzo-redis-daily-" + tenant.lcaas_id'
    hourly:
      app_label: local["_gonzo-redis"].fullnameOverride
      retention_days: 2
      on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
      name: '"gonzo-redis-hourly-" + tenant.lcaas_id'

gonzo-redis: "nil"

_gonzo-dragonfly:
  enabled: true
  image:
    registry: "globals.gcr"
    tag: 'region.is_fedramp ? "v1.35.1" : "v0.15.1-updated-os-2020"'
  fullnameOverride: 'globals.st_resource_prefix + "-gonzo-dragonfly"'
  namespaceOverride: local.namespaceOverride
  env:
    REDISCONF_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: "globals.tenant_secrets"
          key: "'gonzo_redis_password'"
          optional: false
  resources:
    requests:
      cpu: 'local._redis_large_tenant || infra_ff.enable_redis_split ? "2" : "0.3"'
      memory: 'local._redis_large_tenant || infra_ff.enable_redis_split ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2.5Gi"'
    limits:
      cpu: "'4'"
      memory: 'local._redis_large_tenant || infra_ff.enable_redis_split ? (infra_ff.enable_megatron_xdr ? "16Gi" : "10Gi") : "4Gi"'
  nodeSelector: local._nodeSelector
  tolerations: local._tolerations

gonzo-dragonfly: 'globals.gonzo_redis ? local["_gonzo-dragonfly"] : nil'
