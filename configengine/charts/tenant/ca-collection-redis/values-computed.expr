_securityContext:
  fedramp:
    runAsUser: 999
    runAsGroup: 1000
    fsGroup: 1000

fullnameOverride: 'globals.st_resource_prefix + "-ca-collection-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'ca_collection_redis_password'"
        optional: false

resources:
  requests:
    cpu: '"1"'
    memory: 'infra_ff.enable_megatron_xdr ? "30Gi" : "3Gi"'
  limits:
    cpu: '"1"'
    memory: 'infra_ff.enable_megatron_xdr ? "30Gi" : "3Gi"'

securityContext: "region.is_fedramp ? local._securityContext.fedramp : nil"
persistence:
  storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
  size: 'infra_ff.enable_megatron_xdr ? "50Gi" : "6Gi"'

snapshots:
  dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
  hourlyEnabled: false
  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"ca-collection-redis-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"ca-collection-redis-hourly-" + tenant.lcaas_id'
