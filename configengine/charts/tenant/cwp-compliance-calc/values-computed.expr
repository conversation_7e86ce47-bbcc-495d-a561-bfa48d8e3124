_component_name: "'cwp-compliance-calc'"

fullnameOverride: 'globals.st_resource_prefix + "-cwp-compliance-calc"'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, 'cwp-compliance-xdr-calc')"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  CWP_REDIS_HOST: globals.env.REDIS_CWP_URI
  CWP_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cwp-secrets"'
        key: '"cwp_redis_password"'
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
  limits:
    cpu: '"200m"'
    memory: '"512Mi"'
