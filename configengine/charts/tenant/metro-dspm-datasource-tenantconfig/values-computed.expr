tenantconfig:
  lcaasId: 'tenant.lcaas_id'
  secretStoreName: "globals.metro_namespace"
  pushSecretRemoteKey: '"dspm-datasource-tenantconfigs"'
  config:
    cortex:
      lcass: 'tenant.lcaas_id'
      onboarding:
        lcaas: 'tenant.lcaas_id'
        project-id: 'tenant.project_id'
      platform:
        api-uri: '"http://xdr-st-" + tenant.lcaas_id + "-api." + globals.st_namespace + ".svc.cluster.local:4999"'
      cwp:
        scan-result-subscription-name: '"projects/" + tenant.project_id + "/subscriptions/dspm-datasource-scan-results-sub-" + tenant.lcaas_id'
        sp-lifecycle-events-subscription-name: '"projects/" + tenant.project_id + "/subscriptions/dspm-datasource-sp-lifecycle-events-sub-" + tenant.lcaas_id'
      dashboard:
        pubsub:
          account-events-subscription-name: '"projects/" + tenant.project_id + "/subscriptions/dspm-datasource-dashboard-account-sub-" + tenant.lcaas_id'
          onboarding-topic: '"projects/" + tenant.project_id + "/topics/dspm-datasource-onboarding-" + tenant.lcaas_id'
          scan-now-topic: '"projects/" + tenant.project_id + "/topics/dspm-datasource-scan-now-" + tenant.lcaas_id'
    metro:
      tenants:
        - id: 'tenant.lcaas_id'


















