_component_name: "'dspm-voyager'"
_component_image: "'dspm-voyager'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '"1Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "1" : "3"'
    memory: '"1Gi"'

env:
  LOG_LEVEL: '"INFO"'
  JAVA_OPTS: '"-Xms128m -Xmx1024m"'
  CONSOLE_LOG_STRUCTURED_FORMAT: '"logstash"'
  SPRING_CLOUD_GCP_BIGQUERY_DATASET_NAME: '"dspm_voyager_" + tenant.lcaas_id'
  SPRING_TEMPORAL_CONNECTION_TARGET: 'globals.env.TEMPORAL_URL'
  CORTEX_LCAAS: tenant.lcaas_id
  CORTEX_PUBSUB_LISTING_SUBSCRIPTION: '"dspm-voyager-listing-sub-" + tenant.lcaas_id'
  CORTEX_PUBSUB_SHARED_LINKS_SUBSCRIPTION: '"dspm-voyager-shared-links-sub-" + tenant.lcaas_id'
  CORTEX_PUBSUB_ONBOARDING_SUBSCRIPTION: '"dspm-voyager-onboarding-sub-" + tenant.lcaas_id'
  CORTEX_FDA_TOPIC_NAME: '"dspm-listing-data-" + tenant.lcaas_id'
  CORTEX_FDA_BUCKET_NAME: 'tenant.project_id + "-dspm-content-bouncer-data"'
  DSPM_LISTING_RESULTS_TOPIC: '"dspm-voyager-listing-result-" + tenant.lcaas_id'
  DSPM_LISTING_DATA_BUCKET: 'tenant.project_id + "-dspm-voyager-listing-data"'
  PLATFORM_METRO_PROJECT_ID: 'tenant.project_id'
  METRO_TENANTS_0_ID: 'tenant.lcaas_id'
  CORTEX_GCP_FACADE_SERVICE_ACCOUNT:
    valueFrom:
      secretKeyRef:
        key: "'facade-service-account'"
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        optional: false
  SPRING_DATA_MONGODB_URI:
    valueFrom:
      secretKeyRef:
        key: "'mongodb_connection_string_voyager'"
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        optional: false
  EGRESSPROXY_PORT: "globals.egress_proxy_port"
  PROXY_IPS:
    valueFrom:
     configMapKeyRef:
        name: 'tenant.lcaas_id + "-configmap-dspm"'
        key: "'CLOUDONBOARDING_AZURE_STORAGE_ALLOWED_IPS'"
        optional: false
        
        
envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false
      
_cert_command: |-
  [
    "cp -R /opt/java/openjdk/lib/security/* /cacerts/ &&",
    "awk 'BEGIN {c=0;} /-----BEGIN CERTIFICATE-----/ {filename=sprintf(\"/tmp/cert_%d.crt\", c++);} {print > filename}' /etc/certs/egress.crt &&",
    "i=0; for certfile in /tmp/cert_*.crt; do keytool -import -noprompt -trustcacerts -alias \"proxy_crt_$i\" -file \"$certfile\" -keystore /cacerts/cacerts -storepass changeit 2>/dev/null; i=$((i + 1)); done &&",
    "rm -f /tmp/cert_*.crt"
  ]
        
initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'

  initCert:
    image:
      registry: globals.gcr
      tag: "local._images.component.tag ?? local._images.family.tag"
      repository: "local._images.component.repository ?? local._images.family.repository"
    command:
      - "'sh'"
      - "'-c'"
      - join(local._cert_command, "\n") + "\n"


  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
      - name: "'TEMPORAL_NAMESPACE'"
        value: "'voyager'"

