_component_name: "'cwp-scan-spec-manager'"

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  CWP_SCAN_SPEC_MANAGER_CHANGE_FEED_SUBSCRIPTION_NAME: '"cwp-scan-spec-manager-asset-change-feed-" + tenant.lcaas_id + "-sub"'
  CWP_SCAN_SPEC_MANAGER_POSTGRES_DB_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cwp-secrets"'
        key: "'postgres_password'"
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"cwp-spec-manager@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
  limits:
    cpu: '"1"'
    memory: '"1Gi"'
