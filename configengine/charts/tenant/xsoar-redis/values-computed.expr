_securityContext:
  fedramp:
    runAsUser: 999
    runAsGroup: 1000
    fsGroup: 1000

fullnameOverride: 'globals.st_resource_prefix + "-xsoar-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"' 

env:
  XSOAR_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'xsoar_redis_password'"
        optional: false

resources:
  requests:
    cpu: 'globals.is_xsoar_legacy_spec ? "0.3" : "0.15"'
    memory: 'infra_ff.enable_megatron_xsoar ? "8Gi" : globals.is_xsoar_legacy_spec ? "2Gi" : "0.8Gi"'
  limits:
    cpu: '"1"'
    memory: 'infra_ff.enable_megatron_xsoar ? "8Gi" : "4Gi"'

securityContext: "region.is_fedramp ? local._securityContext.fedramp : nil"
