apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: metro-dspm-dacs-tenantconfig
  namespace: "{{ .globals.st_namespace }}"
spec:
  chart:
    spec:
      chart: tenant/metro-dspm-dacs-tenantconfig
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
      interval: "{{ .globals.flux.chart_pull_interval }}"
  driftDetection:
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    disableWait: true
  interval: 1m0s
  rollback:
    disableWait: true
  targetNamespace: "{{ .globals.st_namespace }}"
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
  values: {}
