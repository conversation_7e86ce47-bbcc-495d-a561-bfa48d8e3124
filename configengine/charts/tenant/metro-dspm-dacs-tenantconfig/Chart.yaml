apiVersion: v2
name: metro-dspm-dacs-tenantconfig
description: A Helm chart for Kubernetes
type: application
version: 0.3.0
appVersion: "0.2.12"
dependencies:
  - name: dspm-tenantconfig
    alias: tenantconfig
    version: "*.*.*"
    repository: "file://../../common/dspm-tenantconfig"
  - name: dspm-tenantconfig-externalsecret
    alias: externalsecret
    version: "*.*.*"
    repository: "file://../../common/dspm-tenantconfig-externalsecret"
annotations:
  "panw.com/deploy-eval": |-
    tenant.is_metro_tenant && (
    globals.product_code_c1 || 
    globals.product_code_c3 || 
    globals.product_code_x0 || 
    globals.product_code_x1 || 
    globals.product_code_x3 || 
    globals.product_code_x5 || 
    globals.enable_cloud_posture )
  owner.panw/group: dspm
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/metro-dspm-dacs-tenantconfig
  owner.panw/team: dig_ddr
  owner.panw/team-slack-handle: '@dig_ddr'
  owner.panw/people-slack-handle-team-lead: '@decohen'
  owner.panw/people-slack-handle-owners-group: '@iazran'
