_db:
  name: '"dspm_" + tenant.lcaas_id'
  user: '"root"'
  schema: '"data_classification_settings"'
  url: '"jdbc:postgresql://" + globals.env.PGHOST + "/" + local._db.name + "?currentSchema=" + local._db.schema'

tenantconfig:
  lcaasId: 'tenant.lcaas_id'
  secretStoreName: "globals.metro_namespace"
  pushSecretRemoteKey: '"metro-dspm-dacs-tenantconfigs"'
  config:
    metro:
      config:
        activate:
          on-tenant: tenant.lcaas_id
  
    big-query-writer:
      entity-metadata:
        projectId: 'tenant.project_id'
        dataSetId: '"classification_mgmt_" + tenant.lcaas_id'
    dashboard:
      global-settings-update-topic: '"projects/" + tenant.project_id + "/topics/" + "classification-mgmt-global-settings-update-" + tenant.lcaas_id'
  
    data-modification-request-listener:
      pubsub-data-modification-topics:
        data-pattern-update-topic: '"projects/" + tenant.project_id + "/topics/" + "classification-mgmt-data-pattern-update-" + tenant.lcaas_id'
        profile-update-topic: '"projects/" + tenant.project_id + "/topics/" + "classification-mgmt-profile-update-" + tenant.lcaas_id'
  
      pubsub-data-modification-request-subscriptions:
        profile-data-modification-pubsub-subscription: '"projects/" + tenant.project_id + "/subscriptions/" + "classification-mgmt-profile-modification-request-sub-" + tenant.lcaas_id'
        data-pattern-data-modification-pubsub-subscription: '"projects/" + tenant.project_id + "/subscriptions/" + "classification-mgmt-data-pattern-modification-request-sub-" + tenant.lcaas_id'
        information-protection-label-data-modification-pubsub-subscription: '"projects/" + tenant.project_id + "/subscriptions/" + "classification-mgmt-ipl-modification-request-sub-" + tenant.lcaas_id'
  
    data-modification-update-request-topic-properties:
      data-pattern-topic: '"projects/" + tenant.project_id + "/topics/" + "classification-mgmt-data-pattern-modification-request-topic-" + tenant.lcaas_id'
      profile-topic: '"projects/" + tenant.project_id + "/topics/" + "classification-mgmt-profile-modification-request-topic-" + tenant.lcaas_id'
      ipl-topic: '"projects/" + tenant.project_id + "/topics/" +"classification-mgmt-ipl-modification-request-topic-" + tenant.lcaas_id'
  
    mip-listener:
      dspm-mip-update-subscription: '"projects/" + tenant.project_id + "/subscriptions/" + "dspm-information-protection-label-mip-update-sub-" + tenant.lcaas_id'
      ipl-datasource-onboarding-request-pubsub-subscription: '"projects/" + tenant.project_id + "/subscriptions/" + "classification-mgmt-datasource-onboarding-sub-" + tenant.lcaas_id'


externalsecret:
  lcaasId: 'tenant.lcaas_id'
  secretStoreName: "globals.metro_namespace"
  pushSecretRemoteKey: '"metro-dspm-dacs-tenantconfigs"'
  targetSecretKey: '"metro-datasource"'

  secrets:
    - secretKey: '"datasource_password"'
      configPath: '"metro.datasource.password"'
      remoteRef:
        key: 'tenant.lcaas_id + "-dspm-secrets"'
        property: '"postgres_password"'
  
  config:
    metro:
      config:
        activate:
          on-tenant: tenant.lcaas_id
      datasource:
        url: 'local._db.url'
        password: '"secrets.datasource_password"'
        username: '"root"'
