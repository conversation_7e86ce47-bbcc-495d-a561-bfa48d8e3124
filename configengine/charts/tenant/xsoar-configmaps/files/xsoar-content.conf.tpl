{
        "Server": {
                "ExternalHostname": "{{ .externalFqdn }}",
                "HttpPort":"8080",
                "XsoarNG": {
                    "enabled": {{ .isXsoar }}
                },
                "xsiam": {
                    "enabled": {{ or .isXsiam .isXpanse }}
                },
                "remote": {
                    "type": "server"
                },
                "externalEntities": "audit, incident, configuration",

                "OneTimeConfPath": "/usr/local/demisto/otc.conf.json",
                "CloudStorage": {
                    "BucketName": "{{ .xsoarConfBucketName }}",
                    "ParentPath": "xsoar",
                    "Platform": "Google-Storage"
                }
        },
        "ContentSecrets": {
                "WildFire-Reports": {
                    "token": "{{ .hashedWildfireKey }}"
                },
                "Http_Connector": {
                    "token": "{{ .hashedXdrHttpToken}}",
                    "url": "{{ .externalFqdn }}"
                }
        },
        "elasticsearch": {
               "debug": {
                   "enableQuery": true
                 },
               "enabled": true,
               "indexPrefix": "{{ .elasticIndexPrefix }}",
               "maxResultWindow": 10000,
               "proxy": false,
               "refreshOnBulkInsert": true,
               "responseHeaderTimeoutSeconds": 30,
               "username": "elastic",
               "url": "{{ .elasticsearchUrl }}",
               "insecure": true
        },
        "Monitoring.monitoringKey": "{{ .hashedMonitoringKey }}",
        "defaultEngines": [{"id": "{{ .engineId }}","name": "DefaultRunner","isDefault": true}],
        "workers.count.Tasks": "{{ .workersCount }}",
        "content.validate.docker.images": false,
        "marketplace.initial.sync.delay": 0,
        {{- if .isXsoar }}
        "marketplace.bootstrap.bypass.url": "{{ .marketplaceBootstrapBypassUrl }}",
        "marketplace.bootstrap.url": "{{ .marketplaceBootstrapBypassUrl }}",
        {{- end }}
        {{- if .isXsoar6Migration }}
        "migration.filesEncryption.enabled": true,
        "migration.filesEncryption.keyInBase64": "{{ .migrationToken }}",
        {{- end }}
        "http.tunnel.over.ws.url": "{{ .wsUrl }}",
        "external.saml.session.sso.endpoint": "{{ .marketplaceSsoEndpoint }}",
        "marketplace.premium.gateway.service.url":"{{ .marketplacePremiumContentGatewayServiceUrl}}",
        "marketplace.subscription.service.url": "{{ .marketplaceSubscriptionServiceUrl}}",
        "auth.jwt.enabled": true,
        "folders.temp": "/home/<USER>/temp",
        "data.collection.external.ask.parent.route": "/external/ask",
        "data.collection.external.form.parent.route": "/external/form",
        "container.engine.type": "podman",
        "custom.fields.validate.grid.values": true,
        "db.index.entry.disable": true,
        "unit42intel.service.url": "{{ .unit42intelServiceUrl }}",
        "server.engine.base.url": "api-{{ .externalFqdn }}/xsoar",
        "allow.all.get.license.fields": true,
        "allow.custom.internal.apicalls": true,
        "xsoarconf.ng.gcp.git": {{ .gcpGit }},
        "xsoarconf.ng.vc.branch": "{{ .repoBranch }}",
        "xsoarconf.ng.vc.enabled": "{{ .enableVc }}",
        "xsoarconf.ng.vc.git.url": "{{ .gitUrl }}",
        "xsoarconf.ng.is.dev": "{{ .customerDevTenant }}"
}