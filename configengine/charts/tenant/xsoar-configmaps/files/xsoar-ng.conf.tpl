{
        "Server": {
                "ExternalHostname": "{{ .externalFqdn }}",
                "HttpPort":"8080",
                "XsoarNG": {
                    "enabled": {{ .isXsoar }}
                },
                "xsiam": {
                    "enabled": {{ or .isXsiam .isXpanse }}
                },
                "remote": {
                    "type": "server"
                },
                "externalEntities": "audit, incident, configuration",
                "confDefinitions":{
                    "useOverrideFile":true
                },
                "OneTimeConfPath": "/usr/local/demisto/otc.conf.json",
                "CloudStorage": {
                    "BucketName": "{{ .xsoarConfBucketName }}",
                    "ParentPath": "xsoar",
                    "Platform": "Google-Storage"
                }
        },
        "ContentSecrets": {
                "WildFire-Reports": {
                    "token": "{{ .hashedWildfireKey }}"
                },
                "Http_Connector": {
                    "token": "{{ .hashedXdrHttpToken }}",
                    "url": "{{ .externalFqdn }}"
                }
        },
        "elasticsearch": {
               "debug": {
                   "enableQuery": true
               },
               "periodicalBackup" : {
                   "enabled": {{ .backupEnable }},
                   "bucket_name": "{{ .backupBucket }}",
                   "repoName" : "{{ .elasticSnapshotRepoName }}",
                   "snapshotName": "es-backup",
                   "schedule" : "{{ .backupSchedule }}",
                   "retention.expireAfter" : "30d",
                   "retention.minCount" : 5,
                   "retention.maxCount" : 50
               },
               "defaultReplicasPerIndex":0,
               "enabled": true,
               "indexPrefix": "{{ .elasticIndexPrefix }}",
               "maxResultWindow": 10000,
               "proxy": false,
               "refreshOnBulkInsert": true,
               "responseHeaderTimeoutSeconds": 30,
               "username": "{{ .elasticsearchUser }}",
               "url": "{{ .elasticsearchUrl }}",
               "insecure": true
        },
        "engine.load.module.timeout": 30,
        "Monitoring.monitoringKey": "{{ .hashedMonitoringKey }}",
        "defaultEngines": [{"id": "{{ .engineId }}","name": "DefaultRunner","isDefault": true}],
        "workers.count.Tasks": "{{ .workersCount }}",
        "marketplace.initial.sync.delay": 0,
        {{- if .isXsoar }}
        "marketplace.bootstrap.bypass.url": "{{ .marketplaceBootstrapBypassUrl }}",
        "marketplace.bootstrap.url": "{{ .marketplaceBootstrapBypassUrl }}",
        {{- end }}
        {{- if .isXpanse }}
        "task.clear.non.executed.enabled" : true,
        "investigation.task.partial.index": 7,
        "application.statistics.update.disable" : true,
        {{- end }}
        "http.tunnel.over.ws.url": "{{ .wsUrl }}",
        {{- if .isXsoar6Migration }}
        "migration.filesEncryption.enabled": true,
        "migration.filesEncryption.keyInBase64": "{{ .migrationToken }}",
        "legacyEngines": [{"id": "{{ .gcpconfProjectId }}-engine-1000", "name": "Legacy IP Engine"}],
        {{- end }}
        {{- if .isXsoar }}
        "http.tunnel.over.ws.domain.whitelist": "{{ .xsoarDomainWhitelist }}",
        "http.tunnel.over.ws.port.whitelist": "{{ .xsoarPortWhitelist }}",
        {{- end }}
        "external.saml.session.sso.asc": "{{ .marketplaceSsoAsc }}",
        "external.saml.session.sso.endpoint": "{{ .marketplaceSsoEndpoint }}",
        "marketplace.premium.gateway.service.url":"{{ .marketplacePremiumContentGatewayServiceUrl }}",
        "marketplace.subscription.service.url": "{{ .marketplaceSubscriptionServiceUrl }}",
        "auth.jwt.enabled": true,
        "folders.temp": "/home/<USER>/temp",
        "data.collection.external.ask.parent.route": "/external/ask",
        "data.collection.external.form.parent.route": "/external/form",
        "container.engine.type": "podman",
        "custom.fields.validate.grid.values": true,
        "db.index.entry.disable": true,
        "unit42intel.service.url": "{{ .unit42intelServiceUrl }}",
        "server.engine.base.url": "api-{{ .externalFqdn }}/xsoar",
        "allow.all.get.license.fields": true,
        "allow.custom.internal.apicalls": true,
        "repo.mysql": true,
        "repo.olap.incidents": true,
        "repo.mysql.investigations": true,
        "server.inv.playbook.cache.enabled": false,
        "tim.mysql.replicate.indicators.failures.subscription": "{{ .indicatorsFailuresSubscription }}",
        "tim.mysql.replicate.indicators.failures.topic": "{{ .indicatorsFailuresTopic }}",
        "xsoar.ng.engine.hub.enabled": true,
        "xsoar.ng.playbook.topic.start": "playbook-runner-start",
        "xsoar.ng.playbook.publisher": true,
        "xsoarconf.ng.content.url": "{{ .ngContentUrl }}",
        "xsoarconf.ng.gcp.git": {{ .gcpGit }},
        "xsoar.ng.redis.message.size": "6000000",
        "xsoarconf.ng.vc.branch": "{{ .repoBranch }}",
        "xsoarconf.ng.vc.enabled": "{{ .enableVc }}",
        "xsoarconf.ng.vc.external.content.url": "https://api-{{ .parentExternalFqdn }}/xsoar/vc/bundle",
        "xsoarconf.ng.vc.git.url": "{{ .gitUrl }}",
        "xsoarconf.ng.is.dev": "{{ .customerDevTenant }}"
}

