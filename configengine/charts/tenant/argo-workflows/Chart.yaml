annotations:
  artifacthub.io/changes: |
    - kind: fixed
      description: Make Argo Agent and Artifact GC permissions optional for workflows SA
  artifacthub.io/signKey: |
    fingerprint: 2B8F22F57260EFA67BE1C5824B11F800CD9D2252
    url: https://argoproj.github.io/argo-helm/pgp_keys.asc
  "panw.com/deploy-eval": |
    !tenant.is_metro_tenant && (infra_ff.enable_cortex_platform && 
    !globals.product_code_agentix)
  owner.panw/group: cas
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/argo-workflows
  owner.panw/team: devops
  owner.panw/team-slack-handle: '@cas-devops-oncall'
  owner.panw/people-slack-handle-team-lead: '@navni'
  owner.panw/people-slack-handle-owners-group: '@lbecker'
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"

apiVersion: v2
appVersion: "v3.6.2"
description: A Helm chart for Argo Workflows
home: https://github.com/argoproj/argo-helm
icon: https://argo-workflows.readthedocs.io/en/stable/assets/logo.png
maintainers:
  - name: argoproj
    url: https://argoproj.github.io/
name: argo-workflows
sources:
  - https://github.com/argoproj/argo-workflows
type: application
version: 0.45.4
