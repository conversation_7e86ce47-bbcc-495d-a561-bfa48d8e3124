fullnameOverride: ""
namespaceOverride: ""
configmap: {}

apps:
  - name: fda
    enabled: true
  - name: ace
    enabled: true
  - name: ack
    enabled: true
  - name: cb
    enabled: true
  - name: mac
    enabled: true
  - name: mac-worker
    enabled: true
  - name: x-files
    enabled: true
  - name: dpc
    enabled: true
  - name: worker
    enabled: true
  - name: das
    enabled: true
  - name: bigquery-migrator
    enabled: true

config:
  DIG_INFRA_PRODUCT: CORTEX
  DIG_INFRA_CLOUD: GCP
  CORTEX_PLATFORM_SERVICE_AUDIENCE: cortex.platform

fdaConfig:
  FDA_INTERNAL_API_PASSWORD: temp
  FDA_INTERNAL_API_USERNAME: user
  SPRING_DATA_MONGODB_AUTHENTICATION_DATABASE: admin
  SPRING_DATA_MONGODB_DATABASE: dspm-fda
  SPRING_DATA_MONGODB_PORT: "27017"
  SPRING_DATA_MONGODB_USERNAME: root
  SPRING_DATA_REDIS_PORT: "6379"
  SPRING_TEMPORAL_NAMESPACE: dspm-fda

cbConfig:
  ECHO_BRIDGE_URL: http://dummy:8888
  FDA_PASSWORD: temp
  FDA_USER: user
  TEMPORAL_NAMESPACE_CB: dspm-cb

macConfig: {}

mac-workerConfig:
  init: init

x-filesConfig:
  DB_NAME: dspm-x-files
  REDIS_PORT: "6379"
  TEMPORAL_NAMESPACE_X_FILES: dspm-x-files
  TEMPORAL_QUEUE_NAME: x_files_queue
  TEMPORAL_TLS: "false"

dpcConfig:
  APP__TEMPORAL__NAMESPACE: "dspm-dpc"

workerConfig:
  APP__TEMPORAL__NAMESPACE: "dspm-worker"
  CORTEX_PLATFORM_LOCAL_SERVICE_AUDIENCE: "cortex.platform.local"