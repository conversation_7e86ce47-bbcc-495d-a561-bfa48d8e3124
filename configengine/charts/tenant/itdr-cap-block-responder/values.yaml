# values.yaml
deployment:
  strategy:
    type: Recreate

replicas: 1

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

ports:
  - containerPort: 8080
    protocol: TCP
  - containerPort: 9091
    protocol: TCP

serviceAccount:
  create: true
  automountServiceAccountToken: false
  name: itdr-cap-block-responder

terminationGracePeriodSeconds: 60

nodeSelector:
  xdr-pool: wi-dynamic

service:
  create: true
  annotations: {}

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  initialDelaySeconds: 10
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1
readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  initialDelaySeconds: 10
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
startupProbe:
  failureThreshold: 60
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  initialDelaySeconds: 10
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 2

hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 5

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: itdr
  team: sonic
podLabels:
  group: itdr
  team: sonic

podAnnotations:
  prometheus.io/port: "9091"
  prometheus.io/scrape: "true"
  prometheus.io/path: "/metrics"

command:
  - ./itdr-cap-block-responder

env:
  POSTGRES_USERNAME: "root"
  CWP_REDIS_PORT: "6379"
  ITDR_CAP_RESPONDER_REDIS_DATABASE: "0"
  XSOARCONF_HOST_XSOAR_API: "***********:5566"
  XSOARCONF_AUTH_KEY:
    valueFrom:
      secretKeyRef:
        name: xdr-auth-token
        key: xdr-auth-token
        optional: false
