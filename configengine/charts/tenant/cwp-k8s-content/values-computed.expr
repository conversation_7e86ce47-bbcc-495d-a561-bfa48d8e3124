_component_name: "'cwp-k8s-content'"
_component_image_name: "'cwp-k8s-api'"

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, local._component_image_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

env:
  ENABLE_CLOUD_POSTURE: "'true'"
  K8SCONNECTORCONF_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: '"postgres_password"'
        name: 'tenant.lcaas_id + "-cwp-secrets"'
        optional: false
  CONTENT_KMS_KEY_PROJECT: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  CONTENT_BUCKET: 'region.is_fedramp || region.is_dev ? "global-cwp-content-" + region.multi_project_postfix : "global-cwp-content"'


serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"cwp-k8s-content" + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "512Mi"'
  limits:
    cpu: '"1"'
    memory: '"2Gi"'
