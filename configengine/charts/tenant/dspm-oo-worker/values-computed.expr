_component_name: "'dspm-oo-worker'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"
_temporal_url: 'globals.env.TEMPORAL_URL'

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "150m" : "150m"'
    memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "2" : "2"'
    memory: '!infra_ff.is_enable_prod_spec ? "3Gi" : "3Gi"'

env:
  ST_RESIDENT_ID: 'tenant.lcaas_id'
  TEMPORAL_URL: local._temporal_url
  GENIE_INTERNAL_DOMAIN: '"dspm-oo-genie-svc." + local.namespaceOverride + ".svc.cluster.local"'
  JOE_INTERNAL_URL: '"http://dspm-oo-ready-job-evaluator-svc." + globals.dspm_namespace + ".svc.cluster.local"'
  JOSE_INTERNAL_URL: '"http://dspm-crespo-jose-svc." + globals.dspm_namespace + ".svc.cluster.local"'
  GENIE_INTERNAL_DOMAIN: '"dspm-oo-genie-svc." + local.namespaceOverride + ".svc.cluster.local"'
  AZURE_SECRET:
    valueFrom:
      secretKeyRef:
        key: "'azure_secret'"
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        optional: false
  DB_URL:
    valueFrom:
      secretKeyRef:
        key: "'mongodb_connection_string'"
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: local._temporal_url
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: local._temporal_url
      - name: "'TEMPORAL_NAMESPACE'"
        value: '"dspm-outpost-orchestrator"'
