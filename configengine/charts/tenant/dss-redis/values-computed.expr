_redis_large_tenant: "license.total_agents > 10000"
_redis_mem_req_large: 'infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi"'

fullnameOverride: 'globals.st_resource_prefix + "-dss-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'  

env:
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-secrets"'
        key: "'dss_redis_password'"
        optional: false

resources:
  requests:
    cpu: 'license.is_small_epp ? "100m" : "0.3"'
    memory: |
      license.is_small_epp ? "1Gi" :
      local._redis_large_tenant ? local._redis_mem_req_large:
      "2Gi"

  limits:
    cpu: 'infra_ff.enable_megatron_xdr ? "1.2" : "1"'
    memory: 'local._redis_large_tenant ? (infra_ff.enable_megatron_xdr ? "16Gi" : "8Gi") : "2Gi"'

args:
  maxclients: 'infra_ff.enable_megatron_xdr ? "30000" : "20000"'


snapshots: 
  dailyEnabled: false
  hourlyEnabled: false
  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"dss-redis-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"dss-redis-hourly-" + tenant.lcaas_id'
