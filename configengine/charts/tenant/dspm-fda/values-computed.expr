_family: "'dspm_fda_cortex'"
_service_names:
  worker: "'dspm-fda-worker'"
  api: "'dspm-fda-api'"
  lsr: "'dspm-fda-lsr'"

_images:
  family: "get(images.families, local._family)"

api:
  fullnameOverride: 'local._service_names.api'
  namespaceOverride: "globals.dspm_namespace"
  service:
    create: true
  hpa:
    enabled: true
  env:
    LCAAS_ID: "tenant.lcaas_id"
    METRO_CONTEXT_HOLDER_STRATEGY: "'STATIC'"
    SPRING_PROFILES_ACTIVE: "'prod,gcp,migration,internalApi'"
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    SPRING_DATA_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'redis_password'"
          optional: false
    SPRING_DATA_MONGODB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'mongodb_password_cwp'"
          optional: false

  serviceAccount:
    name: local._service_names.api
    create: true
    annotations:
      iam.gke.io/gcp-service-account: 'local._service_names.api + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

  resources:
    limits:
      cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
      memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1500Mi"'
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "50m" : "50m"'
      memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "256Mi"'
worker:
  fullnameOverride: 'local._service_names.worker'
  namespaceOverride: "globals.dspm_namespace"
  serviceAccount:
    name: local._service_names.worker
    create: true
    annotations:
      iam.gke.io/gcp-service-account: 'local._service_names.worker + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
  service:
    create: false
  hpa:
    enabled: true
    metrics:
      - type: "'Object'"
        object:
          describedObject:
            name: "'xdr-st'"
            apiVersion: "'/v1'"
            kind: "'Namespace'"
          metric:
            name: "'dspm_fda_worker_hpa'"
          target:
            type: "'Value'"
            value: 0.9
    minReplicas: 1
    maxReplicas: "!infra_ff.is_enable_prod_spec ? 4 : 10"
    behavior:
      scaleUp:
        stabilizationWindowSeconds: 0
        selectPolicy: "'Max'"
        policies:
          - type: "'Percent'"
            value: 100
            periodSeconds: 15
      scaleDown:
        stabilizationWindowSeconds: 600
        selectPolicy: "'Max'"
        policies:
          - type: "'Percent'"
            value: 10
            periodSeconds: 120
  resources:
    requests:
      cpu: 'region.viso_env == "dev" ? "50m" : "50m"'
      memory: 'region.viso_env == "dev" ? "256Mi" : "256Mi"'
    limits:
      cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
      memory: 'region.viso_env == "dev" ? "1Gi" : "1500Mi"'
  initContainers:
    initTemporal:
      name: "'init-temporal'"
      image:
        registry: globals.gcr
        repository: "'golden-images/temporalio/admin-tools'"
        tag: 'region.is_fedramp ? "1.29" : "********"'
      imagePullPolicy: "'IfNotPresent'"
      command:
        - "'sh'"
        - "'-c'"
        - "'temporal operator namespace describe dspm-fda || temporal operator namespace create --retention 3 dspm-fda &&
            temporal operator search-attribute create -n dspm-fda --name TenantId --type Keyword
        '"
      terminationMessagePath: "'/dev/termination-log'"
      terminationMessagePolicy: "'File'"
      env:
        - name: "'TEMPORAL_ADDRESS'"
          value: 'globals.env.TEMPORAL_URL'
  env:
    LCAAS_ID: "tenant.lcaas_id"
    METRO_CONTEXT_HOLDER_STRATEGY: "'STATIC'"
    SPRING_PROFILES_ACTIVE: "'prod,gcp,migration,worker'"
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    # TODO: remove envs
    SPRING_DATA_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'redis_password'"
          optional: false
    SPRING_DATA_MONGODB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'mongodb_password_cwp'"
          optional: false
    # TODO: remove envs

lsr:
  fullnameOverride: 'local._service_names.lsr'
  namespaceOverride: "globals.dspm_namespace"

  hpa:
    enabled: true

  serviceAccount:
    name: 'local._service_names.lsr'
    create: true
    annotations:
      iam.gke.io/gcp-service-account: 'local._service_names.lsr + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

  resources:
    requests:
      cpu: '!infra_ff.is_enable_prod_spec ? "50m" : "50m"'
      memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "256Mi"'
    limits:
      cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
      memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1500Mi"'

  env:
    LCAAS_ID: "tenant.lcaas_id"
    METRO_CONTEXT_HOLDER_STRATEGY: "'STATIC'"
    SPRING_CONFIG_ADDITIONAL_LOCATION: "'/config/config.yaml'"
    SPRING_PROFILES_ACTIVE: "'prod,gcp,migration,assetAnalysisListener,columnAnalysisListener,filesListener,assetInventoryListener,assetPostProcessorListener'"
    SPRING_DATA_REDIS_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'redis_password'"
          optional: false
    SPRING_DATA_MONGODB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-dspm-secrets"'
          key: "'mongodb_password_cwp'"
          optional: false
global:
  image:
    registry: "globals.gcr"
    tag: "local._images.family.tag"
    repository: "local._images.family.repository"
  globalConfig:
    metro:
      tenants:
        - id: "tenant.lcaas_id"
      mongo:
        database-factory-enabled: false
    fda:
      cortex:
        platform-base-url: "globals.env.CORTEX_PLATFORM_URI"
      asset-analysis-listener:
        queue: '"dspm-fda-asset-analysis-" + tenant.lcaas_id + "-sub"'
      asset-inventory-listener:
        queue: '"dspm-fda-asset-change-feed-" + tenant.lcaas_id + "-sub"'
      asset-post-processing-listener:
        queue: '"dspm-fda-asset-post-processing-request-" + tenant.lcaas_id + "-sub"'
      column-analysis-listener:
        queue: '"dspm-fda-column-analysis-" + tenant.lcaas_id + "-sub"'
      files-listener:
        queue: '"dspm-fda-file-analysis-" + tenant.lcaas_id + "-sub"'
      replication:
        big-query:
          dataset-name: '"dspm_" + tenant.lcaas_id'
      asset-post-processing:
        request-topic: '"dspm-asset-post-processing-request-" + tenant.lcaas_id'
      skinner:
        base-url: '"http://dspm-x-files-skinner-svc." + globals.dspm_namespace + ".svc.cluster.local"'
      dacs:
        base-url: '"http://dspm-dacs-dashboard-svc." + globals.dspm_namespace + ".svc.cluster.local"'
      voyager:
        base-url: '"http://dspm-voyager-svc." + globals.dspm_namespace + ".svc.cluster.local"'
    spring:
      data:
        mongodb:
          host: globals.env.MONGO_DB_HOST
          authentication-database: '"admin"'
          database: '"dspm-fda"'
          port: '27017'
          username: '"root"'
        redis:
          host: '"xdr-st-" + tenant.lcaas_id + "-dspm-redis.xdr-st.svc.cluster.local"'
          port: '6379'
      temporal:
        connection:
          target: 'globals.env.TEMPORAL_URL'
        namespace: '"dspm-fda"'