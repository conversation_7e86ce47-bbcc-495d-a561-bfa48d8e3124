apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: xql-engine
  namespace: "{{ .globals.st_namespace }}"
spec:
  chart:
    spec:
      chart: tenant/xql-engine
      sourceRef:
        kind: HelmRepository
        name: helm-charts
        namespace: flux-system
      interval: "{{ .globals.flux.chart_pull_interval }}"
{{- if not .tenant.is_metro_tenant }}
  dependsOn:
    - name: vsg
{{- end }}
  driftDetection:
    ignore:
      - paths:
          - /spec/replicas
        target:
          kind: Deployment
{{- if not .tenant.is_metro_tenant }}
      - paths:
          - /spec/template/spec/containers/0/resources
        target:
          kind: Deployment
{{- end }}
    mode: "{{ .globals.flux.helm_release_drift_detection_mode }}"
  install:
    disableWait: true
    createNamespace: true
    remediation:
      retries: -1
  interval: 1m0s
  rollback:
    disableWait: true
  targetNamespace: "{{ .globals.st_namespace }}"
  uninstall:
    disableWait: true
  upgrade:
    disableWait: true
  values: {}
