apiVersion: v2
name: uvem-vxp-api
description: A Helm chart for Kubernetes
type: application
version: 0.2.3
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
appVersion: "0.2.0"
annotations:
  "panw.com/deploy-eval": |
    !tenant.is_metro_tenant &&
    (globals.product_code_c1 ||
    globals.product_code_c3 || 
    globals.product_code_x0 || 
    globals.product_code_x1 || 
    globals.product_code_x3 || 
    globals.product_code_x5 ||
    globals.product_code_eplus)
  owner.panw/group: 'uvem'
  owner.panw/team: 'uvem'
  owner.panw/team-slack-handle: '#uvem-vulnerability-experience-public'
  owner.panw/people-slack-handle-owners-group: '@uvem-vulnerability-experience-group'
  owner.panw/people-slack-handle-team-lead: '@Venky Chintapandu'
  owner.panw/source-code-ops-helm-chart-url: 'https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/-/tree/dev/configengine/charts/tenant/uvem-vxp-api' 
