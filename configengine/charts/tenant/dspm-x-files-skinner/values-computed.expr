_component_name: "'dspm-x-files-skinner'"
_component_image: "'dspm-x-files-skinner'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "2Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : "2Gi"'

env:
  ST_RESIDENT_ID: 'tenant.lcaas_id'
  CORTEX_BASE_PATH: 'globals.env.CORTEX_PLATFORM_URI'
  TEMPORAL_URL: 'globals.env.TEMPORAL_URL'
  EGRESSPROXY_URL: 'globals.egress_proxy_address'

  DB_URL:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'mongodb_connection_string'"
        optional: false

  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-secrets"'
        key: "'redis_password'"
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false

  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-x-files"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-x-files-feature-flags"'
      optional: false

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
      - name: "'TEMPORAL_NAMESPACE'"
        value: "'dspm-x-files'"