deployment:
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate

serviceAccount:
  create: true
  automountServiceAccountToken: false
  name: dspm-x-files-skinner

terminationGracePeriodSeconds: 60

nodeSelector:
  xdr-pool: wi-dynamic

ports:
  - containerPort: 8099
    protocol: TCP

service:
  create: true
  port: 80
  targetPort: 8099

deploymentAnnotations:
  reloader.stakater.com/auto: "true"

deploymentLabels:
  group: dspm
  team: x-files
podLabels:
  group: dspm
  team: x-files

podSecurityContext:
  fsGroup: 888
  runAsNonRoot: false

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8099"

initContainers:
  initTemporalNamespace:
    name: init-temporal-namespace
    command:
      - "sh"
      - "-c"
      - "temporal operator namespace describe dspm-x-files || temporal operator namespace create dspm-x-files"

  initTemporalSearchAttribute:
    name: init-temporal-search-attribute
    command:
      - "sh"
      - "-c"
      - "temporal operator search-attribute create --name company_id --type Keyword;temporal operator search-attribute create --name project_id --type Keyword;temporal operator search-attribute create --name cloud_provider --type Keyword"

env:
  TEMPORAL_NAMESPACE_X_FILES: "dspm-x-files"
  TEMPORAL_TLS: "false"
  TEMPORAL_QUEUE_NAME: "x_files_queue"
  WORKERS: "5"
  EGRESSPROXY_CA_PATH: "/etc/certs/egress.crt"
  AWS_CA_BUNDLE: "/etc/certs/egress.crt"

hpa:
  enabled: false
  metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: 95
          type: Utilization
      type: Resource
  minReplicas: 1
  maxReplicas: 20
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 120

livenessProbe:
  failureThreshold: 5
  httpGet:
    path: /health
    port: 8099
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2
readinessProbe:
  failureThreshold: 5
  httpGet:
    path: /health
    port: 8099
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2

volumes:
  - name: api-certs-dspm
    secret:
      secretName: api-certs-dspm
      defaultMode: 186
      optional: false

volumeMounts:
  - name: api-certs-dspm
    mountPath: /etc/certs/egress.crt
    mountPropagation: None
    readOnly: true
    subPath: egress.crt

resources: {}