_component_name: "'cwp-private-discovery-orchestrator'"

fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'
namespaceOverride: "globals.cwp_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

env:
  CWP_PRIVATE_DISCOVERY_SCAN_SPEC_MANAGER_URL: '"http://xdr-st-"+ tenant.lcaas_id +"-cwp-scan-spec-manager-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  SCAN_PLATFORM_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-workload-orchestration-svc." + globals.cwp_namespace + ".svc.cluster.local:8080/api/v1"'
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_HOST: globals.env.PGHOST
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: 'tenant.lcaas_id + "-cwp-secrets"'
          key: '"postgres_password"'
          optional: false
  CWP_PRIVATE_DISCOVERY_REQUEST_SUBSCRIPTION_NAME: '"ca-collection-outpost-discovery-requests-" + tenant.lcaas_id + "-sub"'
  CWP_PRIVATE_DISCOVERY_RESULT_SUBSCRIPTION_NAME: '"cwp-private-discovery-results-" + tenant.lcaas_id + "-sub"'
  CWP_PRIVATE_DISCOVERY_LIFE_CYCLE_EVENT_SUBSCRIPTION_NAME: '"cwp-private-disc-orc-sp-lifecycle-sub-" + tenant.lcaas_id'
  CWP_PRIVATE_DISCOVERY_ORCHESTRATOR_PLATFORM_HEALTHMONITORING_TOPIC: '"cloud-health-monitoring-" + tenant.lcaas_id'
  CWP_PRIVATE_DISCOVERY_ORCHESTRATOR_PLATFORM_CLOUD_HEALTHMONITORING_TOPIC: '"cloud-health-monitoring-statuses-" + tenant.lcaas_id'
  CWP_PRIVATE_DISCOVERY_BATCHER_BATCH_SIZE: 1000000

  CWP_PRIVATE_DISCOVERY_REDIS_HOST: globals.env.REDIS_CWP_URI
  CWP_PRIVATE_DISCOVERY_REDIS_PASS:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cwp-secrets"'
        key: '"cwp_redis_password"'
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-cwp-feature-flags"'
      optional: false

serviceAccount:
  annotations:
    iam.gke.io/gcp-service-account: '"cwp-private-disc-orchestrator@" + tenant.project_id + ".iam.gserviceaccount.com"'

hpa:
  metrics:
    - external:
        metric:
          name: '"pubsub.googleapis.com|subscription|num_undelivered_messages"'
          selector:
            matchLabels:
              resource.labels.project_id: "tenant.project_id"
              resource.labels.subscription_id: '"ca-collection-outpost-discovery-requests-" + tenant.lcaas_id + "-sub"'
        target:
          type: '"AverageValue"'
          averageValue: '"200"'
      type: '"External"'
    - external:
        metric:
          name: '"pubsub.googleapis.com|subscription|num_undelivered_messages"'
          selector:
            matchLabels:
              resource.labels.project_id: "tenant.project_id"
              resource.labels.subscription_id: '"cwp-private-disc-orc-sp-lifecycle-sub-" + tenant.lcaas_id'
        target:
          type: '"AverageValue"'
          averageValue: '"200"'
      type: '"External"'
    - external:
        metric:
          name: '"pubsub.googleapis.com|subscription|num_undelivered_messages"'
          selector:
            matchLabels:
              resource.labels.project_id: "tenant.project_id"
              resource.labels.subscription_id: '"cwp-private-discovery-results-" + tenant.lcaas_id + "-sub"'
        target:
          type: '"AverageValue"'
          averageValue: '"200"'
      type: '"External"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "10m" : "50m"'
    memory: '!infra_ff.is_enable_prod_spec ? "64Mi" : "256Mi"'
  limits:
    cpu: '"1"'
    memory: '"1Gi"'
