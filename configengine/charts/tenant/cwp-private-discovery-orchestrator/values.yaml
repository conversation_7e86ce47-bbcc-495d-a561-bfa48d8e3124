image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
restartPolicy: ""
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}
podSecurityContext: {}
terminationGracePeriodSeconds: 30
automountServiceAccountToken: false
deploymentAnnotations:
  reloader.stakater.com/auto: "true"
env:
  SERVICE_NAME: "PRIVATE_DISCOVERY_ORCHESTRATOR"
  DEPLOYMENT_TYPE: "PRIVATE"
  CWP_PRIVATE_DISCOVERY_OUTPOST_WORKLOAD_MAX_LIFETIME_SECONDS: 18000
  CWP_PRIVATE_DISCOVERY_OUTPOST_WORKLOAD_LAUNCH_DEADLINE_SECONDS: 21600
  CWP_PRIVATE_DISCOVERY_OUTPOST_WORKLOAD_DISK_SIZE_GB: 40
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_PORT: "5432"
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_USER: "root"
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_SCHEMA: "public"
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_NAME: "private_asset_discovery"
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_MAX_OPEN_CONNECTIONS: 5
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_MAX_IDLE_CONNECTIONS: 3
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_CONNECTION_MAX_LIFETIME_SECONDS: 0
  CWP_PRIVATE_DISCOVERY_POSTGRES_DB_CONNECTION_MAX_IDLE_TIME_SECONDS: 1800
  CWP_PRIVATE_DISCOVERY_BATCHER_SLEEP_INTERVAL_MILLIS: 60000
  CWP_PRIVATE_DISCOVERY_BATCHER_RETRY_MAX_ATTEMPTS: 3
  CWP_PRIVATE_DISCOVERY_BATCHER_BATCH_PREFIX: "CWP_PRIVATE_DISCOVERY"
  CWP_PRIVATE_DISCOVERY_ORCHESTRATOR_ENABLE_SCRIPT_DEBUG: false
  CWP_PRIVATE_DISCOVERY_REDIS_PORT: "6379"
  CWP_PRIVATE_DISCOVERY_REDIS_DB: 0
  CWP_PRIVATE_DISCOVERY_SCANNER_CONCURRENCY: 1

ports:
  - containerPort: 8080
    protocol: TCP

livenessProbe:
  failureThreshold: 9
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

deploymentLabels:
  group: cwp
  team: core
podLabels:
  group: cwp
  team: core
podAnnotations:
  prometheus.io/port: "8080"
  prometheus.io/scrape: "true"

nodeSelector:
  xdr-pool: "wi-dynamic"

deployment:
  strategy:
    type: "RollingUpdate"

serviceAccount:
  create: true
  name: cwp-private-disc-orchestrator

securityContext:
  fsGroup: 1001

hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 120
