fullnameOverride: 'globals.st_resource_prefix + "-cwp-mongodb"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.0.26" : "7.0.12"'
configmap:
  nameOverride: 'tenant.lcaas_id + "-configmap-mongodb"'

env:
  MONGO_INITDB_ROOT_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: '"mongodb_password_cwp"'
        name: 'tenant.lcaas_id + "-secrets"'
        optional: false

extraVolumes:
  - name: '"mongodb-keyfile"'
    secret:
      defaultMode: 420
      optional: false
      secretName: 'tenant.lcaas_id + "-secrets"'

serviceAccount:
  name: "'mongodb'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

agent:
  resources:
    limits:
      cpu: 'region.is_fedramp ? "200m" : "100m"'
      memory: 'region.is_fedramp ? "1024Mi" : "128Mi"'
    requests:
      cpu: 'region.is_fedramp ? "100m" : "10m"'
      memory: 'region.is_fedramp ? "128Mi" : "12Mi"'
