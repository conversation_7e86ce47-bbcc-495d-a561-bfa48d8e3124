apiVersion: batch/v1
kind: Job
metadata:
  name: cloudsec-resident-common-init-{{ .Values.tenant_id }}
  namespace: {{ .Values.namespaceOverride }}
  labels:
    app.kubernetes.io/name: cloudsec-resident-common-init
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "5"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded,hook-failed
spec:
  template:
    metadata:
      name: {{ .Values.fullnameOverride }}
      labels:
        app.kubernetes.io/name: cloudsec-resident-common-init
        job-name: cloudsec-resident-common-init-{{ .Values.tenant_id }}
    spec:
      serviceAccountName: {{ include "chart.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
      restartPolicy: "OnFailure"
      containers:
        - name: cloudsec-resident-common-init
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: IfNotPresent
          {{- with .Values.env }}
          env: {{ (include "common.renderEnv" .) | nindent 12 }}
          {{- end }}
          {{- with .Values.envFrom }}
          envFrom: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with (default .Values.nodeSelector) }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
      {{- toYaml . | nindent 8 }}
      {{- end }}