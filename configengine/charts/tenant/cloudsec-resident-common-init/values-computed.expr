_component_name: "'cloudsec-resident-common-init'"

namespaceOverride: globals.st_namespace
fullnameOverride: local.namespaceOverride + "-cloudsec-resident-common-init"
tenant_id: tenant.lcaas_id

image:
  repository: globals.gcr + "/prismacloud/cloudsec-resident-common-init"
  tag: '"7e5a45b7e183ab62771cd573d12269431ab066e7"'

env:
  GCP_PROJECT_ID: tenant.project_id
  GENERIC_LCAAS_ID: tenant.lcaas_id
  GCPCONF_PROJECT_ID: tenant.lcaas_id
  CLOUDSEC_COMMON_POSTGRES_SVC: globals.env.PGHOST
  CLOUDSEC_COMMON_POSTGRES_USER: '"root"'
  CLOUDSEC_COMMON_POSTGRES_PORT: '"5432"'
  RESIDENT_COMMON_INIT_VERSION: '"1.0.0"'
  CLOUDSEC_COMMON_POSTGRES_RULE_MGMT_SCHEMA: '"cspm_" + string(tenant.lcaas_id)'
  CLOUDSEC_DATASET_ID: '"cloudsec_" + string(tenant.lcaas_id)'
  PLATFORM_DATASET_ID: '"public_platform_" + string(tenant.lcaas_id)'
  CLOUDSEC_RW_POSTGRES_USER:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres"'
        key: "'POSTGRES_CSPM_USERNAME'"
        optional: false
  CLOUDSEC_RW_POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-postgres-ps"'
        key: "'POSTGRES_CSPM_PASSWORD'"
        optional: false
  RULES_DB_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-cloudsec-secrets"'
        key: "'CLOUDSEC_COMMON_POSTGRES_PASSWORD'"
        optional: false