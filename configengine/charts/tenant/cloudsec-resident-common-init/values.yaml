debug: true
containerSecurityContext: {}
securityContext: {}
imagePullSecrets: {}
tolerations: []
affinity: {}

# Main container image configuration
image:
  repository: us-docker.pkg.dev/xdr-registry-dev-01/cortex-docker-dev/cloudsec-resident-common-init
  tag: latest
  pullPolicy: IfNotPresent

# Init container configuration for secdo-init-wait

# cloudsec_init_secdo_wait_envFrom:
#   - configMapRef:
#       name: '{{ .Values.tenant_id }}-cloudsec-configmap'
#       optional: false

# Tenant configuration
tenant_id: ""
st_namespace: ""
fullnameOverride: "cloudsec-resident-common-init"


resources: {}
# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#  cpu: 100m
#  memory: 128Mi
# requests:
#  cpu: 100m
#  memory: 128Mi

nodeSelector:
  xdr-pool: wi-dynamic

# Service account configuration
serviceAccount:
  create: true
  automountServiceAccountToken: true
  annotations: {}