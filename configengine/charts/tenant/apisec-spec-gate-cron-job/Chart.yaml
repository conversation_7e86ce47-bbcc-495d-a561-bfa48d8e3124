apiVersion: v2
name: apisec-spec-gate-cron-job
description: A Helm chart for Kubernetes
type: application
version: 0.1.0
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
appVersion: "0.1.0"
annotations:
  "panw.com/deploy-eval": (!tenant.is_metro_tenant && globals.enable_apisec)
  owner.panw/group: apisec
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/apisec-spec-gate-cron-job
  owner.panw/team: nexus
  owner.panw/team-slack-handle: '@waas-nexus'
  owner.panw/people-slack-handle-team-lead: '@mgrechuhin'
  owner.panw/people-slack-handle-owners-group: '@udagan'
