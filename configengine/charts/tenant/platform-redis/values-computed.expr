_securityContext:
  fedramp:
    runAsUser: 999
    runAsGroup: 1000
    fsGroup: 1000

fullnameOverride: 'globals.st_resource_prefix + "-platform-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  PLATFORM_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'platform_redis_password'"
        optional: false

resources:
  requests:
    cpu: '"0.3"'
    memory: '"256Mi"'
  limits:
    cpu: '"1"'
    memory: '"256Mi"'

securityContext: "region.is_fedramp ? local._securityContext.fedramp : nil"
