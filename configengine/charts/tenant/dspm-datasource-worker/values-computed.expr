_component_name: "'dspm-datasource-worker'"
_component_image: "'dspm-datasource_worker'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : "2Gi"'

env:
  SPRING_MAIN_BANNER-MODE: '"off"'
  CONSOLE_LOG_STRUCTURED_FORMAT: '"logstash"'
  EGRESSPROXY_PORT: "globals.egress_proxy_port"
  SPRING_TEMPORAL_CONNECTION_TARGET: 'globals.env.TEMPORAL_URL'
  CORTEX_PLATFORM_URI: 'globals.env.CORTEX_PLATFORM_URI'
  CORTEX_LCAAS: "tenant.lcaas_id"
  JDBC_URL: '"********************************************************************************=" + tenant.project_id'
  DBAAS_DATASOURCE_SCHEMA: '"dspm_dbaas_datasource_" + tenant.lcaas_id'
  CORTEX_ASSET_SERVICE_PUBSUB_TOPIC: '"dp-uai-asset-observations-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_ACCOUNT_EVENTS_TOPIC: '"dspm-datasource-account-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_MIP_LABELS_TOPIC: '"dspm-information-protection-label-mip-update-topic-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_ONBOARDING_SUBSCRIPTION_NAME: '"dspm-datasource-worker-onboarding-sub-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_LISTING_TOPIC: '"dspm-voyager-listing-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_SCAN_NOW_SUB: '"dspm-datasource-worker-scan-now-sub-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_DSPM_FILE_ANALYSIS_TOPIC: '"dspm-file-analysis-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_DSPM_COLUMN_ANALYSIS_TOPIC: '"dspm-column-analysis-" + tenant.lcaas_id'
  CORTEX_WORKER_PUBSUB_DSPM_ASSET_ANALYSIS_TOPIC: '"dspm-asset-analysis-" + tenant.lcaas_id'
  OUTPOST_ORCHESTRATOR_INTERNAL_URL: '"http://dspm-oo-ready-job-evaluator-svc." + local.namespaceOverride + ".svc.cluster.local"'
  SP_WORKLOAD_ORCHESTRATOR_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-workload-orchestration-svc.cwp.svc.cluster.local:8080"'
  CORTEX_ASSET_TABLE_METADATA_PAYLOAD_BUCKET: 'tenant.project_id + "-dspm-content-bouncer-data"'
  CORTEX_ASSET_TABLE_METADATA_PUBSUB_TOPIC: '"projects/" + tenant.project_id + "/topics/dspm-column-listing-data-" + tenant.lcaas_id'
  JVM_STORE_PASS: "tenant.lcaas_id"
  SCAN_RESULTS_SUB: '"dspm-datasource-scan-results-sub-" + tenant.lcaas_id'
  SP_LIFECYCLE_EVENTS_SUB: '"dspm-datasource-sp-lifecycle-events-sub-" + tenant.lcaas_id'
  DSPM_LISTING_RESULT_SUBSCRIPTION_NAME: '"dspm-voyager-listing-result-sub-" + tenant.lcaas_id'
  DSPM_LISTING_DATA_BUCKET: 'tenant.project_id + "-dspm-voyager-listing-data"'
  ASSET_ERROR_SUBSCRIPTION: '"dspm-datasource-dp-uai-asset-ingestion-errors-sub-" + tenant.lcaas_id'
  PLATFORM_METRO_PROJECT_ID: 'tenant.project_id'
  METRO_TENANTS_0_ID: 'tenant.lcaas_id'
  SPRING_DATA_MONGODB_URI:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        key: "'mongodb_connection_string_datasource_worker'"
        optional: false
  CORTEX_GCP_FACADE_SERVICE_ACCOUNT:
    valueFrom:
      secretKeyRef:
        key: "'facade-service-account'"
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        optional: false
  SNOWFLAKE_PRIVATE_KEY:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        key: "'onboarding-snowflake-private-key'"
        optional: false
  SNOWFLAKE_PUBLIC_KEY:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-platform-secrets"'
        key: "'onboarding-snowflake-public-key'"
        optional: false
  CORTEX_CRYPTO_AES_KEY:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-datasource-secrets"'
        key: "'agentconfig_encryption_key'"
        optional: false
  CORTEX_DACS_BASE_URL: '"http://dspm-dacs-dashboard-svc." + local.namespaceOverride + ".svc.cluster.local"'
  BC_DISTRIBUTOR_BASE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-bc-distributor-svc.cwp.svc.cluster.local:8080"'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false

_cert_command: |-
  [
    "cp -R /opt/java/openjdk/lib/security/* /cacerts/ &&",
    "awk 'BEGIN {c=0;} /-----BEGIN CERTIFICATE-----/ {filename=sprintf(\"/tmp/cert_%d.crt\", c++);} {print > filename}' /etc/certs/egress.crt &&",
    "i=0; for certfile in /tmp/cert_*.crt; do keytool -import -noprompt -trustcacerts -alias \"proxy_crt_$i\" -file \"$certfile\" -keystore /cacerts/cacerts -storepass changeit 2>/dev/null; i=$((i + 1)); done &&",
    "rm -f /tmp/cert_*.crt"
  ]

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'

  initCert:
    image:
      registry: globals.gcr
      tag: "local._images.component.tag ?? local._images.family.tag"
      repository: "local._images.component.repository ?? local._images.family.repository"
    command:
      - "'sh'"
      - "'-c'"
      - join(local._cert_command, "\n") + "\n"


  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
      - name: "'TEMPORAL_NAMESPACE'"
        value: "'datasource'"
