_component_name: '"temporal"'
_temporal_database_name: local._component_name
_temporal_database_username: local._component_name
_temporal_visibility_database_name: 'local._component_name + "_visibility"'
_temporal_visibility_database_username: 'local._component_name + "_visibility"'
_tag: '"1.28.1.0"'
_admin_tools_tag: '"1.28"'
_admin_tools_image: 'globals.st_namespace + "/golden-images/temporalio/admin-tools"'
_temporal_web_image_repository: 'globals.st_namespace + "/golden-images/temporalio/ui"'
_temporal_server_image_repository: 'globals.st_namespace + "/golden-images/temporalio/server"'
_helm_version: '"v1.22.2"'
_images:
  liquibase_job:
    family: 'get(images.families, "ciem_liquibase")'

namespaceOverride: globals.st_namespace
fullnameOverride: 'local._component_name + "-" + tenant.lcaas_id'

# create db and user job
create_db_and_user:
  name: 'local.fullnameOverride + "-create-db-and-user"'
  containerName: '"psql"'
  command:
    - '"psql"'
  args:
    - '"-c"'
    - '"CREATE DATABASE $(TEMPORAL_DATABASE_NAME);"'
    - '"-c"'
    - |-
      "CREATE USER temporal WITH ENCRYPTED PASSWORD '$(TEMPORAL_POSTGRES_PASSWORD)';"
    - '"-c"'
    - '"GRANT ALL PRIVILEGES ON DATABASE $(TEMPORAL_DATABASE_NAME) TO $(TEMPORAL_DATABASE_USERNAME);"'
    - '"-c"'
    - '"GRANT ALL ON DATABASE $(TEMPORAL_DATABASE_NAME) to $(TEMPORAL_DATABASE_USERNAME);"'
    - '"-c"'
    - '"ALTER DATABASE temporal owner to $(TEMPORAL_DATABASE_USERNAME);"'
    - '"-c"'
    - '"CREATE DATABASE $(TEMPORAL_VISIBILITY_DATABASE_NAME);"'
    - '"-c"'
    - |-
      "CREATE USER $(TEMPORAL_VISIBILITY_DATABASE_USERNAME) WITH ENCRYPTED PASSWORD '$(TEMPORAL_VISIBILITY_POSTGRES_PASSWORD)';"
    - '"-c"'
    - '"GRANT ALL PRIVILEGES ON DATABASE $(TEMPORAL_VISIBILITY_DATABASE_NAME) TO $(TEMPORAL_VISIBILITY_DATABASE_USERNAME);"'
    - '"-c"'
    - '"GRANT ALL ON DATABASE $(TEMPORAL_VISIBILITY_DATABASE_NAME) to $(TEMPORAL_VISIBILITY_DATABASE_USERNAME);"'
    - '"-c"'
    - '"ALTER DATABASE $(TEMPORAL_VISIBILITY_DATABASE_NAME) owner to $(TEMPORAL_VISIBILITY_DATABASE_USERNAME);"'
  backoffLimit: '"100"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"-100"'
    helm.sh/hook-delete-policy: '"before-hook-creation,hook-succeeded"'
  image:
    fullName: 'globals.gcr + "/golden-images/postgres:16.2"'
  restartPolicy: '"Never"'
  automountServiceAccountToken: true
  env:
    TEMPORAL_DATABASE_NAME: local._temporal_database_name
    TEMPORAL_DATABASE_USERNAME: local._temporal_database_username
    TEMPORAL_VISIBILITY_DATABASE_NAME: local._temporal_visibility_database_name
    TEMPORAL_VISIBILITY_DATABASE_USERNAME: local._temporal_visibility_database_username
    PGUSER: '"root"'
    PGHOST: globals.env.PGHOST
    PGPASSWORD:
      valueFrom:
        secretKeyRef:
          key: "'postgres_password'"
          name: "globals.tenant_secrets"
          optional: false
    TEMPORAL_POSTGRES_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: '"temporal-postgress"'
          key: '"password"'
          optional: false
    TEMPORAL_VISIBILITY_POSTGRES_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: '"temporal-visibility-postgress"'
          key: '"password"'
          optional: false

# liquibase job
liquibase_job:
  name: 'local.fullnameOverride + "-liquibase-job"'
  containerName: '"job-liquibase-ciem"'
  command:
    - '"/bin/bash"'
  args:
    - '"-c"'
    - |-
      "echo 'Checking if the database exists...'\n" +
      "if psql -h $(PGHOST) -p $(PORT) -U $(PGUSER) -d postgres -tAc \"SELECT 1 FROM pg_database WHERE datname = 'ciem';\" | grep -q 1; then\n" +
      "  echo 'Database ciem found.'\n" +
      "else echo 'Database ciem not found, creating...'\n" +
      "  psql -h $(PGHOST) -p $(PORT) -U $(PGUSER) -d postgres -c \"CREATE DATABASE ciem;\"\n" +
      "  echo 'Waiting for the database to become available...'\n" +
      "  sleep 5\n" +
      "fi\n" +
      "echo 'Checking if the database is ready to accept connections...'\n" +
      "while ! pg_isready -h $(PGHOST) -p $(PORT) -U $(PGUSER) -d ciem; do\n" +
      "  echo 'Database not ready, waiting...'\n" +
      "  sleep 2\n" +
      "done\n" +
      "echo 'Database is ready, proceeding to Liquibase migration...'\n" +
      "./liquibase --driver=org.postgresql.Driver --url=***************************************************) --username=$(PGUSER) --password=$(PGPASSWORD) --changeLogFile=$(CHANGELOG_FILE) --logLevel=debug migrate"
  backoffLimit: '"100"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"-90"'
    helm.sh/hook-delete-policy: '"before-hook-creation,hook-succeeded"'
  automountServiceAccountToken: true
  image:
    fullName: 'globals.gcr + "/" + local._images.liquibase_job.family.repository + ":" + local._images.liquibase_job.family.tag'
  restartPolicy: '"Never"'
  env:
    PGUSER: '"root"'
    PGPASSWORD: local.create_db_and_user.env.PGPASSWORD
    PGHOST: globals.env.PGHOST
    PORT: '"5432"'
    DATABASE_NAME: '"ciem"'
    CHANGELOG_FILE: '"/db/changelog/db.changelog-root.yaml"'

# temporal_migration job
temporal_migration:
  name: 'local.fullnameOverride + "-migration"'
  containerName: '"temporal-db-setup"'
  command:
    - '"/bin/bash"'
  args:
    - '"-c"'
    - |-
      "set -e\n" +
      "set -x\n" +
      "temporal-sql-tool --database $(TEMPORAL_DATABASE_NAME) setup-schema -v 0.0\n" +
      "temporal-sql-tool --database $(TEMPORAL_DATABASE_NAME) update-schema --schema-dir /etc/temporal/schema/postgresql/v12/temporal/versioned"
  backoffLimit: '"100"'
  restartPolicy: '"Never"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"-80"'
    helm.sh/hook-delete-policy: '"before-hook-creation,hook-succeeded"'
  automountServiceAccountToken: true
  image:
    fullName: 'globals.gcr + "/golden-images/temporalio/admin-tools:" + local._admin_tools_tag'
  env:
    SQL_USER: local._temporal_database_username
    SQL_HOST: globals.env.PGHOST
    SQL_PORT: '"5432"'
    SQL_PLUGIN: '"postgres12"'
    SQL_PASSWORD:
      valueFrom:
        secretKeyRef:
          key: "'password'"
          name: '"temporal-postgress"'
          optional: false
    TEMPORAL_DATABASE_NAME: local._temporal_database_name

# temporal_visibility_migration job
temporal_visibility_migration:
  name: 'local.fullnameOverride + "-visibility-migration"'
  containerName: '"temporal-visibility-db-setup"'
  command:
    - '"/bin/bash"'
  args:
    - '"-c"'
    - |-
      "set -e\n" +
      "set -x\n" +
      "temporal-sql-tool --database $(TEMPORAL_VISIBILITY_DATABASE_NAME) setup-schema -v 0.0\n" +
      "temporal-sql-tool --database $(TEMPORAL_VISIBILITY_DATABASE_NAME) update-schema --schema-dir /etc/temporal/schema/postgresql/v12/visibility/versioned"
  backoffLimit: '"100"'
  jobAnnotations:
    helm.sh/hook: '"pre-install,pre-upgrade"'
    helm.sh/hook-weight: '"-70"'
    helm.sh/hook-delete-policy: '"before-hook-creation,hook-succeeded"'
  automountServiceAccountToken: true
  image:
    fullName: 'globals.gcr + "/golden-images/temporalio/admin-tools:" + local._admin_tools_tag'
  env:
    SQL_USER: local._temporal_visibility_database_username
    SQL_HOST: globals.env.PGHOST
    SQL_PORT: '"5432"'
    SQL_PLUGIN: '"postgres12"'
    SQL_PASSWORD:
      valueFrom:
        secretKeyRef:
          name: "'temporal-visibility-postgress'"
          key: '"password"'
          optional: false
    TEMPORAL_VISIBILITY_DATABASE_NAME: local._temporal_visibility_database_name
  restartPolicy: '"Never"'

# Temporal overrides
version: local._helm_version
server:
  image:
    tag: local._tag
    repository: 'globals.gcr + "/golden-images/temporalio/server"'
  enabled: true
  config:
    persistence:
      default:
        driver: '"sql"'
        sql:
          driver: '"postgres12"'
          host: globals.env.PGHOST
          port: 5432
          database: local._temporal_database_name
          user: local._temporal_database_username
          # for a production deployment use this instead of `password` and provision the secret beforehand e.g. with a sealed secret
          # it has a single key called `password`
          existingSecret: 'local._component_name + "-postgress"'
          secretName: 'local._component_name + "-postgress"'
          maxConns: 20
          maxConnLifetime: '"1h"'
          tls:
            enabled: false
            enableHostVerification: false

      visibility:
        driver: '"sql"'
        sql:
          driver: '"postgres12"'
          host: globals.env.PGHOST
          port: 5432
          database: local._temporal_visibility_database_name
          user: local._temporal_visibility_database_username
          # for a production deployment use this instead of `password` and provision the secret beforehand e.g. with a sealed secret
          # it has a single key called `password`
          existingSecret: 'local._component_name + "-visibility-postgress"'
          secretName: 'local._component_name + "-visibility-postgress"'
          maxConns: 20
          maxConnLifetime: '"1h"'
          tls:
            enabled: false
            enableHostVerification: false
          #  serverName: _HOST_ # this is strictly required when using serverless CRDB offerings
    logLevel: '"error"'

  archival:
    history:
      state: '"disabled"'
      enableRead: false
    visibility:
      state: '"disabled"'
      enableRead: false

  namespaceDefaults:
    archival:
      history:
        state: '"disabled"'
      visibility:
        state: '"disabled"'

  frontend:
    resources:
      requests:
        cpu: '"500m"'
        memory: '"1Gi"'
  #      limit:
  #        memory: '"1Gi"'

  history:
    resources:
      requests:
        cpu: 1
        memory: '"1Gi"'
  #      limit:
  #        memory: '"1Gi"'

  matching:
    replicaCount: 3
    resources:
      requests:
        cpu: '"500m"'
        memory: '"500Mi"'
  #      limit:
  #        memory: '"1Gi"'

  worker:
    resources:
      requests:
        cpu: '"250m"'
        memory: '"1Gi"'
  #      limit:
  #        memory: '"1Gi"'

  metrics:
    serviceMonitor:
      enabled: false
cassandra:
  enabled: false

mysql:
  enabled: false

postgresql:
  enabled: false

prometheus:
  enabled: false

grafana:
  enabled: false

elasticsearch:
  enabled: false

schema:
  setup:
    enabled: false
  update:
    enabled: false

admintools:
  enabled: false

serviceAccount:
  # Name of the service account, default: temporal.fullname
  name: local._component_name

  extraAnnotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

web:
  image:
    tag: 'region.is_fedramp ? "2.43.3" : "2.39.0"'
    repository: 'globals.gcr + "/golden-images/temporalio/ui"'
