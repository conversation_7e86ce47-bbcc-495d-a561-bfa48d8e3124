tags:
  metroTenant: tenant.is_metro_tenant

_fqdn_suffix: 'split(globals.external_fqdn, ".")[0] + (region.is_dev ? "-dev" : "")'

_is_metro_v2: "tenant.is_metro_tenant && !tenant.is_legacy_metro"

_xsoar_conf_xsoar_host: '"10.181." + string(tenant.metro_tenant_index) + ".80"'
_frontend_url: '(local._is_metro_v2 ? "" : globals.st_resource_prefix + "-") + "frontend." + globals.default_namespace + ".svc.cluster.local"'
_app_frontend_url: '"http://" + local._frontend_url'

_distribution_postfix: 'region.multi_project_postfix == "dev" ? "-dev" : region.multi_project_postfix == "prod-fr" ? "-prod-fed" : region.multi_project_postfix == "prod-gv" ? "-prod-gv" : ""'

# FIRESTOREACCESSSERVICE_FIRESTORE_SERVICE_ENDPOINT, READONLY_FAS_BLUE_GREEN_TARGET
_fas_urls:
  blue: 'globals.tenant_proxy + ":11123"'
  green: 'globals.tenant_proxy + ":11124"'
  perf: 'globals.tenant_proxy + ":11116"'
  black: 'globals.tenant_proxy + ":11133"'
  gold: 'globals.tenant_proxy + ":11134"'
_fas_blue_green_target: 'lower(infra_ff.enable_cortex_platform ? "gold" : "green")'

# FIRESTOREACCESSSERVICE_KEY_LOCATION
_encrypt_fas_keyring_location: '"us-central1"'

# DSSCONF_SERVER_CERTIFICATE_FILE
_dssconf_dss_url_map:
  default: '"https://app-directory-sync." + globals.region_short_code + ".apps.paloaltonetworks.com"'
  dev: '"https://app-directory-sync-qa.us.qa.appsvc.paloaltonetworks.com"'
  prod-eu: '"https://app-directory-sync.eu.paloaltonetworks.com"'
  prod-fa: '"https://app-directory-sync.fr.apps.paloaltonetworks.com"'
  prod-fr: '"https://app-directory-sync.gov.apps.paloaltonetworks.com"'
  prod-gv: '"https://app-directory-sync.fed.apps.paloaltonetworks.com"'
  prod-qt: '"https://app-directory-sync.qa.apps.paloaltonetworks.com"'
  prod-sg: '"https://app-directory-sync.sg.paloaltonetworks.com"'
  prod-uk: '"https://app-directory-sync.uk.paloaltonetworks.com"'
  prod-us: '"https://app-directory-sync.us.paloaltonetworks.com"'
  prod-pr: '"https://app-directory-sync.us.paloaltonetworks.com"'

# GCPGEOLOCATIONCONF_DATASET
_geo_location_dataset: |-
  region.is_dev ? "geolite_" + region.viso_env :
  "geolite_" + replace(region.viso_env, "-", "_")

_tenant_endpoint_scortex: 'tenant.is_legacy_metro ? "xdr-mt-" + tenant.metro_host_id + "-scortex.xdr-mt.svc.cluster.local" : globals.st_resource_prefix  + "-scortex"'

_no_proxy_domains: |-
  [
    globals.st_resource_prefix + "-frontend." + globals.st_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-frontend",
    local._frontend_url,
    globals.st_resource_prefix + "-platform",
    globals.env.CORTEX_PLATFORM_FQDN,
    ".svc.cluster.local",
    ".internal",
    ".googleapis.com",
    globals.st_resource_prefix + "-xql-engine",
    globals.st_resource_prefix + "-api-agent-be",
    globals.st_resource_prefix + "-api",
    globals.st_resource_prefix + "-api-be",
    globals.st_resource_prefix + "-api-papi",
    globals.st_resource_prefix + "-api-xql",
    globals.st_resource_prefix + "-api-xsoar",
    "localhost",
    globals.tenant_proxy,
    globals.st_resource_prefix + "-reports-chrome",
    globals.st_resource_prefix + "-xsoar",
    globals.st_resource_prefix + "-pz-schema-manager",
    globals.st_resource_prefix + "-xsoar-migration",
    globals.st_resource_prefix + "-agentix-hub-svc",
    globals.st_resource_prefix + "-chat-api-svc",
    globals.st_resource_prefix + "-mcp-svc",
    "127.0.0.1",
    region.gcp_region + "-docker.pkg.dev",
    globals.st_resource_prefix + "-xsoar-content",
    globals.st_resource_prefix + "-engine-hub",
    local._xsoar_conf_xsoar_host,
    "************",
    local._tenant_endpoint_scortex,
    globals.st_resource_prefix + "-cts." + globals.cortex_cts_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-cwp-ads-api-svc",
    globals.st_resource_prefix + "-cwp-rules-management-svc,***********",
    globals.st_resource_prefix + "-xsoar-api",
    globals.st_resource_prefix + "-cwp-api-svc",
    globals.st_resource_prefix + "-cwp-malware-analyzer-svc" + globals.cwp_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-cwp-secret-analyzer-svc" + globals.cwp_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-cwp-vulnerability-analyzer-svc" + globals.cwp_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-ciem-api-svc",
    "dspm-fda-api-svc",
    globals.st_resource_prefix + "-itdr-api-svc",
    globals.st_resource_prefix + "-apisec-bff-service-svc",
    globals.st_resource_prefix + "-cwp-ci-analyzer-svc",
    "dspm-data-classification-settings",
    globals.st_resource_prefix + "-uvem-netscan-processor",
    globals.st_resource_prefix + "-uvem-vxp-api",
    globals.st_resource_prefix + "-uvem-vip-api",
    globals.st_resource_prefix + "-uvem-netscan-controller",
    globals.st_resource_prefix + "-dp-uai-findings." + globals.st_namespace + ".svc.cluster.local:7973",
    globals.st_resource_prefix + "-dp-uai-assets." + globals.st_namespace + ".svc.cluster.local:7973",
    globals.st_resource_prefix + "-secops-dash-api" + globals.cloudsec_namespace + ".svc.cluster.local:8080",
    "source-control" + globals.cas_namespace + ".svc.cluster.local:80",
    "dashboards-api.cas.svc.cluster.local:80",
    globals.st_resource_prefix + "-dashboard-api" + globals.cloudsec_namespace + ".svc.cluster.local:8080",
    globals.st_resource_prefix + "-cwp-compliance-agent-rules-api-svc",
    globals.st_resource_prefix + "-cwp-scan-spec-manager-svc",
    "opentelemetry-collector." + globals.monitoring_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-cwp-scan-spec-manager-svc." + globals.cwp_namespace + ".svc.cluster.local:8080",
    "dp-ipl-alyx." + globals.dp_ipl_alyx_namespace + ".svc.cluster.local",
    "dspm-dacs-dashboard-svc." + globals.dspm_namespace + ".svc.cluster.local",
    globals.st_resource_prefix + "-qr-reader-server",
    globals.st_resource_prefix + "-apisec-grouping-service.xdr-st.svc.cluster.local"
  ]

# REMOTETERMINAL_RTS_ADDR, REMOTETERMINAL_RTS_WS_SERVER_ADDRESS
_lrc_addr_map:
  default: '"lrc-" + globals.region_short_code + ".paloaltonetworks.com:443"'
  dev: '"lrc.paloaltonetworks.com:443"'
  prod-es: '"lrc.es.paloaltonetworks.com:443"'
  prod-fa: '"lrc.fa.paloaltonetworks.com:443"'
  prod-il: '"lrc.il.paloaltonetworks.com:443"'
  prod-id: '"lrc.id.paloaltonetworks.com:443"'
  prod-it: '"lrc.it.paloaltonetworks.com:443"'
  prod-sa: '"lrc.sa.paloaltonetworks.com:443"'
  prod-kr: '"lrc.kr.paloaltonetworks.com:443"'
  prod-za: '"lrc.za.paloaltonetworks.com:443"'
  prod-br: '"lrc.br.paloaltonetworks.com:443"'
  prod-dl: '"lrc.dl.paloaltonetworks.com:443"'
  prod-fr: '"lrc-fed.paloaltonetworks.com:443"'
  prod-pr: '"lrc.pr.paloaltonetworks.com:443"'

_lrc_addr: 'get(local._lrc_addr_map, region.viso_env) ?? local._lrc_addr_map["default"]'

# SLACKCONF_HYDRA_REDIRECT
_slackconf_hydra_redirect_map:
  default: '"https://slck.xdr." + globals.region_short_code + ".paloaltonetworks.com/integ"'
  dev: '"https://slck.xdr-qa2-uat.us.paloaltonetworks.com/integ"'
  prod-fr: '"https://slck-prod-fed.xdr.federal.paloaltonetworks.com/integ"'
  prod-gv: '"https://slck-prod-gv.xdr.gv.paloaltonetworks.com/integ"'

# WILDFIRE_SERVER_HOSTNAME
_wildfire_url_map:
  default: 'globals.region_short_code + ".wildfire.paloaltonetworks.com"'
  dev: '"wildfire.paloaltonetworks.com"'
  prod-fa: '"fr.wildfire.paloaltonetworks.com"'
  prod-fr: '"wildfire.gov.paloaltonetworks.com"'
  prod-gv: '"us-central1.wildfire.il.gov.paloaltonetworks.com"'
  prod-qt: '"qa.wildfire.paloaltonetworks.com"'
  prod-us: '"wildfire.paloaltonetworks.com"'
  prod-pr: '"wildfire.paloaltonetworks.com"'

_st_proxydome_ip_list:
  dev: "'**************,*************'"
  prod-au: "'************,*************'"
  prod-ca: "'*************,**************'"
  prod-ch: "'*************,*************'"
  prod-de: "'**************,************'"
  prod-es: "'*************,************'"
  prod-eu: "'*************,************'"
  prod-fa: "'*************,************'"
  prod-id: "'*************,**************'"
  prod-il: "'*************,*************'"
  prod-in: "'*************,***********'"
  prod-jp: "'**************,************'"
  prod-qt: "'************,************'"
  prod-sa: "'*************,************'"
  prod-sg: "'*************,**************'"
  prod-tw: "'************,************'"
  prod-uk: "'**************,**************'"
  prod-us: "'***********,**************'"
  prod-pr: "'*************,************'"
  prod-kr: "'************,************'"
  prod-it: "'*************,*************'"
  prod-za: "'************,************'"
  prod-br: "'**************,*************'"
  prod-dl: "'*************,*************'"
  prod-fr: "'**************,*************'"
  prod-gv: "'**************,************'"

_harvester_ip_list:
  dev: "'*************,**************'"
  prod-au: "'**************,*************'"
  prod-ca: "'***********,************'"
  prod-ch: "'*************,**********'"
  prod-de: "'************,************'"
  prod-es: "'*************,*************'"
  prod-eu: "'************,**************'"
  prod-fa: "'**************,**************'"
  prod-fr: "'************,*************'"
  prod-id: "'*************,*************'"
  prod-il: "'**************,**************'"
  prod-in: "'*************,***********'"
  prod-it: "'**************,*************'"
  prod-jp: "'************,************'"
  prod-kr: "'***********,*************'"
  prod-pl: "'*************,**************'"
  prod-pr: "'**************,************'"
  prod-qt: "'***********,************'"
  prod-sa: "'************,*************'"
  prod-sg: "'*************,*************'"
  prod-tw: "'*************,**************'"
  prod-uk: "'*************,**************'"
  prod-us: "'************,35.202.21.123'"
  prod-za: "'34.35.60.86,34.35.69.156'"
  prod-br: "'34.39.177.125,34.39.140.36'"
  prod-dl: "'34.131.111.87,34.131.101.138'"

# XCLOUDCONF_AWS_MASTER_PATH, XCLOUDCONF_AWS_MEMBER_PATH
_xcloud_onboarding_s3_bucket_url: |-
  region.is_dev ? "https://cortex-xdr-dev-onboarding-scripts.s3.us-east-2.amazonaws.com" :
  region.is_fedramp ? "https://cortex-xdr-gov-onboarding-scripts.s3-us-gov-east-1.amazonaws.com" :
  "https://cortex-xdr-onboarding-scripts.s3.us-east-2.amazonaws.com"

_xcloud_onboarding_s3_fedramp_bucket_url: |-
  region.is_dev ? "https://cortex-xdr-dev-onboarding-scripts.s3.us-east-2.amazonaws.com" :
  region.is_fedramp ? "https://cortex-xdr-gov-onboarding-scripts.s3-us-gov-east-1.amazonaws.com" :
  "https://cortex-xdr-onboarding-scripts.s3.us-east-2.amazonaws.com"

_xcloud_onboarding_s3_commercial_bucket_url: |-
  region.is_dev ? "https://cortex-xdr-dev-onboarding-scripts.s3.us-east-2.amazonaws.com" :
  "https://cortex-xdr-onboarding-scripts.s3.us-east-2.amazonaws.com"

_certs_project: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'

# XpanseThreatEventsConf_global_storage_bucket_name, XpanseComplianceFrameworksConf_global_storage_bucket_name, XpanseTechnologyMetadataConf_global_storage_bucket_name
_xpanse_multi_tenants_bucket_name: '(region.viso_env in region.non_prod_project_prefixes) ? "xpanse-policies-" + region.multi_project_postfix : "xpanse-policies-prod"'

_shared_resources_aws_accounts:
  dev: 'tenant.creation_date > ************* ? "************" : "************"'
  prod-commercial-all: '"************"'
  prod-fr: '"************"'
  prod-gv: '"************"'

fullnameOverride: 'tenant.lcaas_id + "-configmap"'
namespaceOverride: globals.st_namespace

rocksdb:
  resources:
    limits:
      cpu: globals.rocksdb_cpu
      memory: globals.rocksdb_memory
    requests: local.rocksdb.resources.limits

config:
  URLPROTECTION_API_HOST: 'globals.tenant_proxy + ":11135"'
  ISSUESCONF_INGESTION_FEEDBACK_TOPIC: '"ap-issue-ingest-feedback-" + tenant.lcaas_id'
  ISSUESCONF_INGESTION_FEEDBACK_VXP_SUBSCRIPTION: '"ap-issue-ingest-feedback-vxp-" + tenant.lcaas_id + "-sub"'
  CTSCONF_SECRET_KMS_KEY_NAME: tenant.lcaas_id
  CTSCONF_SECRET_KMS_KEY_RING: "'cts-security'"
  CTSCONF_SECRET_KMS_LOCATION: globals.kms_keyring_region
  CTSCONF_SECRET_KMS_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  CTSCONF_URL_TARGET_HOST: globals.env.CTSCONF_URL_TARGET_HOST
  ALERTSCONF_APPLY_RETENTION_SIMULATION: 'license.is_xsoar ? "False" : nil'
  XPANSECONF_ENABLE_XPANSE_CONTENT_SEARCH: license.is_xpanse
  CLOUDTAGSCONF_ALWAYS_RETRIEVE_CLOUD_TAGS_FOR_ASSETS: license.is_xpanse
  UNIFIEDINVENTORYCONF_ENABLE_ASN_DETAILS: '"True"'
  UNIFIEDINVENTORYCONF_ENABLE_CERTIFICATE_PEM_REPLICATION: license.is_xpanse
  XPANSEEFFECTIVEIPRANGECONF_STAT_COLLECTION_ENABLED: license.is_xpanse
  SUPPORTCASECONF_STATS_DATASET: '"metrics_center_" + replace(region.viso_env , "-", "_")'
  DSSCONF_XQL_AD_OBJECTS_REDIS_USE_DEFAULT: 'infra_ff.enable_redis_dss ? "False" : "True"'
  HPL_DSS_REDIS_CONNECTION_STRING: 'globals.st_resource_prefix + "-dss-redis:6379"'
  DSSCONF_XQL_AD_OBJECTS_REDIS_HOST: 'globals.st_resource_prefix + "-dss-redis"'
  HPL_DSS_REDIS_USE_DEFAULT: 'infra_ff.enable_redis_dss ? "False" : "True"'
  ANALYTICSCONF_CONTENT_LOADER_DATA_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-feature-data"'
  ANALYTICSCONF_DETECTION_ENGINE_PUBSUB_MULTI_EVENTS_DESTINATION_BUCKET: 'tenant.project_id + "-edr-data"'
  ANALYTICSTASKPROCESSOR_PS_SUBSCRIPTION: '"analytics-task-processor-" + tenant.lcaas_id + "-sub"'
  ANALYTICSTASKPROCESSOR_PS_TOPIC: '"analytics-task-processor-" + tenant.lcaas_id'
  AnalyticsEmailBiConf_email_bi_project_id: '"xdr-bi-email-data-" + region.viso_env + "-01"'
  AnalyticsEmailBiConf_email_bi_kms_key_location: region.gcp_region
  AnalyticsEmailBiConf_email_bi_topic_name: "'bi-email-data'"
  AnalyticsEmailBiConf_email_bi_kms_key_ring: "'bi-email-data'"
  AnalyticsEmailBiConf_email_bi_kms_key_name: "'bi_email_data_key'"
  AnalyticsEmailBiConf_email_kms_dek_cache_location: "region.gcp_region"
  AnalyticsEmailBiConf_email_kms_dek_cache_project_id: tenant.project_id
  ASSOCIATIONREPLICATION_ASSOCIATION_REPLICATION_ENABLED: 'license.is_epp_only ? "false" : nil'
  AGENTAPICONF_REDIS_HOST: 'infra_ff.enable_redis_agent_api ? globals.st_resource_prefix + "-agent-api-redis" : nil'
  AGENTAPICONF_REDIS_PORT: 'infra_ff.enable_redis_agent_api ? "6379" : nil'
  ANALYTICSCONF_DEDUP_COUNTERS_VISIBILITY: 'license.is_small_epp ? "False" : nil'
  XQLSYNCSERVICECONF_UPDATE_SYSTEM_AUDIT: 'license.is_small_epp ? "False" : nil'
  XQLSYNCSERVICECONF_UPDATE_INCIDENTS_XQL: 'license.is_small_epp ? "False" : nil'
  STORYBUILDERDWPCONF_ENABLED: 'license.is_small_epp || (tenant.is_metro_tenant && !tenant.is_metro_host) ? "False" : nil'
  SUPPORTCASECONF_CSP_GENERAL_LINK: 'region.viso_env  != "dev" ? "https://supportcases.paloaltonetworks.com/s/" : nil'
  APPLICATIONHUB_READINESS_CHECK_JOB_MAX_TIME_SEC: "'3600'"
  ALPHAFEATURES_FORENSICS_TENANT: license.forensics
  ALPHAFEATURES_FORENSICS_MODULE: license.forensics
  ALPHAFEATURES_FORENSICS_DISSOLVABLE_AGENT: license.forensics
  ALPHAFEATURES_PRISMA_TENANT_LINK_ENABLED: 'region.is_fedramp ? "False" : "True"'
  ALPHAFEATURES_API_POLLING_ENABLED: "license.is_xpanse || globals.is_xsiam ? true : nil"
  ALPHAFEATURES_UNIT42_ENABLED: "region.is_fedramp ? false : nil"
  ALPHAFEATURES_ENABLE_AGENTIX_MODE: 'globals.is_agentix_deployment ? globals.product_code_agentix ? "True" : "False" : nil'
  ALPHAFEATURES_ENABLE_AGENTIX_MODE_IN_PLATFORM: 'globals.is_agentix_deployment ? infra_ff.is_enable_agentix_hub ? "True" : "False" : nil'
  AGENT_REPORTS_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-agent-reports"'
  AGENTANALYTICSCONF_PROJECT_ID: '"xdr-agent-analytics-" + region.viso_env + "-01"'
  AGENTANALYTICSCONF_PUBSUB_TOPIC_NAME: "'agent-analytics-topic'"
  AGENTCONF_CC_URL: '"https://cc-" + local._fqdn_suffix + ".traps.paloaltonetworks.com"'
  AGENTCONF_CDC_URL: '"https://dc-" + local._fqdn_suffix + ".traps.paloaltonetworks.com"'
  AGENTCONF_CH_URL: '"https://ch-" + local._fqdn_suffix + ".traps.paloaltonetworks.com"'
  AGENTINTEGRATION_CWP_COMPLIANCE_BASE_URL: '"http://" + globals.st_resource_prefix + "-cwp-compliance-agent-rules-api-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  ALERTSCONF_QUEUE_BUCKET_NAME: 'tenant.project_id + "-alerts-queue"'
  AgentFeatureFlagsConf_ff_fedramp: 'region.viso_env == "prod-fr" ? 1 : region.viso_env == "prod-gv" ? 2 : 0'
  ANALYTICSCONF_EXTERNAL_DATASET_BUCKET_NAME: 'region.is_fedramp ? "global-bioc-rules-" + region.viso_env : region.viso_env == "dev" ? "global-bioc-rules-dev" : "global-bioc-rules-prod"'
  ALERTSCONF_FETCHER_TOPIC_NAME: '"alerts-fetcher-" + tenant.lcaas_id'
  ALERTSCONF_FETCHER_SUBSCRIPTION_NAME: "'alerts-fetcher'"
  # CRTX-93789
  RETENTIONCONF_EGRESS_RAW_BUCKET: 'tenant.project_id + "-egress-raw"'
  ALERTSFETCHERCONF_GLOBAL_STORAGE_BUCKET_NAME: 'region.is_fedramp ? "global-pan-content-rules-" + region.viso_env : region.viso_env == "dev" ? "global-pan-content-rules-dev" : "global-pan-content-rules"'
  ALERTSFETCHERCONF_GLOBAL_STORAGE_PROJECT_ID: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'
  ALERTSFETCHERCONF_PUBSUB_EXT_LOGS_SUBSCRIPTION_NAME: "'inr-ext-logs-sub'"
  ALERTSFETCHERCONF_PUBSUB_EXT_LOGS_TOPIC: '"ext-logs-" + tenant.lcaas_id'
  ALERTSFETCHERCONF_REDIS_ENABLED: 'infra_ff.enable_redis_fetcher ? "True" : nil'
  ALERTSFETCHERCONF_REDIS_HOST: 'infra_ff.enable_redis_fetcher ? globals.st_resource_prefix + "-fetcher-redis" : nil'
  ALERTSFETCHERCONF_REDIS_PORT: 'infra_ff.enable_redis_fetcher ? "6379" : nil'
  AnalyticsDDE_metaline_bucket: 'tenant.project_id + "-analytics-data"'
  AnalyticsConf_multi_tenant_raw_bq_connector_bq_dataset_name: "'analytics_source_tables'"
  AnalyticsConf_multi_tenant_raw_bq_connector_project_id: 'region.viso_env == "dev" ? "xdr-res-analytics-dev-01" : region.viso_env == "prod-fr" ? "xdr-res-analytics-prod-fr-01" : region.viso_env == "prod-gv" ? "xdr-res-analytics-prod-gv-01" : "xdr-res-analytics-prod-us-01"'
  ANALYTICSCONF_MULTI_TENANT_RAW_BQ_CONNECTOR_GLOBAL_BUCKET_NAME: 'region.viso_env == "dev" ? "analytics-source-tables-dev" : region.viso_env == "prod-fr" ? "analytics-source-tables-prod-fr" : region.viso_env == "prod-gv" ? "analytics-source-tables-prod-gv" : "analytics-source-tables-prod-us"'
  ANALYTICSCONF_REDIS_ENABLED: 'infra_ff.enable_redis_split ? "True" : "False"'
  ANALYTICSCONF_REDIS_HOST: globals.env.REDIS_ANALYTICS_HOST
  ANALYTICSCONF_REDIS_PORT: "'6379'"
  ANALYTICSGLOBALPROFILES_PROJECT_ID: '"xdr-global-profiles-" + globals.global_profiles_project_env + "-01"'
  AnalyticsGlobalProfiles_firestore_project: '"xdr-global-profiles-" + globals.global_profiles_project_env + "-01"'
  AnalyticsGlobalProfiles_provisioning_bq_location: upper(globals.global_profiles_source_region)
  ANALYTICSGLOBALPROFILES_REGIONAL_BUCKET_EXPORT_PREFIX: 'region.viso_env == "dev" ? "dev-xdr-analytics-global-profiles" : region.viso_env == "prod-fr" ? "federal-xdr-analytics-global-profiles" : region.viso_env == "prod-gv" ? "federal-gv-xdr-analytics-global-profiles" : "xdr-analytics-global-profiles"'
  ANALYTICSGLOBALPROFILES_IS_ENABLED: "'True'"
  ANALYTICSGLOBALPROFILES_CAN_CONTRIBUTE: 'region.viso_env != "dev" && !region.is_fedramp && (tenant.authcode == "internal" || (region.viso_env in globals.global_profiles_small_regions_list)) ? "False" : "True"'
  AnalyticsGlobalProfiles_profiles_source_region: '(region.viso_env in globals.global_profiles_eu_regions) ? "eu" : "us"'
  ANALYTICSCONF_ASSOCIATION_MIGRATION_WINDOW_DAYS: 8
  ANALYTICSCONF_BIG_QUERY_EXPORT_DATA_BUCKET_NAME: 'tenant.project_id + "-a2-bq-data"'
  ANALYTICSCONF_BIG_QUERY_SRC_DATASET: '"ds_" + tenant.lcaas_id'
  ANALYTICSCONF_BIG_QUERY_SRC_PROJECT: tenant.project_id
  ANALYTICSCONF_BQ_DATASET: '"analytics_" + tenant.lcaas_id'
  ANALYTICSCONF_DETECTION_ENGINE_READ_SUBSCRIPTION: 'infra_ff.enable_pipeline || license.is_xpanse ? "edr-matching-service" : "A2_NOT_ENABLED"'
  ANALYTICSCONF_DETECTION_HITS_TOPIC: 'infra_ff.enable_pipeline || license.is_xpanse ? globals.topics.analytics_detection_hits : "A2_NOT_ENABLED"'
  ANALYTICSCONF_DISABLE_ANALYTICS_MIGRATIONS: "'false'"
  ANALYTICSCONF_EMITTER_SUBSCRIPTION_NAME: 'infra_ff.enable_pipeline || license.is_xpanse ? "alerts-emitter" : "A2_NOT_ENABLED"'
  ANALYTICSCONF_IOC_BLOOM_FILTER_CACHE_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-feature-data"'
  ANALYTICSCONF_TIMEZONE_OFFSET: globals.timezone_offset
  ANALYTICSCONF_ROCKSDB_GRPC_CHANNEL_LOCK: 'infra_ff.enable_rocksdb_blue_green_mode  ? "True" : nil'
  ANALYTICSNOTIFICATIONAPICONF_SUBSCRIPTION_NAME: '"ext-notifications-" + tenant.lcaas_id + "-sub"'
  ANALYTICSNOTIFICATIONAPICONF_TOPIC_NAME: '"ext-notifications-" + tenant.lcaas_id'
  ANALYTICSONDEMANDCONF_CONTENT_BUCKET_NAME: 'region.viso_env == "dev" ? "analytics-on-demand-content-dev" : region.is_fedramp ? "" : "analytics-on-demand-content-prod"'
  ANALYTICSONDEMANDCONF_IS_ENABLED: 'region.is_fedramp ? "False" : "True"'
  ANALYTICSONDEMANDCONF_JOBS_PROJECT_ID: 'region.is_fedramp ? "" : "xdr-bq-mt-stats-" + region.viso_env + "-01"'
  ANALYTICSONDEMANDCONF_INTERIM_CALC_RESULTS_BQ_DATASET: '"analytics_on_demand_" + tenant.lcaas_id'
  ANALYTICSCONF_PROFILES_VIEWS_DATASET: '"analytics_profiles_views_" + tenant.lcaas_id'
  ANALYTICSCONF_PROFILES_ROCKSDB_HOST: "'analytics-rocksdb'"
  ASMPublicApiServiceConf_asm_asset_limit: 'license.is_xpanse ? "5000" : "500"'
  ASMPublicApiServiceConf_external_ip_range_limit: 'license.is_xpanse ? "5000" : "1000"'
  BQSTATSCONF_BQ_STATS_PROJECT: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  BQSTATSCONF_ST_SUBSCRIPTION_NAME: '"bq-stats-" + tenant.lcaas_id + "-sub"'
  BQSTATSCONF_ST_TOPIC_NAME: '"bq-stats-" + tenant.lcaas_id'
  BQSTATSCONF_BQ_STATS_METRICS_CENTER_DATASET: '"metrics_center_" + replace(region.viso_env, "-", "_")'
  BROKERCONF_BROKER_BUCKET_NAME: '"xdr-ova-installers-" + region.multi_project_postfix'
  BROKERCONF_FILE_COLLECTOR_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-ext-files"'
  BROKERCONF_LOG_REQUEST_BUCKET_NAME: 'region.project_prefix + "-" + tenant.lcaas_id + "-broker"'
  CA_COLLECTION_CLOUD_PROJECT_ID: tenant.project_id
  CA_COLLECTION_LCAAS_ID: tenant.lcaas_id
  CA_COLLECTION_REDIS_CONNECTION_STRING: "infra_ff.enable_cortex_platform ? globals.env.REDIS_CA_COLLECTION_URI : nil"
  CA_COLLECTION_MY_SQL_DB_NAME: 'tenant.lcaas_id + "_main"'
  CA_COLLECTION_MYSQL_DB_NAME: 'tenant.lcaas_id + "_main"'
  CA_COLLECTION_MYSQL_DDL_ENDPOINT: "''"
  CA_COLLECTION_MYSQL_DML_ENDPOINT: 'tenant.is_metro_tenant ? "xdr-mt-" + tenant.metro_host_id + "-mysql.xdr-mt.svc.cluster.local" : globals.st_resource_prefix + "-mysql"'
  CLOUDASSETDISCOVERY_CA_COORDINATOR_SERVICE: '"http://xdr-st-" + tenant.lcaas_id + "-ca-collection-coordinator-service:80"'
  CLOUDONBOARDING_AZURE_STORAGE_ALLOWED_IPS: 'get(local._st_proxydome_ip_list, region.viso_env) ?? ""'
  THIRDPARTYSAAS_COLLECTION_STATIC_IPS: 'infra_ff.enable_cortex_platform ? get(local._harvester_ip_list, region.viso_env) ?? "" : nil'
  CLOUDONBOARDING_MT_KMS_ACCOUNT: 'get(local._shared_resources_aws_accounts, region.viso_env) ?? (local._shared_resources_aws_accounts["prod-commercial-all"])'
  CLOUDONBOARDING_TEMPLATES_BUCKET: 'tenant.project_id + "-cloud-onboarding-templates"'
  CLOUDONBOARDING_ACCOUNTS_NOTIFICATION_TOPIC: '"cloud-accounts-" + tenant.lcaas_id'
  CLOUDONBOARDING_TASKS_TOPIC: '"cloud-onboarding-tasks-" + tenant.lcaas_id'
  CLOUDONBOARDING_TASK_SUB: "'cloud-onboarding-tasks-sub'"
  CLOUDONBOARDING_AWS_SHARED_RESOURCES_ACCOUNT_ID: "infra_ff.enable_cortex_platform ? globals.cwp_aws_shared_resources_account_id : nil"
  CLASSIFICATIONMANAGEMENTCLIENT_HOST: '"dspm-dacs-dashboard-svc." + globals.dspm_namespace + ".svc.cluster.local"'
  CLASSIFICATIONMANAGEMENTCLIENT_DATA_PATTERN_UPDATE_SUBSCRIPTION: '"classification-mgmt-data-pattern-update-sub-" + tenant.lcaas_id'
  CLASSIFICATIONMANAGEMENTCLIENT_PROFILE_UPDATE_SUBSCRIPTION: '"classification-mgmt-profile-update-sub-" + tenant.lcaas_id'
  CLASSIFICATIONMANAGEMENTCLIENT_GLOBAL_SETTINGS_UPDATE_SUBSCRIPTION: '"classification-mgmt-global-settings-update-sub-" + tenant.lcaas_id'
  COLLECTIONOAUTH2_REDIRECT_URL: 'region.viso_env == "prod-fr" ? "https://agent-gateway-public-api-prod-fed.traps.paloaltonetworks.com" : "https://agent-gateway-public-api-" + region.viso_env + ".traps.paloaltonetworks.com"'
  COMMUNICATION_SLACK_CHANNEL_NAME: "'xdr-app-errors-prod'"
  COMMUNICATION_SLACK_CHANNEL_NAME_NEXUS: "'cortexdr-prod-errors'"
  COMMUNICATION_USE_SLACK: 'region.project_prefix == "xdr-us" || region.project_prefix == "xdr-eu" ? "True" : "False"'
  CONTENTCONF_STORAGE_BUCKET_NAME: 'region.is_fedramp ? "global-content-profiles-policy-" + region.viso_env : region.viso_env == "dev" ? "global-content-profiles-policy-dev" : "global-content-profiles-policy"'
  CONTENTCONF_STORAGE_PROJECT_ID: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'
  ContextualSearchGraphConf_is_contextual_search_graph_enabled: 'tenant.is_migrated_cortex_platform ? nil : "True"'
  CORRELATIONSRULESCONF_BQ_TEMP_TABLES_DATASET: "'correlation_temp_tables'"
  COPILOT_CHAT_API_URL: 'globals.st_resource_prefix + "-chat-api-svc:2020"'
  COPILOT_MT_PROJECT_ID: '"xdr-cortex-copilot-" + region.viso_env + "-01"'
  COPILOT_CHAT_SERVICE_PROXY: "'https://************:11115'"
  COPILOT_CHAT_SERVICE_URL: 'region.viso_env + ".copilot.crtx.paloaltonetworks.com"'
  COPILOT_CHAT_SERVICE_CLOUD_RUN_AUDIENCE: '"cortex-copilot-" + region.viso_env'
  COPILOT_COPILOT_STATS_PROJECT: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  COPILOT_COPILOT_STATS_REGION: tenant.bq_location
  COPILOT_MODEL_GARDEN_LOCATION: region.gcp_region
  CORTEX_PLATFORM_URL: "infra_ff.enable_cortex_platform ? globals.env.CORTEX_PLATFORM_URL : nil"
  CORTEX_PLATFORM_PUBLIC_DS: '"public_platform_" + tenant.lcaas_id'
  CORTEX_PLATFORM_DYNAMIC_CONFIG_DIR: '"/etc/config"'
  CORTEX_PLATFORM_REDIS_ENABLED: 'infra_ff.enable_redis_platform ? "True" : nil'
  CORTEX_PLATFORM_REDIS_HOST: 'infra_ff.enable_redis_platform ? "xdr-st-" + tenant.lcaas_id + "-platform-redis" : nil'
  CORTEX_PLATFORM_REDIS_PORT: 'infra_ff.enable_redis_platform ? "6379" : nil'
  CORTEXASMMONITORING_MT_BUCKET_NAME: 'infra_ff.enable_cortex_platform ? region.viso_env == "dev" ? "cortex-asm-monitoring-dev" : "cortex-asm-monitoring-prod" : nil'
  CORTEXASMMONITORING_MT_PROJECT: 'infra_ff.enable_cortex_platform ? region.viso_env == "dev" ? "xp-gcp-h-dev-s-dev" : "xp-gcp-h-prod-s-prod" : nil'
  CwpOnCortex_bucket_name: 'tenant.project_id + "-cwp-scan-results"'
  CwpOnCortex_scan_results_topic: '"cwp-scan-results-" + tenant.lcaas_id'
  CwpOnCortex_environment_enabled: "infra_ff.enable_cortex_platform ? true : false"
  CwpOnCortex_scan_spec_manager_url: '"http://" + globals.st_resource_prefix + "-cwp-scan-spec-manager-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  CWPREDISCONF_DB_NUMBER: 'infra_ff.enable_cortex_platform ? "0" : nil'
  CWPREDISCONF_HOST: "infra_ff.enable_cortex_platform ? globals.env.REDIS_CWP_URI : nil"
  CWPREDISCONF_PORT: 'infra_ff.enable_cortex_platform ? "6379" : nil'
  PLATFORM_IS_ACTIVE: "infra_ff.enable_cortex_platform ? true : nil"
  DASHBOARDENGINECONF_UNUSED_WIDGETS_EXPIRY_DAYS: "'5'"
  DataIngestionHealth_subscription: '"health-alerts-" + tenant.lcaas_id + "_sub"'
  DataIngestionHealth_topic: '"health-alerts-" + tenant.lcaas_id'
  DATA_PLATFORM_ASSETS_TARGET_HOST: '"xdr-st-" + tenant.lcaas_id + "-dp-uai-assets." + globals.st_namespace + ".svc.cluster.local:7973"'
  DATA_PLATFORM_FINDINGS_TARGET_HOST: '"xdr-st-" + tenant.lcaas_id + "-dp-uai-findings." + globals.st_namespace + ".svc.cluster.local:7973"'
  DATAVISIBILITY_INGEST_RATE_LIMIT_SUBSCRIPTION: '"stitched-" + tenant.lcaas_id'
  DISTRIBUTIONSERVICECONF_CLOUD_RUN_PROJECT: '"xdr-cloudrun-" + region.multi_project_postfix + "-01"'
  DISTRIBUTIONSERVICECONF_DISTRIBUTIONS_BUCKET_NAME: 'tenant.project_id + "-distributions"'
  DISTRIBUTIONSERVICECONF_INSTALLERS_BUCKET_NAME: '"panw-xdr-installers-" + region.multi_project_postfix'
  DISTRIBUTIONSERVICECONF_REGISTER_URL: '"https://distributions" + globals._distribution_postfix + ".traps.paloaltonetworks.com"'
  DISTRIBUTIONSERVICECONF_KMS_KEY_PROJECT: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  DISTRIBUTIONSERVICECONF_K8S_KONNECTOR_REALTIME_AGENT_VERSION: 'region.is_fedramp ? "9.0.0.141088" : region.viso_env == "dev" ? "9.0.0.141079" : "9.0.0.141085"'
  DISTRIBUTION_KEY_PROJECT: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  DMLCONFIG_network_meta_hours_to_keep: "'24'"
  DMLCONFIG_network_raw_hours_to_keep: "'30'"
  DockerImageConf_artifactory_url: 'region.gcp_region + "-docker.pkg.dev"'
  DockerImageConf_repository_name: "'agent-docker'"
  DSSCONF_CERT_KEY: "'/etc/cert/dss.key'"
  DSSCONF_CERT_PATH: "'/etc/cert/dss.pem'"
  DSSCONF_DSS_URL: 'infra_ff.enable_assured_workloads ? "https://app-directory-sync.de-rg.apps.paloaltonetworks.com" : get(local._dssconf_dss_url_map, region.viso_env) ?? local._dssconf_dss_url_map["default"]'
  DSSCONF_SERVER_CERTIFICATE_FILE: "'/etc/cert/dss_server.crt'"
  DSSCONF_SYNC_WITH_AD: tenant.enable_dirsync
  DUMPSTERCONF_ACTIVATE_GCS_MODE: '(region.viso_env in globals.dumpsterconf_activate_gcs_mode) ? "True" : "False"'
  DUMPSTERCONF_ANALYSIS_REPORTS_BUCKET: '"dumpster-analysis-reports-" + region.viso_env'
  DUMPSTERCONF_MT_PROJECT_ID: '"xdr-dumpster-" + region.viso_env + "-01"'
  ENABLE_CLOUD_POSTURE: globals.enable_cloud_posture
  EMAILARTIFACTSRELAY_DECRYPTED_ATTACHMENTS_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-unknown-mail-attachments-wf-uploads"'
  ExternalNotificationsConf_pubsub_subscription: "'notifications-notifier'"
  EXTERNALINTEGRATIONFORWARDERCONF_EXTERNAL_INTEGRATION_FORWARDER_PS_SUBSCRIPTION: '"external-integration-data-forwarder-" + tenant.lcaas_id + "-sub"'
  EXTERNALINTEGRATIONFORWARDERCONF_EXTERNAL_INTEGRATION_FORWARDER_PS_TOPIC: '"external-integration-data-forwarder-" + tenant.lcaas_id'
  EXPORTCONF_EXPORT_AGENTS_GCS_BUCKET_NAME: 'region.project_prefix + "-" + tenant.lcaas_id + "-user-exports"'
  FIRESTOREACCESSSERVICE_FIRESTORE_SERVICE_ENDPOINT: local._fas_urls[local._fas_blue_green_target]
  FIRESTOREACCESSSERVICE_KEY_LOCATION: local._encrypt_fas_keyring_location
  FIRESTOREACCESSSERVICE_KEY_RING: "'tenants'"
  FIRESTOREACCESSSERVICE_KEY_VERSION: 1
  FIRESTOREACCESSSERVICE_KMS_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  FIRESTORECONF_PROJECT_ID: '"xdr-firestore-" + (region.viso_env == "dev" ? "public-dev" : region.is_commercial ? "pub-prod-us" : "pub-"+region.viso_env ) + "-01"'
  FLIPPERCONF_BUCKET_NAME: 'region.is_fedramp ? "changed-verdicts-" + region.viso_env : region.viso_env == "dev" ? "changed-verdicts-dev" : "changed-verdicts-prod"'
  FLIPPERCONF_CHANGED_VERDICTS_API_URL: 'infra_ff.enable_assured_workloads ? "https://globalservice-eu-reg.wildfire.paloaltonetworks.com/service/wildfire/changed/verdicts" : (get(globals._gvs_url_map, region.viso_env) ?? globals._gvs_url_map["default"]) + "/service/wildfire/changed/verdicts"'
  FLIPPERCONF_MISSED_FLIPS_BUCKET_NAME: 'region.viso_env == "dev" ? "missing-wf-verdict-flips-dev" : region.is_fedramp ? "missing-wf-verdict-flips-" + region.viso_env : "missing-wf-verdict-flips-prod"'
  FORENSICSCONF_DATASET_ID: '"forensics_" + tenant.lcaas_id'
  FORENSICSCONF_EXPORT_BUCKET: 'tenant.project_id + "-forensics"'
  IosConf_use_sandbox: 'region.viso_env == "dev" ? "True" : "False"'
  IosConf_auth_key_path: "'/etc/ssh/ApnKey.p8'"
  GCPCONF_ALERTS_BQ_JOB_TIMEOUT_MS: 'license.is_xsoar ? "960000" : nil'
  GCPCONF_BACKUP_EXTERNAL_DATASET: 'region.viso_env == "dev" ? "False" : "True"'
  GCPCONF_BQ_DATASET: '"ds_" + tenant.lcaas_id'
  GCPCONF_BQ_ENUMS_DATASET: '"enums_" + tenant.lcaas_id'
  GCPCONF_BQ_EXTERNAL_DATASET: '"external_data_" + tenant.lcaas_id'
  GCPCONF_BQ_TEMP_TABLE_DATASET: '"temp_tables_" + tenant.lcaas_id'
  GCPCONF_BQ_REWARMED_DS_NAME: '"rewarmed_tables_" + tenant.lcaas_id'
  GCPCONF_INGESTION_RATE_METRIC_NAME: "'xdr:xql_ingestion_raw_size_bytes:rate1h'"
  GCPCONF_PROJECT_ID: tenant.project_id
  GCPCONF_REAL_REGION: region.gcp_region
  GCPCONF_REGION: tenant.bq_location
  GCPCONF_REGION_ABBR: "globals.gcpconf_region_abbr"
  GCPCONF_ENV_REGION: region.viso_env
  GCPCONF_CORTEX_REGION_ABBR: 'infra_ff.enable_assured_workloads ? "de-rg" : region.viso_env == "dev" ? "us" : region.is_fedramp ? "gov" : globals.region_short_code'
  GCPCONF_METRO_HOST_PROJECT_ID: tenant.tenant_cluster_project_id
  GCPGEOLOCATIONCONF_DATASET: 'region.viso_env == "dev" ? "geolite_dev" : local._geo_location_dataset'
  GCPGEOLOCATIONCONF_PROJECT: 'region.viso_env == "dev" ? "xdr-bq-mt-stats-dev-01" : "xdr-bq-mt-stats-" + region.viso_env + "-01"'
  GCPGEOLOCATIONCONF_TABLE: "'maxmind'"
  GCPPUBSUB_EDR_TOPIC_NAME: '"edr-" + tenant.lcaas_id'
  GCPPUBSUB_EXT_LOGS_TOPIC_NAME: '"ext-logs-" + tenant.lcaas_id'
  GCPPUBSUB_LCAAS_TOPIC_NAME: '"lcaas-" + tenant.lcaas_id'
  GCPPUBSUB_XDR_ANALYTICS_PROJECT_ID: 'region.is_fedramp ? "" : region.is_dev ? "xdr-collection-qa-01" : "xdr-collection-" + region.viso_env + "-01"'
  GCPPUBSUB_XDR_ANALYTICS_TOPIC_NAME: 'region.is_fedramp ? "" : "analytics-ingress"'
  GCPPUBSUB_XDR_ANALYTICS_PROJECT_ID_ENABLED: '(region.viso_env == "dev" || region.is_fedramp) ? "False" : "True"'
  GENERIC_ALLOW_EXTERNAL_CONFIG_SETTING: "'False'"
  GENERIC_CSP_ACCOUNT_NAME: license.support_account_name
  GENERIC_CSP_ID: "license.support_account_id"
  GENERIC_DEFAULT_URL_TARGET_HOST: 'globals.st_resource_prefix + "-api" + (local._is_metro_v2 ? "." + globals.st_namespace + ".svc.cluster.local": "") + ":4999"'
  POLICYCONF_POLICY_RECALC_TASK_TARGET_HOST: 'infra_ff.is_enable_api_split ? globals.st_resource_prefix + "-api-agent-be:4999" : nil'
  PUBLICAPI_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split ? "-api-papi" : "-api") + (local._is_metro_v2 ? "." + globals.st_namespace + ".svc.cluster.local":"") + ":4999"'
  WLM_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split ? "-api-be" : "-api") + (local._is_metro_v2 ? "." + globals.st_namespace + ".svc.cluster.local":"") + ":4999"'
  SCHEDULERCONF_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split ? "-api-be" : "-api") + "." + globals.st_namespace + ".svc.cluster.local" + ":4999"'
  XSOARCONF_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split && globals.is_xsiam ? "-api-xsoar" : "-api") + (local._is_metro_v2 ? "." + globals.st_namespace + ".svc.cluster.local":"") + ":4999"'
  XQLGLOBALCONFDEFAULT_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split ? "-api-xql" : "-api") + ":4999"'
  GENERIC_DML_ACTIVATED: 'infra_ff.enable_pipeline ? "True" : "False"'
  GENERIC_ENABLE_ANALYTICS: "!region.is_dev"
  GENERIC_EXTERNAL_FQDN: globals.external_fqdn
  GENERIC_IS_EPP: '!infra_ff.enable_pipeline && !license.is_xpanse ? "True" : "False"'
  GENERIC_IS_GCP_SETUP: "'True'"
  GENERIC_TENANT_TYPE: 'license.is_xpanse ? "xpanse" : nil'
  GENERIC_XDR_ENVIRONMENT: region.xdr_env
  GENERIC_XDR_ID: tenant.xdr_id
  GENERIC_CORTEX_ID: tenant.xdr_id
  GENERIC_PRODUCT_TYPE: lower(license.product_type)
  GENERIC_PRODUCT_CODE: license.product_code
  GENERIC_XDR_TENANT_NAME: tenant.instance_name
  GENERIC_XDR_TENANT_TYPE: tenant.authcode
  GENERIC_IS_METRO: 'tenant.is_metro_tenant ? "True" : "False"'
  GENERIC_TENANT_DEPLOYMENT_BATCH: 'region.is_fedramp ? nil : region.is_dev ? "P1" : tenant.upgrade_phase'
  GLOBALCONF_GCS_BUCKET: '"xdr-global-configs-" + (region.viso_env == "dev" ? "dev" : region.is_fedramp ? region.viso_env : "prod")'
  GLOBALCONF_GCS_BUCKET_REGION_FOLDER: region.viso_env
  GLOBALVERDICTSERVICECONF_CRT_FILE_PATH: "'/cert/wf-verdict-service.crt.pem'"
  GLOBALVERDICTSERVICECONF_FF_USE_V1: 'region.is_fedramp ? "True" : "False"'
  GLOBALVERDICTSERVICECONF_KEY_FILE_PATH: "'/cert/wf-verdict-service.key.pem'"
  GLOBALVERDICTSERVICECONF_VERDICT_SERVICE_HOST: "globals.global_verdict_service_conf_verdict_service_host"
  GONZO_ENV: region.viso_env
  GONZO_LOGGER_TRANSPORT: 'region.viso_env == "dev" && !tenant.is_metro_tenant ? "console:-1" : "console:0,slack:2"'
  GONZO_PROJECT_ID: tenant.project_id
  GONZO_REDIS_CONNECTION_STRING: 'globals.env.REDIS_MAIN_HOST + ":6379"'
  GONZO_REGION: region.gcp_region
  GONZO_SLACK_CHANNEL: '"xdr-pipeline-" + region.viso_env'
  GCP_PROJECT_ID: tenant.project_id
  HPL_BIGQUERY_INGESTER_DATASET: '"ds_" + tenant.lcaas_id'
  HPL_BIGQUERY_INGESTER_NUM_PIPELINE_ROUTINES: "'32'"
  HPL_BIGQUERY_INGESTER_TABLE: "'edr_data'"
  HPL_BIGQUERY_INGESTOR_ERROR_TOPIC: '"bq-errors-" + tenant.lcaas_id'
  HPL_COLD_STORAGE_DATASET_AGGREGATOR_PUB_SUB_TOPIC: 'license.enable_cold_retention ? "cold_storage_datasets_aggregator-" + tenant.lcaas_id : ""'
  HPL_COLD_STORAGE_GCS_TARGET_BUCKET: 'tenant.project_id + "-cold-storage-quantums-raw"'
  HPL_COLD_STORAGE_AGGREGATOR_RAW_DATA_BUCKET: 'tenant.project_id + "-cold-storage-quantums-raw"'
  HPL_DEDUP_REDIS_CONNECTION_STRING: 'globals.gonzo_redis ? globals.env.DRAGONFLY_URI : globals.env.REDIS_MAIN_HOST + ":6379"'
  HPL_DEDUP_REDIS_USE_DEFAULT: 'infra_ff.enable_redis_split || infra_ff.enable_gonzo_dragonfly ? "False" : "True"'
  HPL_DMS_AGED_STITCHED_TIME: "'24h'"
  HPL_DMS_NETWORK_RAW_TIME_RANGE: "'24h'"
  HPL_DMS_SUBSCRIPTION: '"stitched-" + tenant.lcaas_id'
  HPL_EDR_SOURCE_MAX_OUTSTANDING_MESSAGES: "'40'"
  HPL_EDR_SOURCE_PROJECT_ID: tenant.project_id
  HPL_EDR_SOURCE_SUBSCRIPTION: '"edr-raw-pipeline-" + tenant.lcaas_id + "-sub"'
  HPL_ENABLE_DML_INGESTER: "'True'"
  HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_HOSTS: globals.scylla_endpoint
  HPL_KEY_VALUE_STORE_SCYLLA_CLIENT_USER: globals.scylla_user
  HPL_GCP_PROJECT_ID: tenant.project_id
  HPL_GONZO_REDIS_CONNECTION_STRING: 'globals.gonzo_redis ? globals.env.DRAGONFLY_URI : globals.env.REDIS_MAIN_HOST + ":6379"'
  HPL_GONZO_REDIS_USE_DEFAULT: 'infra_ff.enable_redis_split || infra_ff.enable_gonzo_dragonfly ? "False" : "True"'
  HPL_LEGACY_EDR_INGESTER_TOPIC: '"edr-" + tenant.lcaas_id'
  HPL_MYSQL_DML_ENDPOINT: globals.env.MYSQL_MAIN_URI
  HPL_MYSQL_DDL_ENDPOINT: "''"
  HPL_MYSQL_DB_NAME: 'tenant.lcaas_id + "_main"'
  HPL_PANIC_RECOVERY_ENABLE_RECOVERY: "'True'"
  HPL_PIPELINE_ERRORS_BUCKET: 'region.project_prefix + "-" + tenant.lcaas_id + "-pipeline-errors"'
  HPL_PZ_SCRIPT_RESULTS_SUBSCRIPTION: '"dml-script-results-sub-" + tenant.lcaas_id'
  HPL_PZ_PUBSUB_SUBSCRIPTION: "''"
  HPL_PZ_LCAAS_SUBSCRIPTION: '"lavawall-" + tenant.lcaas_id'
  HPL_PZ_WEF_SUBSCRIPTION: "'dml_broker_wef'"
  HPL_REDIS_CONNECTION_STRING: 'globals.env.REDIS_MAIN_HOST + ":6379"'
  HPL_STORY_BUILDER_PUBLISH_TOPIC: '"stitched-" + tenant.lcaas_id'
  HPL_STITCHER_PUBLISHER_TOPIC: '""'
  HPL_STITCHER_QUERY_MAGNIFIER_DELAY: "'240s'"
  HPL_STITCHER_QUERY_XDR_DELAY: "'240s'"
  HPL_STITCHER_STITCHING_OLDEST_TIME: "'24h'"
  HPL_XQL_BIGQUERY_ERROR_TOPIC: '"xql-ingester-errors-" + tenant.lcaas_id'
  HPL_XQL_BIGQUERY_EXTERNAL_LOGS_DATASET: '"external_data_" + tenant.lcaas_id'
  HPL_XQL_EXT_DL_SUBSCRIPTION: '"xql-dl-sub-" + tenant.lcaas_id'
  HPL_XQL_EXT_SUBSCRIPTION: '"xql-ext-logs-" + tenant.lcaas_id + "-sub"'
  HPL_XQL_PZ_SCHEMA_MANAGER_HOST: 'globals.st_resource_prefix + "-pz-schema-manager:4981"'
  HPL_XQL_ENABLE_METABLOB_INGESTER: 'license.xsiam_gb_licenses > 0 ? "True" : "False"'
  HPL_XQL_MB_INGESTER_TRANSPORT_ANALYTICS_BUCKET: 'globals.is_xsiam ? tenant.project_id + "-external-data" : nil'
  HPL_XQL_MB_INGESTER_TRANSPORT_ANALYTICS_TOPIC: 'globals.is_xsiam && !globals.product_code_agentix ? "external-" + tenant.lcaas_id : nil'
  HPL_MAXMIND_ASN_BUCKET: '"maxmind-" + region.viso_env'
  HPL_MAXMIND_CITY_BUCKET: '"maxmind-" + region.viso_env'
  HPL_MAXMIND_CITY_OBJECT: "'maxmind/GeoLite2-City.mmdb'"
  HPL_MAXMIND_ASN_OBJECT: "'maxmind/GeoLite2-ASN.mmdb'"
  HPL_MAXMIND_BUCKET: '"maxmind-" + region.viso_env'
  HPL_MAXMIND_TAR: "'maxmind/maxmind_db.tar'"
  HPL_MAXMIND_ASN_FILE_NAME: "'GeoLite2-ASN.mmdb'"
  HPL_MAXMIND_CITY_FILE_NAME: "'GeoLite2-City.mmdb'"
  http_proxy: '"http://" + globals.tenant_proxy + ":13128"'
  https_proxy: '"http://" + globals.tenant_proxy + ":13128"'
  HYDRACONF_KEY_LOCATION: local._encrypt_fas_keyring_location
  HYDRACONF_KEY_RING: "'hydra-tenant-ring'"
  HYDRACONF_KEY_VERSION: 1
  HYDRACONF_KMS_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  HYDRACONF_MT_PUBSUB_PROJECT_ID: '"xdr-agent-gateway-" + region.viso_env + "-01"'
  HYDRACONF_MT_PUBSUB_TOPIC: "'hydra-incoming-topic'"
  INGESTER_CLOUD_STORAGE_SOURCE_BUCKET: 'region.project_prefix + "_" + tenant.lcaas_id + "_cs_edr_batch_ingestion"'
  INGESTER_INTERVAL_SEC: "180"
  INGESTER_PUBSUB_ERRORS_TOPIC: '"ingestion-errors-" + tenant.lcaas_id'
  LOOKUPSCONF_BACKUP_LOOKUP: 'region.viso_env == "dev" ? "False" : "True"'
  RETENTIONCONF_XSOAR_RETENTION_DEFAULT_MONTHS: "tenant.is_xsoar_onprem_migration && !tenant.xsoar_mssp_child && !tenant.xsoar_mssp_master && !tenant.xsoar_mssp_dev ? 12 : nil"
  EMAILALERTS_EMAIL_ALERTS_TOPIC_NAME: '"email-security-alerts-" + tenant.lcaas_id'
  EMAILACTIONS_EMAIL_POLICY_ACTIONS_TOPIC_NAME: '"email-security-policy-actions-" + tenant.lcaas_id'

  # HA
  HIGHAVAILABILITY_HIGH_AVAILABILITY_PUBSUB_TOPIC: 'infra_ff.is_enable_playbook_mirroring ? "high-availability-tenants-sync" : nil'
  HIGHAVAILABILITY_HIGH_AVAILABILITY_PUBSUB_SUBSCRIPTION: 'infra_ff.enable_primary_playbook_mirroring ? "high-availability-tenants-sync-sub" : nil'
  HIGHAVAILABILITY_SECONDARY_PROJECT_ID: "infra_ff.is_enable_playbook_mirroring ? tenant.secondary_project_id : nil"
  HIGHAVAILABILITY_PRIMARY_PROJECT_ID: "infra_ff.is_enable_playbook_mirroring ? tenant.primary_project_id :  nil"
  HIGHAVAILABILITY_IS_HIGH_AVAILABILITY_TENANT: 'infra_ff.is_enable_playbook_mirroring ? "True" :  nil'

  # Email data CRTX-112920
  EMAILARTIFACTSRELAY_EMAIL_ARTIFACTS_RELAY_PS_SUBSCRIPTION: "'email-data'"
  EMAILARTIFACTSRELAY_EMAIL_HASH_PROCESSOR_PS_SUBSCRIPTION: "'email-data-hash-processor'"
  EMAILARTIFACTSRELAY_SUBMIT_HASH_PS_TOPIC: '"email-data-attachments-" + tenant.lcaas_id'
  EMAILARTIFACTSRELAY_POLL_VERDICT_PS_TOPIC: '"email-data-submitted-hashes-" + tenant.lcaas_id'
  # DLQ Topic XDR-34598-Merge-fix
  HPL_DEDUP_DEAD_LETTER_QUEUE_ENABLED: "'False'"
  HPL_DEDUP_DEAD_LETTER_QUEUE_TOPIC_PREFIX: "'dlq'"
  HPL_EXT_NAMESPACE: "'ext'"
  HPL_DMS_NAMESPACE: "'dms'"
  HPL_PZ_NAMESPACE: "'pz'"
  HPL_XQL_NAMESPACE: "'xql'"
  IDENTITYSECURITYPOLICYSETTINGSCONF_AGENT_ITDR_INTEGRATION_BUCKET_NAME: 'tenant.project_id + "-agent-itdr-integrations"'
  IDENTITYSECURITYPOLICYSETTINGSCONF_AGENT_ITDR_INTEGRATION_PREPROCCESSED_DATA: 'tenant.project_id + "-preprocessed-data"'
  INGESTER_USE_STREAM: "'False'"
  INGESTER_VALIDATE_LCAAS_ID_ON_INCOMING_STREAM: "'True'"
  KMSCONF_BROKER_VM_KEY_LOCATION: local._encrypt_fas_keyring_location
  KMSCONF_BROKER_VM_KEY_NAME: tenant.lcaas_id
  KMSCONF_BROKER_VM_KEY_RING: "'broker-vm'"
  KMSCONF_BROKER_VM_KEY_VERSION: 1
  KMSCONF_BROKER_VM_KMS_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  KMSCONF_MT_KEY_LOCATION: globals.kms_keyring_region
  KMSCONF_MT_KEY_NAME: "'master'"
  KMSCONF_MT_KEY_RING: "'exec-hsm'"
  KMSCONF_MT_PROJECT_ID: '"xdr-distributions-" + region.multi_project_postfix + "-01"'
  KMSCONF_ST_KEY_LOCATION: globals.kms_keyring_region
  KMSCONF_ST_KEY_NAME: "'slave'"
  KMSCONF_ST_KEY_RING: "'exec-hsm'"
  KMSCONF_ST_KEY_VERSION: "'1'"
  KMSCONF_TSF_ENC_DECRYPT_KEY_NAME: "'enc-dec'"
  KMSCONF_TSF_ENC_KEY_LOCATION: region.gcp_region
  KMSCONF_TSF_ENC_KEY_RING: "'tsf-encrypt-decrypt'"
  KMSCONF_TSF_ENC_KEY_VERSION: "'1'"
  LICENSEMGR_LICENSE_REGION: 'infra_ff.enable_assured_workloads ? "de-rg" : region.license_region'
  LICENSINGCONF_APOLLO2_QUOTA_COLLECTION_NAME: 'region.viso_env == "dev" ? "quotaConfigs-qa-uat" : "quotaConfigs-prod"'
  LICENSINGCONF_APOLLO2_QUOTA_PROJECT_NAME: 'region.viso_env == "dev" ? "pan-qa-logging-svc-portal" : "pan-gov-logging-svc-portal"'
  LICENSINGCONF_DISABLE_BACKEND_LICENSE_CHECK: "'False'"
  LICENSINGCONF_DISABLE_FRONTEND_LICENSE: "'False'"
  LOGCONF_LOG_BQ_PROJECT_ID: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  LOGCONF_LOG_CONSOLE_MIN_SEVERITY: 'region.viso_env == "dev" && !tenant.is_metro_tenant ? "DEBUG" : "INFO"'
  LOGCONF_LOG_FILE_ENABLED: "'False'"
  LOGCONF_LOG_SLACK_CHANNEL_NAME_BACKEND: 'region.viso_env == "dev" ? "cortexdr-dev-errors" : "cortexdr-prod-errors"'
  LOGCONF_LOG_SLACK_CHANNEL_NAME_FRONTEND: 'region.viso_env == "dev" ? "xdr-app-errors-devfe" : "xdr-app-errors-prodfe"'
  LOGCONF_FE_LOG_SLACK_ENABLED: 'region.viso_env == "dev" ? "False" : "True"'
  LOGGINGSERVICE_LCAAS_TENANT: tenant.lcaas_id
  LOGGINGSERVICE_LCAAS_TOPIC_NAME: 'tenant.lcaas_id + "_lcaas"'
  LOGPROCESSORCONF_LOG_PROCESSOR_TOPIC_NAME: '"log-processor-" + tenant.lcaas_id'
  LOGPROCESSORCONF_PUBSUB_ERRORS_TOPIC_NAME: '"log-processor-errors-" + tenant.lcaas_id'
  LOGPROCESSORCONF_DML_SCRIPT_RESULTS_TOPIC: '"dml-script-results-" + tenant.lcaas_id'
  LOGPROCESSORCONF_REPORTED_ISSUES_SUBSCRIPTION: 'infra_ff.enable_cortex_platform ? "agent-management-reported-issues-sub-" + tenant.lcaas_id : nil'
  LOGPROCESSORCONF_CWP_LOG_PROCESSOR_TOPIC_NAME: '"agent-management-reported-issues-" + tenant.lcaas_id'
  FORENSICSPROCESSORCONF_ENABLED: 'globals.agent_mgmt.enable_forensics_processor ? "True" : "False"'
  FORENSICSPROCESSORCONF_TOPIC_NAME: '"forensics-processor-" + tenant.lcaas_id'
  KafkaService_metablob_max_size: "'9500000'"
  LOOKUPSCONF_BQ_DATASET: '"lookups_" + tenant.lcaas_id'
  LOOKUPSCONF_BUCKET_NAME: 'tenant.project_id + "-lookups"'
  LOOKUPSCONF_LOOKUPS_CLONE_DATASET: '"lookups_clones_" + tenant.lcaas_id'
  MODIFICATIONSUBSCRIBERCONF_MODIFICATION_TOPIC_NAME: '"modification-topic-" + tenant.lcaas_id'
  MAILINGCONF_SMTP_SERVER_ADDR: "'************'"
  MAILINGCONF_SMTP_SERVER_PORT: 'region.is_fedramp ? "11129" : "11127"'
  MAILINGCONF_USERNAME: "'apikey'"
  MAILINGCONF_USE_SSL: 'region.is_fedramp ? "True" : "False"'
  ManagementAuditConf_pubsub_subscription: "'mgmt-audit-notifier'"
  MSSPCONF_PERMISSIONS_API_URL: 'region.is_dev ? "https://app-compatibility-qa-uat.qa.appsvc.paloaltonetworks.com/xdr/api/v1/combined-service-mappings" : "https://app-compatibility.appsvc.paloaltonetworks.com/xdr/api/v1/combined-service-mappings"'
  MSSPCONF_PERMISSIONS_BY_CSP_URL: 'region.is_dev ? "https://app-compatibility-qa-uat.qa.appsvc.paloaltonetworks.com/xdr/api/v1/service-mappings" : "https://app-compatibility.appsvc.paloaltonetworks.com/xdr/api/v1/service-mappings"'
  MSSPCONF_IS_MSSP_CHILD_SHARED_LICENSE: tenant.is_mssp_child_xdr_xsiam
  MTHCONF_MTH_BCC_MAIL_ADDRESS: 'region.viso_env == "dev" ? "" : "<EMAIL>"'
  MYSQLCONF_BACKUP_BUCKET_NAME: 'tenant.project_id + "-mysql-backup"'
  MYSQLCONF_BACKUP_METRICS_JOB_INTERVAL_MINUTES: "'60'"
  MYSQLCONF_BACKUP_SNAPSHOT_METRICS_ENABLED: 'region.viso_env == "dev" ? "False" : "True"'
  MYSQLCONF_BACKUP_SNAPSHOT_METRICS_VALID_DAILY_RETENTION_IN_MINUTES: "'1440'"
  MYSQLCONF_HOST: globals.env.MYSQL_MAIN_URI
  MysqlConf_backup_snapshot_metrics_job_interval_minutes: "'20'"
  no_proxy: join(local._no_proxy_domains, ",")
  GCPPUBSUB_PLAYBOOK_EXEC_TOPIC_NAME: '"playbook-execution-" + tenant.lcaas_id'
  PARTYZAURUSCONF_XQLE_REDIS_ADDRESS: "globals.gonzo_redis ? globals.env.DRAGONFLY_URI : nil"
  PROMETHEUS_MULTIPROC_DIR: 'string(".")'
  PLATFORMCOMPLIANCEMANAGEMENTCONF_STORAGE_PROJECT_ID: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'
  PLATFORMCOMPLIANCEMANAGEMENTCONF_STORAGE_BUCKET_NAME: 'region.is_dev ? "platform-compliance-content-dev" : region.viso_env == "prod-fr" ? "platform-compliance-content-prod-fr" : region.viso_env == "prod-gv" ? "platform-compliance-content-prod-gv" : "platform-compliance-content"'
  PUBLICAPI_PAPI_GCS_BUCKET_NAME: 'region.project_prefix + "-" + tenant.lcaas_id + "-papi"'
  PVMMIGRATIONCONF_API_PATH: "'spog/jango/api/v1/migration/'"
  RBACCONF_BASE_URI: 'globals.is_uat ? "https://rbac-qa-uat.qa.appsvc.paloaltonetworks.com/auth/rbac" : region.is_dev ? "https://rbac-qa.qa.appsvc.paloaltonetworks.com/auth/rbac" : "https://rbac.appsvc.paloaltonetworks.com/auth/rbac"'
  REDISCONF_ADDR: globals.env.REDIS_MAIN_HOST
  REDISCONF_BACKUP_INTERVAL: "'86400'"
  REDISCONF_BACKUP_SNAPSHOT_METRICS_ENABLED: 'region.viso_env == "dev" ? "False" : "True"'
  REDISCONF_E_VERSION_UPGRADE_DO_REDIS_MIGRATION: "'True'"
  REDISCONF_REDIS_BACKUP_BUCKET_NAME: 'tenant.project_id + "-redis-backup"'
  RBACCONF_REDIS_TTL_USERS_FOR_XSIAM: "'600'"
  REMOTETERMINAL_PAYLOAD_BUCKET_NAME: '"panw-xdr-payloads-" + region.multi_project_postfix'
  REMOTETERMINAL_PAYLOAD_SIGNED_URL_EXPIRATION: "'600'"
  REMOTETERMINAL_RTS_ADDR: local._lrc_addr
  REMOTETERMINAL_RTS_BUCKET_NAME: 'region.project_prefix + "-rts-{}"'
  REMOTETERMINAL_RTS_WS_SERVER_ADDRESS: '"https://" + local._lrc_addr'
  REMOTETERMINAL_RTS_WS_SERVER_PORT: "'443'"
  REPORTSCONF_FF_CHROME_APP: "'True'"
  REPORTSCONF_REPORTS_BUCKET_NAME: 'tenant.project_id + "-reports"'
  REPORTSCONF_REPORTS_CHROME_URL: '"http://" + globals.st_resource_prefix + "-reports-chrome:9222"'
  REPORTSCONF_REPORTS_FRONTEND_URL: '"http://" + globals.st_resource_prefix + "-frontend"'
  REPORTSCONF_FRONTEND_URL: local._app_frontend_url
  REPORTSCONF_DASHBOARD_DISABLE_BG_ANIMATION: 'license.is_xpanse ? "True" : nil'
  RETENTIONCONF_BACKUP_DATASET: "'edr_retention_ds'"
  RETENTIONCONF_EVENT_FORWARDING_USER_SERVICE_ACCOUNT: 'infra_ff.is_enable_egress ? "event-forwarding-viewer@" + tenant.project_id + ".iam.gserviceaccount.com" : ""'
  RETENTIONCONF_MANAGEMENT_AUDIT_RETENTION_DAYS: "license.is_xsoar ? 3650 : 365"
  REPORTSCONF_USE_CHROME_JOB: '"True"'
  REPORTSCONF_KUBE_NAMESPACE: 'tenant.is_metro_tenant ? globals.st_resource_prefix : "xdr-st"'
  REPORTSCONF_CRONJOB_NAME: 'globals.st_resource_prefix + "-chrome-app-cron-job"'
  RetentionConf_cold_bucket: 'tenant.project_id + "-cold-storage-aggregated"'
  RetentionConf_cold_raw_bucket: 'tenant.project_id + "-cold-storage-raw"'
  RetentionConf_egress_bucket: 'tenant.project_id + "-event-forwarding"'
  RetentionConf_egress_subscription_name: 'infra_ff.is_enable_egress ? "event-forwarding-" + tenant.lcaas_id + "-sub" : nil'
  RetentionConf_enforce_retention: "'False'"
  RULESCONF_GLOBAL_EXPORT_FILE_NAME: "'xdrv2_global.zip'"
  RULESCONF_global_storage_bucket_name: globals.global_bioc_bucket
  RULESCONF_global_storage_project_id: globals.global_bioc_project
  MitreTagsConf_global_storage_bucket_name: globals.global_bioc_bucket
  RulesConf_asymmetric_signing_key_project_id: 'region.is_fedramp ? "xdr-kms-project-" + region.multi_project_postfix + "-01" : "xdr-kms-project-prod-us-01"'
  RulesConf_global_rules_sync_cronjob_namespace: 'tenant.is_metro_tenant ? "xdr-st-" + tenant.lcaas_id : nil'
  ScanningConf_evr_bucket_name: '"panw-xdr-evr-" + region.viso_env'
  SCORTEXCONF_CATBOOST_MODEL_IP: local._tenant_endpoint_scortex
  SCRIPTSCONF_GLOBAL_SCRIPT_BUCKET_NAME: 'region.viso_env == "prod-fr" ? "global-script-bucket-fr" : region.viso_env == "prod-gv" ? "global-script-bucket-gv" : region.viso_env == "dev" ? "global-script-bucket-dev" : "global-script-bucket"'
  SCRIPTSCONF_GLOBAL_SCRIPT_PROJECT_NAME: '"xdr-restricted-gcs-" + region.multi_project_postfix + "-01"'
  SCRIPTSCONF_SCRIPTS_DEV_PUBLIC_KEY_PATH: "'/etc/cert/script_public.key'"
  SCRIPTSCONF_SCRIPTS_PROD_PUBLIC_KEY_PATH: "'/etc/cert/script_public_prod.key'"
  SCRIPTSCONF_SCRIPTS_PUBLIC_KEY_MODE: 'region.viso_env == "dev" ? "dev" : "prod"'
  SCYLLA_NODE_COUNT: globals.scylla_nodes_count
  SCYLLACONF_KEYSPACE: 'tenant.is_legacy_metro ? "dml_" + tenant.lcaas_id : nil'
  SCYLLACONF_INIT_ON_CORTEX_INIT: 'tenant.is_legacy_metro ? "false" : nil'
  SCYLLACONFXCLOUD_INIT_ON_CORTEX_INIT: 'tenant.is_legacy_metro ? "false" : nil'
  SCYLLACONFENRICHMENT_INIT_ON_CORTEX_INIT: 'tenant.is_legacy_metro ? "false" : nil'
  SCYLLACONF_USER: globals.scylla_user
  SCYLLACONFXCLOUD_USER: globals.scylla_xcloud_username
  SCYLLACONFXCLOUD_SCYLLA_ENDPOINT: globals.scylla_xcloud_endpoint
  SCYLLACONFXCLOUD_KEYSPACE: 'tenant.is_legacy_metro ? "xcloud_" + tenant.lcaas_id : nil'
  SCYLLACONFENRICHMENT_USER: globals.scylla_enrichment_username
  SCYLLACONFENRICHMENT_SCYLLA_ENDPOINT: globals.scylla_enrichment_endpoint
  SCYLLACONFENRICHMENT_KEYSPACE: 'tenant.is_legacy_metro ? "enrichment_" + tenant.lcaas_id : nil'
  SLACKCONF_HYDRA_REDIRECT: 'get(local._slackconf_hydra_redirect_map, region.viso_env) ?? local._slackconf_hydra_redirect_map["default"]'
  SLACKCONF_NOTIFICATION_SUBSCRIPTION: "'slack-notification-sub'"
  SLACKCONF_NOTIFICATION_TOPIC_NAME: '"slack-notification-" + tenant.lcaas_id'
  XSOARCONF_BACKUP_SNAPSHOT_METRICS_ENABLED: 'region.viso_env == "dev" ? "False" : "True"'
  XSOARCONF_XSOAR_NG_ENABLED: infra_ff.enable_xsoar_shared_components
  XSOARCONF_IS_MSSP_CHILD: tenant.xsoar_mssp_child
  XSOARCONF_FRONTEND_HOST: "'***********:80'"
  XSOARCONF_HOST: 'local._xsoar_conf_xsoar_host + ":5566"'
  XSOARCONF_HOST_XSOAR_API: "'10.181.' + string(tenant.metro_tenant_index) + '.81:5566'"
  XSOARCONF_USE_XSOAR_API_PODS: 'infra_ff.is_enable_xsoar_api ? "True" : "False"'
  XSOARCONF_AUTH_KEY_HEADER: "'X-XDR-TOKEN'"
  xsoarconf_alerts_artifacts_bucket_name: 'tenant.project_id + "-xsoar-alerts-artifacts"'
  xsoarconf_marketplace_bucket_name: 'infra_ff.enable_cortex_platform ? "marketplace-cortex-content-" + region.multi_project_postfix + "/april-content" : "marketplace-" + lower(license.product_type) + "-" + region.multi_project_postfix'
  SPOGCONF_STORAGE_BUCKET_NAME: 'tenant.project_id + "-shared"'
  STREAMMATCHINGSERVICECONF_pubsub_edr_subscription_name: '"edr-matching-service"'
  STREAMMATCHINGSERVICECONF_PUBSUB_EDR_TOPIC: '"edr-matching-service"'
  STREAMMATCHINGSERVICECONF_pubsub_lcaas_subscription_name: '"lcaas-matching-service"'
  STREAMMATCHINGSERVICECONF_PUBSUB_LCAAS_TOPIC: '"lcaas-matching-service"'
  SYSLOGDISPATCHERCONF_FIRESTORE_PROJECT: '"xdr-log-forwarding-" + region.viso_env + "-01"'
  SYSLOGDISPATCHERCONF_NOTIFICATION_SUBSCRIPTION: '"log-forwarding-" + tenant.lcaas_id + "-sub"'
  SYSLOGDISPATCHERCONF_SYSLOG_DISPATCHER_PROJECT_ID: '"xdr-log-forwarding-" + region.viso_env + "-01"'
  SYSLOGDISPATCHERCONF_SYSLOG_DISPATCHER_TOPIC_NAME: '"lf-data-" + tenant.lcaas_id'
  SYSLOGDISPATCHERCONF_SYSLOG_NOTIFICATION_TOPIC: '"syslog-notification-" + tenant.lcaas_id'
  SYSLOGDISPATCHERCONF_SYSLOG_TEST_TOPIC_NAME: 'tenant.is_perf_tenant ? "syslog-connection-test-perf-topic" : "syslog-connection-test-topic"'
  SYSLOGDISPATCHERCONF_SYSLOG_UPDATE_TOPIC_NAME: 'tenant.is_perf_tenant ? "lf-syslog-update-perf-topic" : "lf-syslog-update-topic"'
  TASKPROCESSOR_TASK_PROCESSOR_PS_SUBSCRIPTION: '"task-processor-" + tenant.lcaas_id + "-sub"'
  TASKPROCESSOR_TASK_PROCESSOR_PS_TOPIC: '"task-processor-" + tenant.lcaas_id'
  TASK_SCHEDULER_MYSQL_HOST: globals.env.MYSQL_MAIN_URI
  TechSupportFileRetrievalEng_pubsub_subscription: 'region.is_fedramp ? "" : "tech-support-file-retrieval-sub"'
  TechSupportFileRetrievalEng_notifications_global_project_id: 'region.is_fedramp ? "" : "xdr-shared-services-prod-eu-01"'
  # There is only one project in eu REGION for shared services
  TechSupportFileRetrievalEng_notifications_global_topic_name: 'region.viso_env == "dev" ? "tech-support-file-global-dev" : region.is_fedramp ? "" : "tech-support-file-global-prod"'
  TIMINDICATORSCONF_FRONTEND_URL: local._app_frontend_url
  TIMINDICATORSCONF_TIM_INDICATORS_BUCKET: 'tenant.project_id + "-tim-indicators"'
  TIMINDICATORSCONF_ALERTS_TO_XSOAR_TOPIC: '"alerts-to-xsoar-" + tenant.lcaas_id'
  TIMINDICATORSCONF_ALERTS_TO_XSOAR_SUBSCRIPTION: '"alerts-to-xsoar-sub-" + tenant.lcaas_id'
  TIMINDICATORSCONF_ARTIFACT_EXTRACTION_TOPIC: '"artifact-extraction-" + tenant.lcaas_id'
  TIMINDICATORSCONF_ARTIFACT_EXTRACTION_SUBSCRIPTION: '"artifact-extraction-sub-" + tenant.lcaas_id'
  TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_SUBSCRIPTION: "'xsoar-artifacts-extraction-incident-notifications-sub'"
  TIMINDICATORSCONF_ARTIFACT_ALERT_RELATION_TOPIC: "'xsoar-artifacts-extraction-incident-notifications'"
  THIRDPARTYSAAS_GW_URL: 'infra_ff.enable_cortex_platform ? "http://" + globals.tenant_proxy + ":18443/saas_proxy_platform" : "http://" + globals.tenant_proxy + ":18443/saas_proxy"'
  THIRDPARTYSAAS_PUBSUB_COLLECTION_RESPONSE_TOPIC: '"collection-responses-" + tenant.lcaas_id'
  THIRDPARTYSAAS_RESPONSES_SUBSCRIPTION_NAME: '"collection-responses-" + tenant.lcaas_id + "-sub"'
  THIRDPARTYSAAS_SAAS_PROXY_URL: 'infra_ff.enable_cortex_platform ? "http://" + globals.tenant_proxy + ":18443/saas_proxy_platform" : "http://" + globals.tenant_proxy + ":18443/saas_proxy"'
  THIRDPARTYSAAS_ONBOARDING_NOTIFICATION_SUB: '"cloud-accounts-log-collection-" + tenant.lcaas_id + "-sub"'
  THIRDPARTYSAAS_SERVICE_ACCOUNT: '"collector-" + tenant.lcaas_id + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
  TUSCONF_UPLOADS_BUCKET: 'tenant.project_id + "-agent-uploads"'
  TENANT_ID: "tenant.lcaas_id"
  UIAnalyticsConf_analytic_stats_project: '"xdr-ui-analytics-" + region.multi_project_postfix + "-01"'
  UIAnalyticsConf_analytic_stats_dataset: 'license.is_xpanse && region.viso_env  != "dev" ? "ui_analytic_xpanse" : "ui_analytic_mt"'
  UIAnalyticsConf_analytic_stats_table: "'ui_analytic'"
  EGRESSPROXY_CA_PATH: "'/etc/cert/egress.crt'"
  EGRESSPROXY_URL: "globals.egress_proxy_address"
  VIEWSCONF_PROJECT: "'xdr-zeus'"
  # Boaz: Mainly for fedramp support in the future
  VIEWSCONF_BUCKET: "'global-content-anonymization-prod'"
  # Boaz: Mainly for fedramp support in the future
  VertexAiBigqueryConf_dataset_name: '!region.is_fedramp ? "llm_models" : nil'
  VertexAiBigqueryConf_model_name: '!region.is_fedramp ? "bq_llm_runtime" : nil'
  VertexAiBigqueryConf_project_name: '!region.is_fedramp ? "xdr-cortex-copilot-" + region.multi_project_postfix + "-01" : nil'
  VISOOPERATIONS_SUBSCRIPTION_NAME: '"viso-operations"'
  VISOOPERATIONS_BUCKET_NAME: 'tenant.project_id + "-viso-operations"'
  VISOOPERATIONS_RESULTS_BUCKET_FOLDER: '"results"'
  VULNERABILITYASSESSMENTCONF_NIST_BUCKET_NAME: 'region.is_fedramp ? "global-va-storage-" + region.viso_env : region.viso_env == "dev" ? "global-va-storage-dev" : "global-va-storage"'
  VULNERABILITYNETWORKSCANNINGCONF_NETSCAN_KMS_KEY_NAME: '"uvem_" + tenant.lcaas_id'
  VULNERABILITYNETWORKSCANNINGCONF_NETSCAN_KMS_KEY_RING: '"uvem_application_layer_ring"'
  VULNERABILITYNETWORKSCANNINGCONF_NETSCAN_KMS_LOCATION: "region.gcp_region"
  VULNERABILITYNETWORKSCANNINGCONF_NETSCAN_KMS_PROJECT_ID: "tenant.project_id"
  NETSCAN_BQ_STATS_PROJECT: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  VULNERABILITYASSESSMENTCONF_TENANT_REPORTS_BUCKET_NAME: 'region.viso_env == "dev" ? "global-va-reports-dev" : region.is_fedramp ? "global-va-reports-" + region.viso_env : "global-va-reports"'
  VULNERABILITYNETWORKSCANNINGCONF_RAW_DATA_BUCKET: 'region.viso_env == "dev" ? "network-scanner-vt-content-dev" : "network-scanner-vt-content-prod"'
  WEBAPPCONF_APP_SWITCHER_URL: 'globals.is_uat ? "https://apps-qa-uat.qa.appsvc.paloaltonetworks.com" : region.is_dev ? "https://apps-qa2.app-portal-qa.us.paloaltonetworks.com" : "https://apps.paloaltonetworks.com"'
  WEBAPPCONF_APPSWITCHER_JS: 'region.viso_env == "dev" ? "https://d1z5jbpmqvye3f.cloudfront.net/app-switcher/index.js" : "https://d2aqaxyce54jsd.cloudfront.net/app-switcher/index.js"'
  WEBAPPCONF_CERTIFICATE_PATH: "'/etc/tokens/'"
  WEBAPPCONF_DISABLE_FRONTEND_RBAC: "'False'"
  WEBAPPCONF_DYNAMIC_FEATURE_FLAGS_DIR: '"/etc/frontend-config"'
  WEBAPPCONF_ENABLE_CSRF: "'True'"
  WEBAPPCONF_LOGOUT_URL: '"https://" + globals.external_fqdn + (region.viso_env == "dev" ? "/logout?returnurl=https://apps-qa2.app-portal-qa.us.paloaltonetworks.com/" : "/logout?returnurl=https://apps.paloaltonetworks.com")'
  WEBAPPCONF_PIN_HOSTNAME: globals.external_fqdn
  WEBAPPCONF_RESTRICT_HOSTNAME: "'True'"
  WEBAPPCONF_USE_SUPPORT_INFO: "'True'"
  WEBAPPCONF_VALIDATE_JWT: "'True'"
  WEBAPPCONF_VALIDATE_RBAC: "'True'"
  WEBAPPCONF_VERIFY_JWT: "'True'"
  WILDFIRE_SERVER_HOSTNAME: 'infra_ff.enable_assured_workloads ? "eu-reg.wildfire.paloaltonetworks.com" : get(local._wildfire_url_map, region.viso_env) ?? local._wildfire_url_map["default"]'
  WildFire_low_priority_enabled: 'region.is_fedramp ? "False" : "True"'
  XCLOUDCONF_ENABLED: infra_ff.is_enable_xcloud
  XCloudAwsConf_xcloud_account_id: 'region.is_dev ? "************" : region.is_fedramp_moderate ? "************" : region.is_fedramp_high ? "**********" : "************"'
  XCloudAwsConf_xcloud_commercial_account_id: 'region.is_dev ? "************" : "************"'
  XCloudAwsConf_xcloud_fedramp_account_id: 'region.is_fedramp ? "************" : nil'
  XCloudAwsConf_xcloud_account_region: 'region.is_fedramp ? "us-gov-east-1" : "us-east-2"'
  XCloudAwsConf_xcloud_commercial_account_region: "'us-east-2'"
  XCloudAwsConf_xcloud_fedramp_account_region: "'us-gov-east-1'"
  XCloudAwsConf_default_region: 'region.is_fedramp ? "us-gov-east-1" : "us-east-2"'
  XCloudAwsConf_commercial_default_region: '"us-east-2"'
  XCloudAwsConf_fedramp_default_region: '"us-gov-east-1"'
  # todo: prod-gv
  XCloudGcpConf_default_full_scan_interval_sec: "license.is_xpanse ? 86400 : nil"
  XCLOUDCONF_AWS_MASTER_PATH: 'local._xcloud_onboarding_s3_bucket_url + "/cortex-xdr-aws-master-ro-1.0.0.template"'
  XCLOUDCONF_AWS_FEDRAMP_MASTER_PATH: 'local._xcloud_onboarding_s3_fedramp_bucket_url + "/cortex-xdr-aws-master-ro-1.0.0.template"'
  XCLOUDCONF_AWS_COMMERCIAL_MASTER_PATH: 'local._xcloud_onboarding_s3_commercial_bucket_url + "/cortex-xdr-aws-master-ro-1.0.0.template"'
  XCLOUDCONF_AWS_MEMBER_PATH: 'local._xcloud_onboarding_s3_bucket_url + "/cortex-xdr-aws-member-ro-1.0.0.template"'
  XCLOUDCONF_AWS_FEDRAMP_MEMBER_PATH: 'local._xcloud_onboarding_s3_fedramp_bucket_url + "/cortex-xdr-aws-member-ro-1.0.0.template"'
  XCLOUDCONF_AWS_COMMERCIAL_MEMBER_PATH: 'local._xcloud_onboarding_s3_commercial_bucket_url + "/cortex-xdr-aws-member-ro-1.0.0.template"'
  XCLOUDINVENTORYCONF_PB_TOPIC: 'infra_ff.is_enable_xcloud ? "inventory-" + tenant.lcaas_id : "XCLOUD_DISABLED"'
  XCLOUDINVENTORYCONF_PB_SUBSCRIPTION: 'infra_ff.is_enable_xcloud ? "inventory-" + tenant.lcaas_id : "XCLOUD_DISABLED"'
  XCLOUDINVENTORYCONF_PB_BUCKET_NAME: 'infra_ff.is_enable_xcloud ? tenant.project_id + "-inventory" : "XCLOUD_DISABLED"'
  XCLOUDREDISCONF_HOSTNAME: "globals.xcloud_redis_standalone_deployment ? globals.env.REDIS_XCLOUD_HOST : globals.env.REDIS_MAIN_HOST"
  XQLCSVEXPORTCONF_BUCKET_NAME: 'region.project_prefix + "-" + tenant.lcaas_id + "-csv-exports"'
  XQLENGINE_SERVICE_URL: '"http://" + globals.st_resource_prefix + "-xql-engine:7973"'
  XQLINGESTIONCONF_INGESTER_ERRORS_SUB: "'xql-ingester-errors-sub'"
  XqlSyncServiceConf_GLOBAL_CONTENT_BUCKET: '"global-xql-content-" + (region.viso_env == "dev" ? "qa" : region.is_fedramp ? region.viso_env : "prod")'
  XqlSyncServiceConf_per_tenant_content_bucket_format: '"global-xql-content-" + (region.viso_env == "dev" ? "qa" : region.is_fedramp ? region.viso_env : "prod")'
  XQLSYNCSERVICECONF_SIGNING_KEY_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  XQLSYNCSERVICECONF_SIGNING_KEY_LOCATION: globals.kms_keyring_region
  XQLSYNCSERVICECONF_SIGNING_KEY_RING_NAME: "'content_signing'"
  XQLSYNCSERVICECONF_SIGNING_KEY_NAME: "'content_signing_key'"
  XSOARMIGRATIONCONF_bq_migration_project: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  XSOARMIGRATIONCONF_MIGRATION_BUCKET: 'tenant.project_id + "-xsoar-migration"'
  XSOARMIGRATIONCONF_ARTIFACTS_BUCKET: 'tenant.project_id + "-xsoar-files"'
  XSOARMIGRATIONCONF_ONPREM_RETENTION_DEFAULT: "'12'"
  XSOARMIGRATIONCONF_IS_ON_PREM: tenant.is_xsoar_onprem_migration
  XSOARMIGRATIONCONF_SOURCE_TENANT_HOST: 'tenant.is_xsoar_onprem_migration ? "on_prem_migration" : tenant.xsoar_6_host'
  XSOARMIGRATIONCONF_TENANT_SERIAL: tenant.xsoar_6_sn
  XSOARMIGRATIONCONF_BUCKET_ACCESS_SA_NAME: "'xsoar-mig-pod'"
  XSOARMIGRATIONCONF_DEV_PROD_TYPE: 'tenant.xsoar_6_standalone ? "standalone" : tenant.xsoar_6_env'
  XSOARMIGRATIONCONF_BYOG_USE_MULTI_BRANCH: tenant.xsoar_6_byog_use_multi_branch
  XSOARMIGRATIONCONF_EMAILS_CC: tenant.xsoar_6_emails_cc
  XSOARMIGRATIONCONF_DEFAULT_CUTOFF_DATE: tenant.xsoar_6_cutoff_date
  XSOARMIGRATIONCONF_DEFAULT_CUTOFF_START_HOUR: tenant.xsoar_6_cutoff_start_hour
  XSOARMIGRATIONCONF_DEFAULT_CUTOFF_END_HOUR: tenant.xsoar_6_cutoff_end_hour
  XDRGATEWAYCONF_GATEWAY_URL: 'region.viso_env == "dev" ? "https://cortex-gateway-dev.paloaltonetworks.com" : region.viso_env == "prod-fr" ? "https://cortex-gateway-federal.paloaltonetworks.com" : region.viso_env == "prod-gv" ? "https://cortex-gateway-gov.paloaltonetworks.com" : "https://cortex-gateway.paloaltonetworks.com"'
  CASESYSTEMSCORINGCONF_SYSTEM_SCORING_BUCKET: 'region.is_fedramp ? "global-bioc-rules-" + region.viso_env : "global-bioc-rules-prod"'
  XDRBQSLOTPRIORITY_ANALYTICS_ON_DEMAND_PRIORITY_PROJECT_ID: 'region.is_fedramp ? "" : "xdr-data-research-slots-aod-01"'
  UnifiedAssetInventoryConf_singlestore_host: 'infra_ff.enable_cortex_platform ? "svc-dp-singlestore-cluster-ddl" : nil' # should be removed.. now it breaks old platform code
  UnifiedAssetInventoryConf_singlestore_host_ddl: 'infra_ff.enable_cortex_platform ? "svc-dp-singlestore-cluster-ddl" : nil'
  UnifiedAssetInventoryConf_singlestore_host_dml: 'infra_ff.enable_cortex_platform ? "svc-dp-singlestore-cluster-dml" : nil'
  UnifiedAssetInventoryConf_singlestore_port: 'infra_ff.enable_cortex_platform ? "3306" : nil'
  UnifiedAssetInventoryConf_singlestore_user: 'infra_ff.enable_cortex_platform ? "admin" : nil'
  UnifiedAssetInventoryConf_singlestore_db: 'infra_ff.enable_cortex_platform ? "main_" + tenant.lcaas_id : nil'
  UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_DEBUG_SUBSCRIPTION_NAME: "'vulnerability-and-compliance-scans-debug'"
  UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_SUBSCRIPTION_NAME: "'vulnerability-and-compliance-scans'"
  UNIFIEDAGENTCONF_VC_SCAN_PUBSUB_TOPIC: '"vulnerability-and-compliance-scans-" + tenant.lcaas_id'
  UNIFIEDAGENTCONF_VC_SCAN_UPLOAD_BUCKET: 'tenant.project_id + "-vulnerability-and-compliance-scans"'
  UNIFIEDINVENTORYCONF_ASSET_TAGS_ENABLED: 'license.is_xpanse ? "True" : nil'
  UNIFIEDINVENTORYCONF_IS_USING_CACHED_DATA: '((license.pro_agents_for_scaling > 10000) || (license.monthly_tb_licenses > 50) || license.enable_asm) ? "True" : "False"'
  UNIFIEDASSETINVENTORYCONF_ASSET_EXPORT_BUCKET: 'tenant.project_id + "-dp-asset-export"'
  UnifiedAssetInventoryConf_singlestore_partitions_per_leaf: '!infra_ff.is_enable_prod_spec ? "8" : "32"'
  ScortexConf_content_project_id: globals.global_bioc_project
  ScortexConf_content_bucket_name: globals.global_bioc_bucket
  ALERTSCONF_ALERTS_DATA_BUCKET_NAME: 'tenant.project_id + "-alerts-data"'
  XPANSECONF_API_KEY_ID: 'license.enable_asm ? "1000000" : nil'
  XpanseClientConf_api_url: 'license.enable_asm ? (region.viso_env == "dev" ? "https://expander-viso.dev.q-internal.tech" : "https://expander.expanse.co") : nil'
  XpanseClientConf_request_token_url: 'license.enable_asm ? (region.viso_env == "dev" ? "https://apitest.paloaltonetworks.com/api/oauth2/RequestToken" : "https://api.paloaltonetworks.com/api/oauth2/RequestToken") : nil'
  XpanseConf_enable_tags_replication: 'license.is_xpanse ? "True" : "False"'
  XpanseConf_enable_websites_replication: 'license.enable_asm ? "True" : "False"'
  XpanseConf_enable_replication: license.enable_asm
  XpanseConf_enable_xpanse_dashboard_filtering: 'license.is_xpanse ? "True" : "False"'
  XpanseAlertDatabaseSyncConf_is_enabled: 'license.is_xpanse ? "True" : "False"'
  XpanseIncidentDatabaseSyncConf_is_enabled: 'license.is_xpanse ? "True" : "False"'
  XpanseCaseAlertDataSyncConf_is_enabled: 'license.is_xpanse ? "True" : "False"'
  XpanseConf_in_fedramp_ev2_migration_period: "'False'"
  XpanseConf_xpanse_security_rating_dashboard_type: 'license.is_xpanse ? region.is_fedramp ? "no-benchmark-scores" : "has-benchmark-scores" : nil'
  UnifiedInventoryConf_use_id_to_tags_cache_table_for_services_and_websites: license.is_xpanse
  NotificationBanner_global_banner_bucket_name: 'region.viso_env == "dev" ? "xdr-banners-dev" : region.is_fedramp ? "xdr-banners-" + region.viso_env + "" : "xdr-banners-prod"'
  NotificationBanner_global_banner_project_id: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'
  XpanseThreatEventsConf_bq_sync_enabled: 'license.is_xpanse || globals.is_xsiam ? "True" : "False"'
  XpanseThreatEventsConf_global_storage_project_id: local._certs_project
  XpanseThreatEventsConf_global_storage_bucket_name: local._xpanse_multi_tenants_bucket_name
  XpanseComplianceFrameworksConf_global_storage_project_id: local._certs_project
  XpanseComplianceFrameworksConf_global_storage_bucket_name: local._xpanse_multi_tenants_bucket_name
  XpanseTechnologyMetadataConf_global_storage_project_id: local._certs_project
  XpanseTechnologyMetadataConf_global_storage_bucket_name: local._xpanse_multi_tenants_bucket_name
  XpansePolicyManagerConf_api_url: 'license.enable_asm ? (region.viso_env == "dev" ? "https://expander-viso.dev.q-internal.tech/api/v1" : "https://expander.expanse.co/api/v1") : nil'
  XpansePolicyManagerConf_request_token_url: 'license.enable_asm ? (region.viso_env == "dev" ? "https://apitest.paloaltonetworks.com/api/oauth2/RequestToken" : "https://api.paloaltonetworks.com/api/oauth2/RequestToken") : nil'
  #CRTX-105752
  VULNERABILITYTESTINGCONF_IS_ENABLED: "'False'"
  VULNERABILITYTESTINGCONF_IS_DISABLED: 'region.is_fedramp ? "True" : "False"'
  XPANSEVULNERABILITYTESTSCONF_GLOBAL_STORAGE_BUCKET_NAME: 'region.viso_env == "prod-fr" ? "xpanse-policies-prod-fr" : region.viso_env == "prod-gv" ? "xpanse-policies-prod-gv" : region.viso_env == "dev" ? "xpanse-policies-dev" : "xpanse-policies-prod"'
  XPANSEVULNERABILITYTESTSYNCCONFIG_MT_BUCKET_NAME: 'region.viso_env == "prod-gv" ? "vulnerability-tests-gv-prod" : region.viso_env == "prod-fr" ? "xpanse-vulnerability-test-results-unused" : region.viso_env == "dev" ? "vulnerability-tests-dev" : "vulnerability-tests-prod"'
  XPANSECONF_ENABLE_XPANSE_USER_DEFINED_IP_RANGES: 'license.is_xpanse ? "True" : "False"'
  REPORTSCONF_MAX_WIDGET_OVER_TIME_RETENTION_DAYS: "license.is_xpanse ? 90 : 30"
  ASMPublicApiServiceConf_internet_exposure_limit: 'license.is_xpanse ? "5000" : "500"'
  GCPConf_mth_queries_charged_project: 'region.viso_env == "dev" ? "" : region.is_fedramp ? "" : "xdr-mth-slots-prod-us-01"'
  GCPConf_mdr_queries_charged_project: 'region.viso_env == "dev" ? "" : region.is_fedramp ? "" : "xdr-mth-slots-prod-us-01"'
  DASHBOARDENGINECONF_OUTPUT_DATASET: '"dashboard_engine_" + tenant.lcaas_id'
  ALPHAFEATURES_USE_PENDO: 'region.is_fedramp ? "False" : "True"'
  # Metrics Aggregator
  HPL_GCP_DATASET: '"ds_" + tenant.lcaas_id'
  HPL_XQL_METRICS_REGISTRY_PUBSUB_TOPIC: '"metrics-aggregator-" + tenant.lcaas_id'
  #CRTX-80280 Xapsne feature flag
  APILOGSCONF_LOG_REQUEST_BODY: 'license.is_xpanse ? (region.viso_env == "dev" || region.viso_env == "prod-us" ? "True" : "False") : "False"'
  APILOGSCONF_LOG_USERNAME_WITHOUT_HASH: 'license.is_xpanse ? (region.viso_env == "dev" || region.viso_env == "prod-us" ? "True" : "False") : "False"'
  #CRTX-80728 api monitoring BQ
  APILOGSCONF_BQ_PROJECT: '"xdr-bq-mt-stats-" + region.viso_env + "-01"'
  APILOGSCONF_ENABLED: '(region.viso_env == "dev" || license.is_xpanse || globals.is_xsiam || license.is_xsoar) ? "True" : "False"'
  APPLICATIONHUB_BQ_PROJECT: '"xdr-app-hub-slots-" + region.viso_env + "-01"'
  APPLICATIONHUB_BQ_VIEWS_DATASET: '"public_access_views_" + tenant.lcaas_id'
  APPLICATIONHUB_BQ_USER_DATASET: '"public_access_user_" + tenant.lcaas_id'
  XpanseIncidentsConf_reopen_incidents_with_open_alerts_enabled: license.is_xpanse
  XpanseConf_enable_xpanse_ipv6: 'license.is_xpanse ? "True" : "False"'
  # CRTX-94914 set true for all xpanse tenants
  #CRTX-94371 move rcs configs from env vars to CM
  XpanseConf_enable_xpanse_remediation_confirmation_scanning: 'license.enable_asm && !region.is_fedramp ? "True" : "False"'
  XpanseRemediationScanningConf_mt_pubsub_project_id: 'region.viso_env == "dev" ? "xp-gcp-h-dev-s-dev" : region.viso_env == "prod-gv" ? "xpanse-aw-prod-svc-asm" : "xp-gcp-h-prod-s-prod"'
  XpanseRemediationScanningConf_topic_name: 'region.viso_env == "dev" ? "manual-scan-requests-dev" : region.viso_env == "prod-gv" ? "manual-scan-requests-gv-prod" : "manual-scan-requests-prod"'
  XpanseRcsResultProcessorConf_GCS_SCAN_RESULTS_BUCKET_NAME: 'license.enable_asm && region.viso_env != "prod-fr" ? tenant.project_id + "-xpanse-manual-scan-results": ""'
  XpanseRcsResultProcessorConf_PUBSUB_SCAN_RESULTS_SUBSCRIPTION_NAME: 'license.enable_asm && region.viso_env != "prod-fr" ? "xpanse-manual-scan-results" : ""'
  ExportConf_async_exports_gcs_bucket_name: 'tenant.project_id + "-async-export-files"'
  #CRTX-98703
  XPANSEINCIDENTSCONTEXTCONFIG_SYNC_ENABLED: 'license.is_xpanse ? "True" : "False"'
  INCIDENTSCONF_PARALLEL_INCIDENT_MANAGEMENT: 'license.is_xpanse ? "True" : "False"'
  # CRTX-96911 enable migration-banners buckets
  XSOARMIGRATIONCONF_GLOBAL_STORAGE_CUSTOM_NOTIFICATION_BUCKET_NAME: 'license.is_xsoar ? (region.viso_env == "dev" ? "xsoar-migration-banners-dev" : "xsoar-migration-banners-prod") : nil'
  AssetManagementConf_pubsub_assets_change_topic_name: '"ipl-asset-changes-" + tenant.lcaas_id'
  AssetManagementConf_pubsub_assets_association_subscription_name: '"ipl-asset-changes-" + tenant.lcaas_id + "-sub"'
  AssetManagementConf_asset_management_v2_bucket_name: 'tenant.project_id + "-asset-inventory"'
  AssetManagementConf_bq_asset_management_dataset: '"asset_inventory_" + tenant.lcaas_id'
  AssetManagementConf_bq_asset_management_inventory_dataset: '"asset_inventory_" + tenant.lcaas_id'
  AssetManagementConf_redis_host: globals.env.REDIS_IPL_ASSET_URI
  AssetManagementConf_redlock_host: globals.env.REDIS_IPL_ASSET_URI
  SUPPORTCASECONF_CASE_URL: 'region.is_commercial ? "https://sso.paloaltonetworks.com/app/panw-ciam_sfdcprodcasecommunities_1/exk3jjv9xQhNTsmhF0j6/sso/saml?RelayState=https%3A%2F%2Fsupportcases.paloaltonetworks.com/s/casedetail?Id={id}" : nil'
  RETENTIONCONF_EGRESS_FORWARDING_HTTP_PROXY: "globals.egress_proxy_address"
  RETENTIONCONF_EGRESS_FORWARDING_SUBSCRIPTION_NAME: 'infra_ff.is_enable_egress ? "event-forwarding-external-" + tenant.lcaas_id + "-sub" : nil'
  FIRESTOREACCESSSERVICE_EGRESS_FIRESTORE_PROJECT: 'region.viso_env == "dev" ? "xdr-egress-firestore-dev-01" : region.is_fedramp ? "xdr-egress-fas-" + region.viso_env + "-01" : "xdr-egress-fas-prod-us-01"'
  MITREMAPPINGSCONF_ENABLE_ASM_ALERT_MITRE_MAPPINGS: 'license.is_xpanse ? "True" : "False"'
  THIRDPARTYCOLLECTIONKMS_PROJECT_ID: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  THIRDPARTYCOLLECTIONKMS_LOCATION: local._encrypt_fas_keyring_location
  THIRDPARTYCOLLECTIONKMS_KEY_RING: "'collection-security'"
  THIRDPARTYCOLLECTIONKMS_KEY_NAME: tenant.lcaas_id
  XSOARCONF_IS_REDIS_XSOAR_ENABLED: 'globals.is_xsiam ? "true" : nil'
  XSOARCONF_REDIS_PORT: 'globals.is_xsiam ? "6379" : nil'
  XSOARCONF_REDIS_HOST: "globals.is_xsiam ? globals.env.REDIS_XSOAR_HOST : nil"
  #CRTX-124604 & CRTX-126837
  ISSUESFETCHERCONF_CREATION_TOPIC: '"ap-issue-create-" + tenant.lcaas_id'
  ISSUESINGESTERCONF_INGESTER_TOPIC: '"ap-issue-upsert-" + tenant.lcaas_id'
  ISSUESCONF_DEAD_LETTER_TOPIC: '"ap-issue-ingestion-errors-" + tenant.lcaas_id'
  ISSUESCONF_DEAD_LETTER_SUBSCRIPTION: '"ap-issue-ingestion-errors-" + tenant.lcaas_id'
  ISSUESINGESTERCONF_INGESTER_SUBSCRIPTION: '"ap-issue-upsert-" + tenant.lcaas_id'
  ISSUESFETCHERCONF_CREATION_SUBSCRIPTION: '"ap-issue-create-" + tenant.lcaas_id'
  ISSUEUPDATECONF_ISSUE_UPDATES_TOPIC_NAME: '"ap-issue-update-" + tenant.lcaas_id'
  ISSUEUPDATECONF_ISSUE_UPDATES_SUBSCRIPTION_NAME: '"ap-issue-update-sub-" + tenant.lcaas_id'
  #CRTX-131355
  CUSTOMERASMMANAGEMENTCONF_MT_BUCKET_NAME: 'region.viso_env == "dev" ? "asm-general-dev" : region.viso_env == "prod-gv" ? "xp-prod-gv-asm-general" : region.viso_env == "prod-fr" ? "xp-empty-no-perms-bucket" : "asm-general-prod"'
  #CRTX-132986
  VipCveConf_global_storage_bucket_name: 'region.viso_env == "dev" ? "xpanse-policies-dev" : region.viso_env == "prod-gv" ? "xpanse-policies-prod-gv" : region.viso_env == "prod-fr" ? "xpanse-policies-prod-fr" : "xpanse-policies-prod"'
  RISKSCORECIEPROCESSOR_RISK_SCORE_PS_TOPIC: '(license.is_xdr || globals.is_xsiam) && infra_ff.enable_pipeline ? "identity-risk-score-updates-" + tenant.lcaas_id : ""'
  RISKSCORECIEPROCESSOR_RISK_SCORE_PS_SUBSCRIPTION: '(license.is_xdr || globals.is_xsiam) && infra_ff.enable_pipeline ? "risk-score-cie-updates" : ""'
  XSOARCONF_UI_TERM_INCIDENT: 'infra_ff.enable_cortex_platform ? "6" : nil'
  THIRDPARTYSAAS_GSUITE_TASK_TOPIC: '"task-notification-google-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_LIFECYCLE_TOPIC: '"msft-lifecycle-notification-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_TASK_TOPIC: '"msft-task-notification-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_BACKUP_TOPIC: '"msft-task-backup-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_AUDIT_TOPIC: '"msft-audit-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_RETRY_TOPIC: '"msft-task-retry-" + tenant.lcaas_id'
  THIRDPARTYSAAS_MSFT_METADATA_TASKS_TOPIC: '"msft-metadata-tasks-" + tenant.lcaas_id'
  THIRDPARTYSAAS_OKTA_TASKS_TOPIC: '"okta-tasks-" + tenant.lcaas_id'
  UNIFIEDASSETINVENTORYCONF_NEO4J_URI: "infra_ff.enable_cortex_platform ? globals.env.NEO4J_SERVER_URI : nil"
  UNIFIEDASSETINVENTORYCONF_NEO4J_USER: 'infra_ff.enable_cortex_platform ? "neo4j" : nil'
  UNIFIEDASSETINVENTORYCONF_NEO4J_ENABLED: 'infra_ff.enable_cortex_platform ? "True" : nil'
  UNIFIEDASSETINVENTORYCONF_NEO4J_DATABASE: 'infra_ff.enable_cortex_platform ? "PlatformGraph" : nil'
  UNIFIEDASSETINVENTORYCONF_ASSET_OBSERVATION_TOPIC_NAME: '"dp-uai-asset-observations-" + tenant.lcaas_id'
  AnalyticsConf_pickle_vectorized_content_load_optimization: '"true"'
  PLATFORMMIGRATIONCONF_IS_MIGRATED_TENANT: 'tenant.is_migrated_cortex_platform ? "True" : nil'
  XSOARMIGRATIONCONF_BYOG_TARGET_BRANCH: "tenant.xsoar_6_byog_target_branch"
  IDENTITYSECURITYPOLICYSETTINGSCONF_WEAK_PASSWORDS_AGENT_REPORTS_TOPIC_NAME: '"agent-management-outgoing-external-integrations-" + tenant.lcaas_id'
  VIPVULNERABILITYCONF_MULTI_TENANT_NAMESPACE: 'region.is_fedramp ? nil : "green"'
  VIPVULNERABILITYCONF_VIP_PROJECT_ID: 'region.viso_env == "dev" ? "xdr-vulnerability-intel-dev-01" : region.viso_env == "prod-fr" ? "xdr-vuln-intel-prod-fr-01" : region.viso_env == "prod-gv" ? "xdr-vuln-intel-prod-gv-01" : "xdr-vuln-intel-prod-us-01"'
  AGENTMODULESINTEGRATIONSCONF_OUTGOING_EXTERNAL_INTEGRATIONS_TOPIC: '"agent-management-outgoing-external-integrations-" + tenant.lcaas_id'
  IDENTITYSECURITYPOLICYSETTINGSCONF_ITDR_API_URL: '"http://" + globals.st_resource_prefix + "-itdr-api-svc." + globals.st_namespace + ".svc.cluster.local:8080"'
  AGENTMANAGEMENT_URL_CRAFTING_TARGET_HOST: 'globals.st_resource_prefix + (infra_ff.is_enable_api_split ? "-api-agent-be" : "-api") + ":4999"'
  AGENTMANAGEMENTCONF_AGENT_MANAGEMENT_PROCESSOR_TOPIC: '"agent-management-incoming-external-integrations-" + tenant.lcaas_id'
  #CRTX-19528 - Send message to PubSub on asset groups operations
  UaiAssetGroupsConf_events_submit_topic_name: '"dp-uai-asset-groups-change-feed-" + tenant.lcaas_id'
  #CRTX-200468
  PlatformSbacConf_dataset_sbac_enabled: 'license.product_type == "XSIAM" ? "True" : "False"'
  UNIFIEDASSETINVENTORYCONF_REAL_TIME_DB: 'tenant.creation_date > 1763899200000 || (tenant.is_migrated_cortex_platform && infra_ff.enable_spanner) ? "spanner" : "singleStore"' # spanner migration FF
  XSOARCONF_BACKUP_SNAPSHOT_LABEL_FORMAT: 'tenant.is_metro_tenant ? "app:xdr-st-{0}-xsoar" : "app:xdr-st-{0}-xsoar-content"'
  # CRTX-207340
  NotificationDispatcherConf_notification_test_topic_name: '"notification-test-connection-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_test_subscription_name: '"notification-test-connection-sub-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_webhook_topic_name: '"notification-webhook-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_webhook_subscription_name: '"notification-webhook-sub-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_s3_topic_name: '"notification-s3-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_s3_subscription_name: '"notification-s3-sub-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_sqs_topic_name: '"notification-sqs-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_sqs_subscription_name: '"notification-sqs-sub-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_splunk_topic_name: '"notification-splunk-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_splunk_subscription_name: '"notification-splunk-sub-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_dlt_topic_name: '"notification-dlt-" + tenant.lcaas_id'
  NotificationDispatcherConf_notification_dlt_subscription_name: '"notification-dlt-sub-" + tenant.lcaas_id'

stConfig:
  fullnameOverride: local.fullnameOverride
  namespaceOverride: globals.st_namespace
  data: local.config