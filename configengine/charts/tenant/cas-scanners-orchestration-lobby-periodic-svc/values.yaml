fullnameOverride: lobby-periodic

envFrom: []

image:
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/scanners-orchestration-lobby-service
    tag: stable-v1.0.0 # will be replaced on demand
    pullPolicy: IfNotPresent

affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: []
restartPolicy: ""
namespaceOverride: cas
priorityClassName: ""
topologySpreadConstraints: {}
podSecurityContext:
  runAsUser: 1000
  fsGroup: 1000

containerSecurityContext:
  runAsUser: 1000

terminationGracePeriodSeconds: 30

deploymentAnnotations:
  reloader.stakater.com/auto: "true"

livenessProbe:
  httpGet:
    path: /healthz
    port: http

readinessProbe:
  httpGet:
    path: /readyz
    port: http

startupProbe:
  httpGet:
    path: /startupz
    port: http
  initialDelaySeconds: 10
  periodSeconds: 5
  failureThreshold: 30

replicas: 2

service:
  enabled: true
  type: ClusterIP
  port: 8080
  targetPort: 3000

deploymentLabels:
  group: cas
  team: scanops
podLabels:
  group: cas
  team: scanops

podAnnotations: {}

nodeSelector:
  xdr-pool: wi-dynamic

deployment:
  strategy:
    type: RollingUpdate

serviceAccount:
  automountServiceAccountToken: true
  name: lobby-periodic-cas

vsg:
  create: false
  enabled: false
  pollingMinutes: 5
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}

horizontalPodAutoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 120
  metrics:
    - type: Resource
      resource:
        name: "cpu"
        target:
          averageUtilization: 85
          type: "Utilization"
    - type: Resource
      resource:
        name: "memory"
        target:
          averageUtilization: 85
          type: "Utilization"

env:
  METRICS_NAMESPACE: scanners-orchestration-lobby-periodic
  PORT: "3000"
  MIN_LOG_LEVEL:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: MIN_LOG_LEVEL
        optional: true
  PROJECT_ID:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: GCPCONF_PROJECT_ID
        optional: false
  LCAAS_ID:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: LCAAS_ID
        optional: false
  OTEL_FEATURE_FLAG: 'true'
  METRICS_RECEIVER_URL:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: METRICS_RECEIVER_URL
        optional: true
  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_password
        optional: false
  REDIS_HOST:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_host
        optional: false
  CORTEX_PLATFORM_URL:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: CORTEX_PLATFORM_URL
        optional: false
  PRE_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC:
    valueFrom:
      configMapKeyRef:
        key: PRE_ENRICHMENT_SCANNER_REPORTS_PR_TOPIC
        name: cas-configmap
        optional: false
  LOBBY_RESULTS_TOPIC:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: LOBBY_RESULTS_TOPIC
        optional: false
  PERIODIC_DATA_COLLECTOR_TO_LOBBY_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_DATA_COLLECTOR_TO_LOBBY_SUBSCRIPTION
        optional: false
  PERIODIC_POST_SCAN_TO_LOBBY_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_POST_SCAN_TO_LOBBY_SUBSCRIPTION
        optional: false
  LOBBY_RESULTS_DLQ_TOPIC:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: LOBBY_RESULTS_DLQ_TOPIC
        optional: false
  PERIODIC_TASKS_MANAGER_TO_POST_SCAN_DLQ_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_TASKS_MANAGER_TO_POST_SCAN_DLQ_SUBSCRIPTION
        optional: false
  PERIODIC_LOBBY_TO_PRE_SCAN_DLQ_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_LOBBY_TO_PRE_SCAN_DLQ_SUBSCRIPTION
        optional: false
  PERIODIC_POST_SCAN_TO_LOBBY_DLQ_SUBSCRIPTION:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PERIODIC_POST_SCAN_TO_LOBBY_DLQ_SUBSCRIPTION
        optional: false
  PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: PRE_ENRICHMENT_SCANNER_REPORTS_PERIODIC_TOPIC
        optional: false
  WORKFLOWS_ARTIFACTS_BUCKET:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: WORKFLOWS_ARTIFACTS_BUCKET
        optional: false
  WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: WORKFLOWS_SENSITIVE_CUSTOMER_DATA_BUCKET
        optional: false
  SCANS_MANAGEMENT_URL: http://scans-management
  CUSTOMER_MODULES_URL: http://customers-management
  FLOW_TYPE: PERIODIC

prometheus:
  enabled: "true"
  port: "3000"
