namespaceOverride: "globals.st_namespace"

configmap:
  nameOverride: 'tenant.lcaas_id + "-configmap-agentix-hub"'

config:
  GOOGLE_CLOUD_PROJECT: tenant.project_id
  GOOGLE_CLOUD_LOCATION: region.gcp_region
  MYSQL_AGENTIX_DATABASE_NAME: 'tenant.lcaas_id + "_agentix"'
  ELASTICCONF_HOST: globals.env.ELASTICSEARCH_URI
  ELASTIC_RESIDENT_PREFIX: 'tenant.is_metro_tenant ? tenant.lcaas_id : ""'
  REDIS_RESIDENT_PREFIX: 'tenant.is_metro_tenant ? tenant.lcaas_id : ""'
  XQL_DOC_APP_ID: 'region.is_dev ? "connector_1765195909580" : nil'
  XQL_EXAMPLE_APP_ID: 'region.is_dev ? "connector_1765196646565_gcs_store" : nil'
  SEARCH_ENGINE_PROJECT: 'region.is_dev ? "xdr-cortex-copilot-dev-01" : nil'
  SEARCH_ENGINE_LOCATION: 'region.is_dev ? "us" : nil'
