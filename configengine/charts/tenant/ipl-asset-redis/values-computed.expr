_redis_large_tenant: "license.total_agents > 10000"

_securityContext:
  fedramp:
    runAsUser: 999
    runAsGroup: 1000
    fsGroup: 1000

fullnameOverride: 'globals.st_resource_prefix + "-ipl-asset-redis"'
namespaceOverride: "globals.st_namespace"

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "7.4.3-alpine" : "7.4.0-alpine"'

env:
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: "globals.tenant_secrets"
        key: "'assets_redis_password'"
        optional: false

securityContext: "region.is_fedramp ? local._securityContext.fedramp : nil"

persistence:
  storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'
  size: '"6Gi"'

resources:
  requests:
    cpu: 'infra_ff.enable_megatron_xdr ? "1" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : infra_ff.enable_megatron_xdr ? "30Gi" : "8Gi"'
  limits:
    cpu: 'infra_ff.enable_megatron_xdr ? "2" : "1"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : infra_ff.enable_megatron_xdr ? "30Gi" : "8Gi"'

snapshots: 
  dailyEnabled: 'region.viso_env != "dev" || tenant.is_perf_tenant'
  hourlyEnabled: false
  daily:
    app_label: local.fullnameOverride
    retention_days: 30
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"ipl-asset-redis-daily-" + tenant.lcaas_id'
  hourly:
    app_label: local.fullnameOverride
    retention_days: 2
    on_source_disk_delete: '"APPLY_RETENTION_POLICY"'
    name: '"ipl-asset-redis-hourly-" + tenant.lcaas_id'