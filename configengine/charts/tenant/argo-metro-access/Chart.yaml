apiVersion: v2
name: argo-metro-access
description: A Helm chart for argo-metro-access
type: application
version: 0.0.3

annotations:
  panw.com/deploy-eval: "tenant.is_metro_tenant && (globals.enable_cloud_posture || globals.enable_cloud_appsec)"
  owner.panw/group: cas
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/argo-metro-access
  owner.panw/team: postops-control
  owner.panw/team-slack-handle: '@cas-postops-control-oncall'
  owner.panw/people-slack-handle-team-lead: '@ssamira'
  owner.panw/people-slack-handle-owners-group: '@lbecker'
dependencies:
  - name: common
    version: '*.*.*'
    repository: file://../common
