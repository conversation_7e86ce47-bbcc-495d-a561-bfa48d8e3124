apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-workflow-submitter
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-workflow-tenant-submitter-rb
  namespace: {{ .Values.roleNamespace }}
subjects:
- kind: ServiceAccount
  name: argo-workflow-submitter
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: argo-workflow-tenant-submitter-cr
  apiGroup: rbac.authorization.k8s.io