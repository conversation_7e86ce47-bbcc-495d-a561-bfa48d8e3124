_component_name: "'dspm-x-files-momento'"
_component_image_momento: "'dspm-x-files-momento'"
_component_image_cb_worker: "'dspm-cb-listener'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  momento:
    component: "get(images.components, local._component_image_momento)"
    family: "get(images.families, local._images.momento.component.family)"
  cb_worker:
    component: "get(images.components, local._component_image_cb_worker)"
    family: "get(images.families, local._images.cb_worker.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.momento.component.tag ?? local._images.momento.family.tag"
  repository: "local._images.momento.component.repository ?? local._images.momento.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

replicas: "!infra_ff.is_enable_prod_spec ? 1 : 10"

# Configure containers with computed values
containers:
  dspm-x-files-momento:
    image:
      registry: "globals.gcr"
      tag: "local._images.momento.component.tag ?? local._images.momento.family.tag"
      repository: "local._images.momento.component.repository ?? local._images.momento.family.repository"
    resources:
      requests:
        cpu: '!infra_ff.is_enable_prod_spec ? "150m" : "150m"'
        memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1Gi"'
      limits:
        cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
        memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : "2Gi"'
    env:
      ST_RESIDENT_ID: 'tenant.lcaas_id'
      EGRESSPROXY_URL: "globals.egress_proxy_address"
      LISTING_DATA_SNS: '"projects/" + tenant.project_id + "/topics/dspm-listing-data-" + tenant.lcaas_id'
      CORTEX_BASE_PATH: 'globals.env.CORTEX_PLATFORM_URI'
      HEALTH_LOGS_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/cloud-health-monitoring-" + tenant.lcaas_id'
      GENIE_INTERNAL_DOMAIN: '"dspm-oo-genie-svc." + local.namespaceOverride + ".svc.cluster.local"'
      REDIS_PASSWORD:
        valueFrom:
          secretKeyRef:
            name: 'tenant.lcaas_id + "-dspm-secrets"'
            key: "'redis_password'"
            optional: false
      DB_URL:
        valueFrom:
          secretKeyRef:
            name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
            key: "'mongodb_connection_string'"
            optional: false
    envFrom:
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-x-files"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-x-files-feature-flags"'
          optional: false

  dspm-cb-worker:
    image:
      registry: "globals.gcr"
      tag: "local._images.cb_worker.component.tag ?? local._images.cb_worker.family.tag"
      repository: "local._images.cb_worker.component.repository ?? local._images.cb_worker.family.repository"
    resources:
      requests:
        cpu: '!infra_ff.is_enable_prod_spec ? "150m" : "150m"'
        memory: '!infra_ff.is_enable_prod_spec ? "256Mi" : "256Mi"'
      limits:
        cpu: '!infra_ff.is_enable_prod_spec ? "1" : "3"'
        memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "2Gi"'
    env:
      ST_RESIDENT_ID: 'tenant.lcaas_id'
    envFrom:
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-cb"'
          optional: false
      - configMapRef:
          name: 'tenant.lcaas_id + "-configmap-dspm-cb-feature-flags"'
          optional: false

initContainers:
  initTemporalMomento:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
  initTemporalCbWorker:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'

hpa:
  maxReplicas: 'region.viso_env == "dev" ? "4" : "14"'
