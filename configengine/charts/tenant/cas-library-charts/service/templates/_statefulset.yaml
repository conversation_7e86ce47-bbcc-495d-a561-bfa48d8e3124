{{ define "service-library-chart.statefulset" }}
{{- $statefulSet := .Values.statefulSet | default dict }}
{{- if $statefulSet.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "service-library-chart.fullname" . }}
  labels:
    app: {{ include "chart.fullname" . }}
    {{- include "service-library-chart.labels" . | nindent 4 }}
  namespace: {{ include "common.namespace" . }}
spec:
  serviceName: {{ include "service-library-chart.fullname" . }}
  replicas: {{ .Values.statefulSet.replicaCount | default 1 }}
  podManagementPolicy: {{ .Values.statefulSet.podManagementPolicy | default "OrderedReady" }}
  {{- with .Values.statefulSet.persistentVolumeClaimRetentionPolicy }}
  persistentVolumeClaimRetentionPolicy: {{ toYaml . | nindent 4 }}
  {{ end }}
  selector:
    matchLabels:
      {{- include "service-library-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if and .Values.prometheus (eq .Values.prometheus.enabled "true") }}
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.prometheus.port | default "3000" }}"
        {{- end }}
        {{- if .Values.featureFlags }}
        configmap.reloader.stakater.com/reload: cas-configmap,cas-configmap-feature-flags
        {{- end }}
      labels:
        app: {{ include "chart.fullname" . }}
        {{- include "service-library-chart.labels" . | nindent 8 }}
        {{- with .Values.deploymentLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- if .Values.serviceAccount.automountServiceAccountToken }}
      automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceAccount.name }}
      serviceAccountName: {{ .Values.serviceAccount.name }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          {{- if .Values.localEnv }}
          image: {{ printf "%s/%s:%s" .Values.image.repository .Values.image.name .Values.image.tag }}
          {{- else }}
          image: {{ printf "%s/%s:%s" .Values.image.registry .Values.image.repository .Values.image.tag }}
          {{- end }}
          imagePullPolicy: {{ default "IfNotPresent" .Values.image.pullPolicy }}
          ports:
          - name: http
            containerPort: {{ .Values.service.targetPort }}
            protocol: TCP
          {{- with .Values.env }}
          env: {{ (include "common.renderEnv" .) | nindent 12 }}
          {{- end }}
          {{- with .Values.envFrom }}
          envFrom: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.startupProbe }}
          startupProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.livenessProbe }}
          livenessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.containerSecurityContext }}
          securityContext: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.resources }}
          resources: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- if .Values.featureFlags }}
          envFrom:
            - configMapRef:
                name: cas-configmap
            - configMapRef:
                name: cas-configmap-feature-flags
          {{- end }}
          {{- with .Values.extraVolumeMounts }}
          volumeMounts: {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
        - {{ toYaml $constraint | nindent 10 | trim }}
          {{- if not $constraint.labelSelector }}
          labelSelector:
            matchLabels: {{ include "chart.selectorLabels" $ | nindent 14 }}
          {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.restartPolicy }}
      restartPolicy: {{ . }}
      {{- end }}
      {{- with .Values.podSecurityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . | int }}
      {{- end }}
      volumes:
        {{- with .Values.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.statefulSet.volumeClaimTemplates }}
  volumeClaimTemplates:
  {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}