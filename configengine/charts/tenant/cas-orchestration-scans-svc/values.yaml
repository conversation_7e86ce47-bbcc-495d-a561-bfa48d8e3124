fullnameOverride: scans

# -- service number of replicas
replicas: 1

envFrom: []

image:   # replace with {} after the migration to Cortex new platform
    registry: us-docker.pkg.dev/xdr-registry-prod-us-01
    repository: cas-docker/orchestration-scans
    tag: stable-v1.0.97-7ef8253d # will be replaced on demand
    pullPolicy: IfNotPresent

affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: []
restartPolicy: ""
namespaceOverride: cas # remove after the migration to Cortex new platform
priorityClassName: ""
topologySpreadConstraints: {}
podSecurityContext: {}

containerSecurityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

terminationGracePeriodSeconds: 30

deploymentAnnotations:
  reloader.stakater.com/auto: "true"

livenessProbe:
  httpGet:
    path: /healthz
    port: http

readinessProbe:
  httpGet:
    path: /readyz
    port: http

startupProbe: {}

service:
  enabled: true
  type: ClusterIP
  port: 80
  targetPort: 3000

deploymentLabels:
  group: cas
  team: flow
podLabels:
  group: cas
  team: flow

podAnnotations: {}

nodeSelector:
  xdr-pool: wi-dynamic

deployment:
  strategy:
    type: RollingUpdate

serviceAccount:
  # -- # Set to false to prevent automatic mounting of the service account token into the pod
  automountServiceAccountToken: true
  # -- The name of the service account to use.
  # cas service account would be created using cas-service-accounts chart
  name: "scans-cas"

# securityContext:
#   fsGroup: 1001

# For more information, refer to VSG README.md: https://gitlab.xdr.pan.local/xdr/development/komodo/-/blob/master/README.md
vsg:
  create: false
  enabled: false
  pollingMinutes: 5
  project: ""
  oomScaling: {}
  verticalScaling: {}
  zeroScaling: {}

horizontalPodAutoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: "Percent"
          value: 10
      selectPolicy: "Max"
      stabilizationWindowSeconds: 120
  metrics:
    - type: Resource
      resource:
        name: "cpu"
        target:
          averageUtilization: 85
          type: "Utilization"
    - type: Resource
      resource:
        name: "memory"
        target:
          averageUtilization: 85
          type: "Utilization"

env:
  MIN_LOG_LEVEL:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: MIN_LOG_LEVEL
        optional: true
  DEFAULT_BUCKET:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: WORKFLOWS_ARTIFACTS_BUCKET
  WORKFLOWS_NAMESPACE:
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace
  PR_SCAN_TEMPLATE_NAME: pr-scan-template
  PR_SCAN_V2_TEMPLATE_NAME: pr-scan-v2-template
  PR_SCAN_NEXT_GEN_TEMPLATE_NAME: pr-scan-next-gen-template
  CLI_SCAN_TEMPLATE_NAME: cli-scan-template
  SCAN_REPOSITORY_WORKFLOW_TEMPLATE_NAME: repo-scan-template
  CUSTOMER_SCAN_TEMPLATE_NAME: customer-scan-template
  EXTERNAL_PROJECT_SCAN_TEMPLATE_NAME: external-project-scan-template
  PRISMA_MIGRATION_SCANNED_BRANCHES_TEMPLATE_NAME: prisma-migration-scanned-branches-template
  ARGO_API_KEY:
    valueFrom:
      secretKeyRef:
        name: argo-controller.service-account-token
        key: token
        optional: false
  ARGO_SERVER_URL: http://argo-workflows-server.cas.svc.cluster.local:2746
  FILES_SCAN_TEMPLATE_NAME: scan-and-persist-template
  METRICS_NAMESPACE: scans
  LCAAS_ID:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: LCAAS_ID
        optional: false
  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_password
        optional: true
  REDIS_HOST:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: redis_host
        optional: true
  MONGO_DB_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: infra-secrets
        key: mongodb_password
        optional: false
  MONGO_DB_HOST_NAME:
    valueFrom:
      configMapKeyRef:
        name: cas-configmap
        key: MONGO_DB_HOST_NAME
        optional: false
  MONGO_DB_USERNAME: root
  MONGO_DB_DATABASE_NAME: cas