global:
  nameOverride: 'tenant.lcaas_id + "-configmap"'

tags:
  cortex-platform: infra_ff.enable_cortex_platform
  pipeline: infra_ff.enable_pipeline
  rocksdb: local.tags.pipeline || license.is_xpanse
  rocksdb-writer: infra_ff.is_enable_rocksdb_writer
  xsoar: infra_ff.enable_xsoar_shared_components
  xsoar-workers: local.tags.xsoar && infra_ff.is_enable_xsoar_workers

feature-flags:
  AlphaFeatures_ios_support: 'region.is_fedramp ? "False" : nil'
  ALPHAFEATURES_ENABLE_ALERT_BULK_OPERATIONS: 'license.product_type != "XPANSE" ? "False" : "True"'
  ALPHAFEATURES_ENABLE_CUSTOMER_ASM_UPLOAD: 'license.product_type == "XPANSE" || license.product_type == "XSIAM" ? "True" : "False"'
  ALPHAFEATURES_ENABLE_EM_COMPENSATING_CONTROLS: 'infra_ff.is_enable_exposure_management ? "True" : nil'
  ALPHAFEATURES_ENABLE_EXPOSURE_MANAGEMENT: 'infra_ff.is_enable_exposure_management ? "True" : nil'
  EMModuleConf_em_inbound_module_findings_enabled: 'infra_ff.is_enable_exposure_management ? "True" : nil'
  UVEM_ENABLE_CC: 'infra_ff.is_enable_exposure_management ? "True" : nil'
  VulnerabilityNetworkScanningConf_vulnerability_network_scanning_enabled: 'infra_ff.is_enable_exposure_management ? "True" : nil'
  # start replication feature-flags:
  HPL_PZ_LCAAS_SUBSCRIPTION: |-
    globals.replication_metadata.is_lcaas ? "lavawall-replica-" + tenant.lcaas_id : nil
  HPL_XQL_EXT_SUBSCRIPTION: |-
    globals.replication_metadata.is_ext_logs ? "xql-ext-logs-replica-" + tenant.lcaas_id : nil
  HPL_EDR_SOURCE_SUBSCRIPTION: |-
    globals.replication_metadata.is_edr ? "edr-raw-replica-" + tenant.lcaas_id : nil
  DSSCONF_SYNC_WITH_AD: |-
    globals.replication_metadata.is_dss ? "1" : nil
  DSSCONF_TENANT_ID: |-
    globals.replication_metadata.is_dss ? globals.replication_metadata.source_tenant_id : nil
  HPL_XQL_APP_HUB_OBSERVABILITY_SOURCE_SUBSCRIPTION: |-
    globals.replication_metadata.is_app_hub_observability ?
    "app-hub-observability-replica-" + tenant.lcaas_id : nil
  ALERTSCONF_FETCHER_SUBSCRIPTION_NAME: |-
    globals.replication_metadata.is_agent_alerts ?
    "alerts-fetcher-replica-" + tenant.lcaas_id : nil
  ANALYTICSCONF_BIG_QUERY_SRC_PROJECT: |-
    globals.replication_metadata.is_detection_engine_only ?
    tenant.replication_source_project_id : nil
  ANALYTICSCONF_BIG_QUERY_SRC_DATASET: |-
    globals.replication_metadata.is_detection_engine_only ?
    "ds_" + globals.replication_metadata.source_tenant_id : nil
  ANALYTICSCONF_DETECTION_ENGINE_READ_SUBSCRIPTION: |-
    globals.replication_metadata.is_detection_engine_only ?
    "edr-replica-" + tenant.lcaas_id : nil
  # end replication feature-flags
  XqlSyncServiceConf_content_sync_version: nil
  DSSCONF_XQL_AD_OBJECTS_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  ASSOCIATION_REPLICATION_ASSOCIATIONS_STORAGE: 'infra_ff.enable_alyx ? "alyx" : nil'
  ASSOCIATION_REPLICATION_ASSETS_STORAGE: 'infra_ff.enable_alyx ? "alyx" : nil'
  FORENSICSCONF_STORE: 'infra_ff.enable_alyx ? "alyx" : nil'
  COPILOT_USE_AGENT_BUILDER: 'license.product_type == "XSIAM" && tenant.is_cortex_platform ? "True" : nil'
  ALPHAFEATURES_ENABLE_ALERT_PERFORMANCE_IMPROVEMENTS_TRC: 'license.product_type != "XPANSE" ? "False" : "True"'
  ALPHAFEATURES_ENABLE_TRC_ENUMERATION: 'license.product_type == "XPANSE" || license.product_type == "XSIAM" ? "True" : "False"'
  ALPHAFEATURES_ENABLE_SERVICE_VERSION: 'region.is_dev ? none : license.product_type == "XPANSE" || license.product_type == "XSIAM" ? "True" : "False"'

dml-feature-flags:
  HPL_KEY_VALUE_STORE_ASSETS_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_ASSOCIATION_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_DSS_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_EAL_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_TGT_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_WEC_LOGIN_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_ENRICHMENT_STORE_TYPE: 'infra_ff.enable_alyx ? "alyx" : nil'
  HPL_KEY_VALUE_STORE_ALYX_TOPOLOGY_PATH: 'infra_ff.enable_alyx ? "configmap://alyx-cluster-node-topology/alyx:topology.json" : nil'
  # start replication dml-feature-flags
  cm_name: |-
    globals.replication_metadata.is_replication_enabled && !(tenant.replication_type in globals.replication_metadata.detection_engine_only) ?
    tenant.lcaas_id + "-configmap-feature-flags" : nil
  GONZO_LOGGER_TRANSPORT: |-
    globals.replication_metadata.is_replication_enabled && !(tenant.replication_type in globals.replication_metadata.detection_engine_only) ?
    "console:0" : nil
  # start replication dml-feature-flags
  HPL_ASSET_PIPELINE_REAL_TIME_DB: 'tenant.creation_date > 1763899200000 || (tenant.is_migrated_cortex_platform && infra_ff.enable_spanner) ? "spanner" : nil'  # spanner migration FF


xql-feature-flags:
  HPL_EDR_PIPELINE_DISABLED: 'infra_ff.enable_msft_s1 ? "False" : nil'
  HPL_ENABLE_FDR_TO_EDR_BRIDGE: 'infra_ff.enable_msft_s1 ? "True" : nil'

cas-feature-flags:
  namespaceOverride: globals.cas_namespace
  fullnameOverride: "'cas-configmap-feature-flags'"

ciem-feature-flags:
  namespaceOverride: globals.ciem_namespace

cwp-feature-flags:
  namespaceOverride: globals.cwp_namespace

dspm-ace-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-ack-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-cb-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-dpc-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-das-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-fda-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-mac-feature-flags:
  namespaceOverride: globals.dspm_namespace

dspm-x-files-feature-flags:
  namespaceOverride: globals.dspm_namespace

apisec-feature-flags:
  namespaceOverride: globals.apisec_namespace

dspm-worker-feature-flags:
  namespaceOverride: globals.dspm_namespace

frontend-feature-flags-no-restart:
  UVEM_ENABLE_CC: 'infra_ff.is_enable_exposure_management ? "True" : nil'

email-security-feature-flags:
  namespaceOverride: globals.st_namespace
