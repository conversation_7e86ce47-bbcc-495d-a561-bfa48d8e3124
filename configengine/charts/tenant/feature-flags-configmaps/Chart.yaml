apiVersion: v2
name: feature-flags-configmaps
description: A Helm chart for Kubernetes
type: application
version: 0.3.0
appVersion: "1.16.0"
annotations:
  "panw.com/deploy-eval": "true"
  owner.panw/group: xdr
  owner.panw/source-code-ops-helm-chart-url: https://gitlab.xdr.pan.local/xdr/devops/gcp-configuration/tree/dev/configengine/charts/tenant/redis
  owner.panw/team: devops
  owner.panw/team-slack-handle: '@platform-infra-owners'
  owner.panw/people-slack-handle-team-lead: '@gacohen'
  owner.panw/people-slack-handle-owners-group: '@mweigert'
dependencies:
  - name: common
    version: "*.*.*"
    repository: "file://../common"
  - name: feature-flags
    version: "*.*.*"
  - alias: cas-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: ciem-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: cwp-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: itdr-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dml-feature-flags
    name: feature-flags
    version: "*.*.*"
  - alias: dspm-cb-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-dpc-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-das-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-worker-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-fda-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-mac-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: dspm-x-files-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - cortex-platform
  - alias: frontend-feature-flags-no-restart
    name: feature-flags
    version: "*.*.*"
  - alias: rocksdb-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - rocksdb
  - alias: rocksdb-writer-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - rocksdb-writer
  - alias: scortex-feature-flags
    name: feature-flags
    version: "*.*.*"
  - alias: xql-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - pipeline
  - alias: xsoar-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - xsoar
  - alias: xsoar-feature-flags-no-restart
    name: feature-flags
    version: "*.*.*"
    tags:
      - xsoar
  - alias: xsoar-workers-gateway-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - xsoar-workers
  - alias: apisec-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - apisec
  - alias: email-security-feature-flags
    name: feature-flags
    version: "*.*.*"
    tags:
      - email-security