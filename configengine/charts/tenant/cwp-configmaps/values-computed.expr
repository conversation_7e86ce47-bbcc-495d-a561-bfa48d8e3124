tags:
  metroTenant: tenant.is_metro_tenant

_fqdn_suffix: 'split(globals.external_fqdn, ".")[0] + (region.is_dev ? "-dev" : "")'
_distribution_postfix: 'region.multi_project_postfix == "dev" ? "-dev" : region.multi_project_postfix == "prod-fr" ? "-prod-fed" : region.multi_project_postfix == "prod-gv" ? "-prod-gv" : ""'

namespaceOverride: "globals.cwp_namespace"

configmap:
  nameOverride: 'tenant.lcaas_id + "-configmap-cwp"'

_config:
  ADS_CONTROLLER_ASSUME_ROLE_ARN:
    dev: 'tenant.creation_date > ************* ? "arn:aws:iam::************:role/pan/cwp-account-controller20250305140355883100000002" : "arn:aws:iam::************:role/pan/cwp-account-controller20240721132734044200000002"'
    prod-pr: '"arn:aws:iam::************:role/pan/cwp-account-controller20241029083210222000000002"'
    prod-us: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101717836900000002"'
    prod-uk: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101510267000000002"'
    prod-tw: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101308702900000002"'
    prod-sg: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101109251100000002"'
    prod-sa: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100810848100000002"'
    prod-qt: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100552436900000002"'
    prod-pl: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100146985300000002"'
    prod-kr: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095902851700000002"'
    prod-jp: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095702831900000002"'
    prod-it: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095452172100000002"'
    prod-in: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095253715100000002"'
    prod-il: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095050835000000002"'
    prod-id: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094840637200000002"'
    prod-fa: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094539954400000002"'
    prod-eu: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094329521500000002"'
    prod-es: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094137591600000002"'
    prod-de: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093938868500000002"'
    prod-ch: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093709215000000002"'
    prod-ca: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093504817600000002"'
    prod-au: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093249205100000002"'
    prod-za: '"arn:aws:iam::************:role/pan/cwp-account-controller20250324133230642700000002"'
    prod-br: '"arn:aws:iam::************:role/pan/cwp-account-controller20251111153117340200000002"'
    prod-dl: '"arn:aws:iam::************:role/pan/cwp-account-controller20251111153109548200000002"'
    prod-fr: '"arn:aws:iam::************:role/pan/cwp-account-controller20250625142031916900000002"'
    prod-gv: '"arn:aws:iam::************:role/pan/cwp-account-controller20250626081722749600000002"'
  ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT:
    dev: '"<EMAIL>"'
    prod-pr: '"<EMAIL>"'
    prod-us: '"<EMAIL>"'
    prod-uk: '"<EMAIL>"'
    prod-tw: '"<EMAIL>"'
    prod-sg: '"<EMAIL>"'
    prod-sa: '"<EMAIL>"'
    prod-qt: '"<EMAIL>"'
    prod-pl: '"<EMAIL>"'
    prod-kr: '"<EMAIL>"'
    prod-jp: '"<EMAIL>"'
    prod-it: '"<EMAIL>"'
    prod-in: '"<EMAIL>"'
    prod-il: '"<EMAIL>"'
    prod-id: '"<EMAIL>"'
    prod-fa: '"<EMAIL>"'
    prod-eu: '"<EMAIL>"'
    prod-es: '"<EMAIL>"'
    prod-de: '"<EMAIL>"'
    prod-ch: '"<EMAIL>"'
    prod-ca: '"<EMAIL>"'
    prod-au: '"<EMAIL>"'
    prod-za: '"<EMAIL>"'
    prod-br: '"<EMAIL>"'
    prod-dl: '"<EMAIL>"'
    prod-fr: '"<EMAIL>"'
    prod-gv: '"<EMAIL>"'
  ADS_CONTROLLER_KMS_KEY_ALIAS:
    dev: '"dev-pan-managed-encrypt-snapshots-customers-generic"'
    prod-pr: '"prod-pr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-us: '"prod-us-pan-managed-encrypt-snapshots-customers-generic"'
    prod-uk: '"prod-uk-pan-managed-encrypt-snapshots-customers-generic"'
    prod-tw: '"prod-tw-pan-managed-encrypt-snapshots-customers-generic"'
    prod-sg: '"prod-sg-pan-managed-encrypt-snapshots-customers-generic"'
    prod-sa: '"prod-sa-pan-managed-encrypt-snapshots-customers-generic"'
    prod-qt: '"prod-qt-pan-managed-encrypt-snapshots-customers-generic"'
    prod-pl: '"prod-pl-pan-managed-encrypt-snapshots-customers-generic"'
    prod-kr: '"prod-kr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-jp: '"prod-jp-pan-managed-encrypt-snapshots-customers-generic"'
    prod-it: '"prod-it-pan-managed-encrypt-snapshots-customers-generic"'
    prod-in: '"prod-in-pan-managed-encrypt-snapshots-customers-generic"'
    prod-il: '"prod-il-pan-managed-encrypt-snapshots-customers-generic"'
    prod-id: '"prod-id-pan-managed-encrypt-snapshots-customers-generic"'
    prod-fa: '"prod-fa-pan-managed-encrypt-snapshots-customers-generic"'
    prod-eu: '"prod-eu-pan-managed-encrypt-snapshots-customers-generic"'
    prod-es: '"prod-es-pan-managed-encrypt-snapshots-customers-generic"'
    prod-de: '"prod-de-pan-managed-encrypt-snapshots-customers-generic"'
    prod-ch: '"prod-ch-pan-managed-encrypt-snapshots-customers-generic"'
    prod-ca: '"prod-ca-pan-managed-encrypt-snapshots-customers-generic"'
    prod-au: '"prod-au-pan-managed-encrypt-snapshots-customers-generic"'
    prod-za: '"prod-za-pan-managed-encrypt-snapshots-customers-generic"'
    prod-br: '"prod-br-pan-managed-encrypt-snapshots-customers-generic"'
    prod-dl: '"prod-dl-pan-managed-encrypt-snapshots-customers-generic"'
    prod-fr: '"prod-fr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-gv: '"prod-gv-pan-managed-encrypt-snapshots-customers-generic"'
  ADS_SNAPSHOT_CORTEX_KMS_KEY_ALIAS:
    dev: '"dev-pan-managed-encrypt-snapshots-customers-generic"'
    prod-pr: '"prod-pr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-us: '"prod-us-pan-managed-encrypt-snapshots-customers-generic"'
    prod-uk: '"prod-uk-pan-managed-encrypt-snapshots-customers-generic"'
    prod-tw: '"prod-tw-pan-managed-encrypt-snapshots-customers-generic"'
    prod-sg: '"prod-sg-pan-managed-encrypt-snapshots-customers-generic"'
    prod-sa: '"prod-sa-pan-managed-encrypt-snapshots-customers-generic"'
    prod-qt: '"prod-qt-pan-managed-encrypt-snapshots-customers-generic"'
    prod-pl: '"prod-pl-pan-managed-encrypt-snapshots-customers-generic"'
    prod-kr: '"prod-kr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-jp: '"prod-jp-pan-managed-encrypt-snapshots-customers-generic"'
    prod-it: '"prod-it-pan-managed-encrypt-snapshots-customers-generic"'
    prod-in: '"prod-in-pan-managed-encrypt-snapshots-customers-generic"'
    prod-il: '"prod-il-pan-managed-encrypt-snapshots-customers-generic"'
    prod-id: '"prod-id-pan-managed-encrypt-snapshots-customers-generic"'
    prod-fa: '"prod-fa-pan-managed-encrypt-snapshots-customers-generic"'
    prod-eu: '"prod-eu-pan-managed-encrypt-snapshots-customers-generic"'
    prod-es: '"prod-es-pan-managed-encrypt-snapshots-customers-generic"'
    prod-de: '"prod-de-pan-managed-encrypt-snapshots-customers-generic"'
    prod-ch: '"prod-ch-pan-managed-encrypt-snapshots-customers-generic"'
    prod-ca: '"prod-ca-pan-managed-encrypt-snapshots-customers-generic"'
    prod-au: '"prod-au-pan-managed-encrypt-snapshots-customers-generic"'
    prod-za: '"prod-za-pan-managed-encrypt-snapshots-customers-generic"'
    prod-br: '"prod-br-pan-managed-encrypt-snapshots-customers-generic"'
    prod-dl: '"prod-dl-pan-managed-encrypt-snapshots-customers-generic"'
    prod-fr: '"prod-fr-pan-managed-encrypt-snapshots-customers-generic"'
    prod-gv: '"prod-gv-pan-managed-encrypt-snapshots-customers-generic"'
  geo_location_dataset: 'region.viso_env == "dev" ? "geolite_" + region.viso_env: "geolite_" + replace(region.viso_env, "-", "_")'

_shared_resources_role_mapping:
  dev: 'tenant.creation_date > ************* ? "arn:aws:iam::************:role/pan/cwp-account-controller20250305140355883100000002" : "arn:aws:iam::************:role/pan/cwp-account-controller20240721132734044200000002"'
  prod-pr: '"arn:aws:iam::************:role/pan/cwp-account-controller20241029083210222000000002"'
  prod-us: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101717836900000002"'
  prod-uk: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101510267000000002"'
  prod-tw: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101308702900000002"'
  prod-sg: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213101109251100000002"'
  prod-sa: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100810848100000002"'
  prod-qt: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100552436900000002"'
  prod-pl: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213100146985300000002"'
  prod-kr: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095902851700000002"'
  prod-jp: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095702831900000002"'
  prod-it: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095452172100000002"'
  prod-in: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095253715100000002"'
  prod-il: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213095050835000000002"'
  prod-id: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094840637200000002"'
  prod-fa: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094539954400000002"'
  prod-eu: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094329521500000002"'
  prod-es: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213094137591600000002"'
  prod-de: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093938868500000002"'
  prod-ch: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093709215000000002"'
  prod-ca: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093504817600000002"'
  prod-au: '"arn:aws:iam::************:role/pan/cwp-account-controller20250213093249205100000002"'
  prod-br: '"arn:aws:iam::************:role/pan/cwp-account-controller20251111153117340200000002"'
  prod-dl: '"arn:aws:iam::************:role/pan/cwp-account-controller20251111153109548200000002"'
  prod-fr: '"arn:aws:iam::************:role/pan/cwp-account-controller20250625142031916900000002"'
  prod-gv: '"arn:aws:iam::************:role/pan/cwp-account-controller20250626081722749600000002"'
config:
  ADS_API_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-ads-api-svc:8080"'
  ADS_CLEANING_CORTEX_PLATFORM_URL: "globals.env.CORTEX_PLATFORM_URL"
  ADS_CONTROLLER_ASSUME_ROLE_ARN: get(local._config.ADS_CONTROLLER_ASSUME_ROLE_ARN, region.viso_env)
  ADS_CONTROLLER_KMS_KEY_ALIAS: get(local._config.ADS_CONTROLLER_KMS_KEY_ALIAS, region.viso_env)
  ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT: get(local._config.ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT, region.viso_env)
  ADS_GROUPING_MAX_INSTANCE_AGE: '"20m"'
  ADS_GROUPING_MIN_SNAPSHOT_COUNT: '"5"'
  ADS_LAUNCH_WORKLOAD_ORCHESTRATOR_ADDRESS: '"xdr-st-" + tenant.lcaas_id + "-cwp-sp-workload-orchestration-svc"'
  ADS_LAUNCH_AWS_MIN_VCPUS: '"2"'
  ADS_LAUNCH_AWS_MIN_MEMORY_GB: '"8"'
  ADS_LAUNCH_GCP_MIN_VCPUS: '"4"'
  ADS_LAUNCH_GCP_MIN_MEMORY_GB: '"8"'
  ADS_LAUNCH_AZURE_MIN_VCPUS: '"2"'
  ADS_LAUNCH_AZURE_MIN_MEMORY_GB: '"8"'
  ADS_LAUNCH_OCI_MIN_VCPUS: '"2"'
  ADS_LAUNCH_OCI_MIN_MEMORY_GB: '"8"'
  ADS_OBSERVER_PUBSUB_SUBSCRIPTION_ID: '"cwp-sp-lifecycle-events-ads-result-sync-sub-" + tenant.lcaas_id'
  ADS_POSTGRES_DB_HOST: globals.env.PGHOST
  ADS_SNAPSHOT_CORTEX_KMS_KEY_ALIAS: get(local._config.ADS_SNAPSHOT_CORTEX_KMS_KEY_ALIAS, region.viso_env)
  ADS_SNAPSHOT_CORTEX_PLATFORM_URL: "globals.env.CORTEX_PLATFORM_FQDN"
  APISEC_ANALYTICS_DATASET: '"apisec_analytics_" + tenant.lcaas_id'
  APISEC_ASSET_INGESTION_ERRORS_SUB: '"apisec-dp-uai-asset-ingestion-errors-sub-" + tenant.lcaas_id'
  APISEC_DP_BUS_ENRICHER_SUB: '"apisec-dp-bus-enricher-sub-" + tenant.lcaas_id'
  APISEC_CONTENT_BUCKET_NAME: 'region.viso_env == "dev" ? "global-apisec-dynamic-content-dev": "global-apisec-dynamic-content"'
  APISEC_CONTENT_BUCKET_PROJECT_ID: '"xdr-zeus"'
  APISEC_ASSET_UPDATES_ASSET_MANAGER_SUB: '"apisec-asset-updates-asset-manager-sub-" + tenant.lcaas_id'
  APISEC_GROUPED_ASSET_MANAGER_SUB: '"apisec-grouped-asset-manager-sub-" + tenant.lcaas_id'
  APISEC_GROUPED_INSEPCTION_SUB: '"apisec-grouped-inspection-sub-" + tenant.lcaas_id'
  APISEC_GROUPED_RISK_SUB: '"apisec-grouped-risk-sub-" + tenant.lcaas_id'
  APISEC_GROUPED_TRANSACTIONS_BQ_TOPIC: '"apisec-grouped-transactions-analytics-bq-" + tenant.lcaas_id'
  APISEC_GROUPED_TRANSACTIONS_TOPIC: '"apisec-grouped-transactions-" + tenant.lcaas_id'
  APISEC_RISK_FINDINGS_ERRORS_SUBSCRIPTION: '"dp-finding-ingestion-errors-risk-sub-" + tenant.lcaas_id'
  APISEC_RISK_ISSUES_ERRORS_SUBSCRIPTION: '"ap-issue-ingestion-errors-risk-sub-" + tenant.lcaas_id'
  APISEC_RISK_MIN_LOG_LEVEL: '"INFO"'
  APISEC_TRANSACTION_GROUPING_SUB: '"apisec-transactions-grouping-sub-" + tenant.lcaas_id'
  APISEC_TRANSACTION_TOPIC: '"apisec-transactions-" + tenant.lcaas_id'
  APISEC_SPEC_ASSET_MANAGER_SUB: '"apisec-spec-asset-manager-sub-" + tenant.lcaas_id'
  APISEC_SPEC_RISK_ENGINE_SUB: '"apisec-spec-risk-engine-sub-" + tenant.lcaas_id'
  APISEC_SPEC_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-apisec-spec-service-svc:8080"'
  APISEC_SPEC_TOPIC: '"apisec-spec-" + tenant.lcaas_id'
  APISEC_SPEC_SERVICE_MIN_LOG_LEVEL: '"INFO"'
  APISEC_SPEC_JOB_MIN_LOG_LEVEL: '"INFO"'
  APISEC_SPEC_GATE_MIN_LOG_LEVEL: '"INFO"'
  APISEC_TESTING_SCAN_RESULTS_BUCKET_NAME: 'tenant.project_id + "-apisec-scan-results"'
  APISEC_BFF_MIN_LOG_LEVEL: '"INFO"'
  BQSTATSCONF_ST_TOPIC_NAME: '"bq-stats-" + tenant.lcaas_id'
  CONTENT_BUCKET: 'region.is_dev || region.is_fedramp ? "global-apisec-content-" + region.multi_project_postfix : "global-apisec-content"'
  CONTENT_EXCEPTION_BUCKET: 'region.is_dev || region.is_fedramp ? "global-apisec-content-" + region.multi_project_postfix : "global-apisec-content"'
  CONTENT_EXCEPTION_LOCATION: '"exception/" + tenant.lcaas_id'
  CORE_ASSET_ANALYZER_REDIS_HOST: globals.env.REDIS_CWP_URI
  CORTEX_PLATFORM_URL: "globals.env.CORTEX_PLATFORM_URL"
  CWP_AWS_SHARED_RESOURCES_ACCOUNT_ID: globals.cwp_aws_shared_resources_account_id
  CWP_RELEASES_CORTEXCLI_BUCKET_NAME: 'region.viso_env == "dev" ? "global-cli-scanner-binaries-dev": "global-cli-scanner-binaries"'
  CWP_SCAN_SPEC_MANAGER_POSTGRES_DB_HOST: globals.env.PGHOST
  CWP_REGISTRY_SCAN_REDIS_HOST: globals.env.REDIS_CWP_URI
  CWP_REDIS_HOST: globals.env.REDIS_CWP_URI
  CWP_REDIS_PORT: '"6379"'
  PC1_MIGRATION_REDIS_HOST: globals.env.REDIS_CWP_URI
  PC1_MIGRATION_REDIS_PORT: '"6379"'
  PC1_MIGRATION_REDIS_DB_NUMBER: '"0"'
  EDR_BUCKET: 'tenant.project_id + "-edr-data"'
  EGRESSPROXY_URL: "globals.egress_proxy_address"
  FINDINGS_EMIT_TOPIC: '"dp-finding-emits-" + tenant.lcaas_id'
  FINDINGS_VIEW: '"public_platform_" + tenant.lcaas_id + ".findings_view"'
  GCP_PROJECT_ID: tenant.project_id
  GCPCONF_PROJECT_ID: tenant.project_id
  GENERIC_PRODUCT_TYPE: lower(license.product_type)
  GENERIC_PRODUCT_CODE: license.product_code
  GENERIC_XDR_ENVIRONMENT: region.xdr_env
  GEO_IP_BQ_TABLE_NAME: '"xdr-bq-mt-stats-" + region.viso_env + "-01." + local._config.geo_location_dataset + ".maxmind"'
  GONZO_PROJECT_ID: tenant.project_id
  IMAGE_ANALYZER_POSTGRES_DB_HOST: globals.env.PGHOST
  IMAGE_ANALYZER_POSTGRES_DB_PORT: '"5432"'
  IMAGE_ANALYZER_POSTGRES_DB_USER: '"root"'
  IMAGE_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-image-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  IMAGE_ANALYZER_REDIS_PORT: '"6379"'
  IMAGE_ANALYZER_REDIS_HOST: globals.env.REDIS_CWP_URI
  IMAGE_ANALYZER_REDIS_DB_NUMBER: '"0"'
  ISSUE_UPSERT_TOPIC: '"ap-issue-upsert-" + tenant.lcaas_id'
  ISSUES_VIEW: '"public_platform_" + tenant.lcaas_id + ".issues_view"'
  K8S_PUBSUB_INVENTORY_SUBSCRIPTION_NAME: '"cwp-k8s-data-inventory-sub-" + tenant.lcaas_id'
  K8S_PUBSUB_INVENTORY_TOPIC_NAME: '"cwp-k8s-data-" + tenant.lcaas_id'
  K8SCONNECTORCONF_API_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-k8s-api:8080"'
  K8SCONNECTORCONF_GCS_INVENTORY_BUCKET_NAME: 'tenant.project_id + "-cwp-k8s-data"'
  K8SCONNECTORCONF_LOG_INGESTOR_TOPIC_NAME: '"cwp-k8s-connector-logs-" + tenant.lcaas_id'
  K8SCONNECTORCONF_MYSQL_DB_NAME: 'tenant.lcaas_id + "_main"'
  K8SCONNECTORCONF_MYSQL_URL: globals.env.MYSQL_MAIN_URI
  K8SCONNECTORCONF_POSTGRES_HOST: globals.env.PGHOST
  KAPI_POSTGRES_HOST: globals.env.PGHOST
  K8SCONNECTORCONF_PUBSUB_INVENTORY_TOPIC_NAME: '"cwp-k8s-data-" + tenant.lcaas_id'
  K8SCONNECTORCONF_RULES_NOTIFICATION_SUBSCRIPTION: '"cwp-policy-rules-notification-sub-" + tenant.lcaas_id'
  K8SCONNECTORCONF_GAR_PROJECT_ID: "tenant.project_id"
  K8SCONNECTORCONF_GAR_LOCATION: "region.gcp_region"
  K8SCONNECTORCONF_GAR_REPOSITORY: "'agent-docker'"
  KAPI_IMAGE_REVIEWERS_URLS: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-malware-analyzer-svc.cwp.svc.cluster.local:8080" + ",http://xdr-st-" + tenant.lcaas_id + "-cwp-vulnerability-analyzer-svc.cwp.svc.cluster.local:8080" + ",http://xdr-st-" + tenant.lcaas_id + "-cwp-secret-analyzer-svc.cwp.svc.cluster.local:8080" + ",http://xdr-st-" + tenant.lcaas_id + "-cwp-trusted-image-analyzer-svc.cwp.svc.cluster.local:8080"'
  KMSCONF_BROKER_VM_KEY_NAME: tenant.lcaas_id
  LOGGINGSERVICE_LCAAS_TENANT: tenant.lcaas_id
  COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_LOCATION: globals.kms_keyring_region
  COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_NAME: "'master'"
  COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_RING: "'exec-hsm'"
  COMPLIANCE_AGENT_RULES_API_KMS_MT_KEY_VERSION: "'1'"
  COMPLIANCE_AGENT_RULES_API_KMS_MT_PROJECT_ID: '"xdr-distributions-" + region.multi_project_postfix + "-01"'
  COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_LOCATION: globals.kms_keyring_region
  COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_NAME: "'slave'"
  COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_RING: "'exec-hsm'"
  COMPLIANCE_AGENT_RULES_API_KMS_ST_KEY_VERSION: "'1'"
  AGENT_RULES_API_AGENT_DOWNLOADS_BUCKET_NAME: 'tenant.project_id + "-cwp-agent-downloads"'
  AGENT_DOWNLOADS_BUCKET_NAME: 'tenant.project_id + "-cwp-agent-downloads"'
  CORTEX_AGENT_CH_BASE_URL: '"https://ch-" + local._fqdn_suffix + ".traps.paloaltonetworks.com"'
  MALWARE_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-malware-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  MALWARE_DETECTION_REDIS_HOST: globals.env.REDIS_CWP_URI
  MESSAGE_BUS_PROJECT_ID: tenant.project_id
  MONGODB_REPLICASSET_NODES: '"mongodb://mongo-headless." + globals.cas_namespace + ".svc.cluster.local:27017"'
  POLICY_RULES_DELETION_ISSUE_CLOSING_PUBSUB_SUB: '"cwp-issue-closing-" + tenant.lcaas_id + "-sub"'
  POLICY_RULES_DELETION_ISSUE_CLOSING_PUBSUB_TOPIC: '"cwp-issue-closing-" + tenant.lcaas_id'
  PLATFORM_ASSET_EXPORT_BUCKET: 'tenant.project_id + "-dp-asset-export"'
  PLATFORM_BUS_BASE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-api." + globals.st_namespace + ".svc.cluster.local:4999"'
  PLATFORM_HEALTHMONITORING_TOPIC: '"projects/"+ tenant.project_id + "/topics/cloud-health-monitoring-" + tenant.lcaas_id'
  PLATFORM_HEALTHMONITORING_STATUS_TOPIC: '"cloud-health-monitoring-statuses-" + tenant.lcaas_id'
  PLATFORM_HEALTHMONITORING_OUTPOST_STATUS_TOPIC: '"cloud-onboarding-tasks-" + tenant.lcaas_id'
  PROJECT_ID: tenant.project_id
  RULES_MANAGEMENT_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-rules-management-svc:8080"'
  SBOM_DATA_AGGREGATION_CONTENT_BUCKET: 'region.viso_env == "dev" || region.is_fedramp ? "global-cwp-content-" + region.multi_project_postfix : "global-cwp-content"'
  CONTENT_KMS_KEY_PROJECT: '"xdr-kms-project-" + region.multi_project_postfix + "-01"'
  SCAN_LOGS_PUB_SUB_SEND_TOPIC_NAME: '"dp-scan-logs-" + tenant.lcaas_id'
  SCAN_LOGS_ENABLED: "true"
  SCAN_LOGS_BUCKET_NAME: 'tenant.lcaas_id + "-dp-scan-logs"'
  SCAN_RESULTS_BUCKET: 'tenant.project_id + "-cwp-scan-results"'
  SCAN_RESULTS_ENRICHER_REDIS_HOST: globals.env.REDIS_CWP_URI
  SCAN_RESULTS_ENRICHER_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-scan-results-enricher-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  SCAN_RESULTS_PUBSUB_TOPIC: '"cwp-scan-results-" + tenant.lcaas_id'
  SECRET_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-secret-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  SERVERLESS_REPORTED_ISSUES_TOPIC: '"agent-management-reported-issues-" + tenant.lcaas_id'
  SERVERLESS_REDIS_HOST: globals.env.REDIS_MAIN_HOST
  SERVERLESS_LAST_SEEN_UPDATE_TOPIC: '"log-processor-" + tenant.lcaas_id'
  SERVICE_LCAAS_TENANT: tenant.lcaas_id
  SP_ACCOUNT_CONTROLLER_ASSUME_ROLE_ARN: local.config.ADS_CONTROLLER_ASSUME_ROLE_ARN
  SP_ACCOUNT_CONTROLLER_KMS_KEY_ALIAS: local.config.ADS_CONTROLLER_KMS_KEY_ALIAS
  SP_ACCOUNT_CONTROLLER_TARGET_SERVICE_ACCOUNT: local.config.ADS_CONTROLLER_TARGET_SERVICE_ACCOUNT
  SP_BC_BUCKET: 'tenant.project_id + "-cwp-sp-bc-data"'
  SP_BC_CONTROL_SUB: '"projects/" + tenant.project_id + "/subscriptions/cwp-sp-bc-control-sub-" + tenant.lcaas_id'
  SP_BC_OBJECTS_SUB: '"projects/" + tenant.project_id + "/subscriptions/cwp-sp-bc-objects-sub-" + tenant.lcaas_id'
  SP_BC_OBJECTS_TOPIC: '"projects/" + tenant.project_id + "/topics/cwp-sp-bc-objects-" + tenant.lcaas_id'
  SP_BC_METRICS_INGESTOR_SUB: '"projects/" + tenant.project_id + "/subscriptions/cwp-sp-bc-control-metrics-" + tenant.lcaas_id + "-sub"'
  SP_BC_METRICS_INGESTOR_TOPIC: '"cwp-sp-bc-metrics-" + tenant.lcaas_id'
  SP_BC_CONTROL_TOPIC: '"projects/" + tenant.project_id + "/topics/cwp-sp-bc-control-" + tenant.lcaas_id'
  SP_WO_BC_RESULTS_TOPIC: '"projects/" + tenant.project_id + "/topics/cwp-scan-results-" + tenant.lcaas_id'
  SP_WO_LIFECYCLE_TOPIC: '"projects/" + tenant.project_id + "/topics/cwp-sp-lifecycle-events-" + tenant.lcaas_id'
  SP_WO_PAYLOAD_DISTRIBUTOR_API_ADDRESS: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-bc-distributor-svc:8080"'
  SP_WO_POSTGRES_DB_HOST: globals.env.PGHOST
  SP_WO_RESULTS_BUCKET_NAME: 'tenant.project_id + "-cwp-sp-bc-data"'
  SP_WO_RESULTS_SUBSCRIPTION_ID: '"projects/" + tenant.project_id + "/subscriptions/cwp-sp-bc-control-workload-orchestration-" + tenant.lcaas_id'
  SP_SNAPSHOT_DB_HOST: globals.env.PGHOST
  SP_SNAPSHOT_API_ADDRESS: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-snapshot-svc:8080"'
  SP_SNAPSHOT_KMS_KEY_ALIAS: 'region.viso_env + "-pan-managed-encrypt-snapshots-customers-generic"'
  TENANT_ID: tenant.lcaas_id
  TENANT_BATCH: 'region.is_fedramp ? nil : region.is_dev ? "P1" : tenant.upgrade_phase'
  TENANT_REGION: region.gcp_region
  TENANT_ENV: region.xdr_env
  UAI_AGGREGATED_ASSETS_VIEW: '"public_platform_" + tenant.lcaas_id + ".aggregated_assets_view"'
  UAI_OBSERVATION_TOPIC: '"dp-uai-asset-observations-" + tenant.lcaas_id'
  VE_CVE_FEED_PATH: 'region.viso_env == "dev" ? "gs://intelligence-builder-dev/feeds/33.02.116/CVEs.zip": "gs://intelligence-builder-prod/feeds/33.01.141/CVEs.zip"'
  VE_WINDOWS_FEED_PATH: 'region.viso_env == "dev" ? "gs://intelligence-builder-dev/feeds/33.02.116/Windows.zip": "gs://intelligence-builder-prod/feeds/33.01.141/Windows.zip"'
  VULNERABILITY_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-vulnerability-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  VULNERABILITY_EVALUATOR_BASE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-uvem-vip-api." + globals.st_namespace + ".svc.cluster.local:8080"'
  UVM_EVALUATOR_URL: '"http://xdr-st-" + tenant.lcaas_id + "-uvem-vxp-api." + globals.st_namespace + ".svc.cluster.local:8080"'
  UVM_NETSCAN_PROCESSOR_URL: '"http://xdr-st-" + tenant.lcaas_id + "-uvem-netscan-processor:8080"'
  UVEM_PROTOFINDING_EMIT_TOPIC: '"uvem-protofinding-emits-" + tenant.lcaas_id'
  VULNERABILITY_RE_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-vulnerability-re-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  CWP_REGISTRY_SCAN_POSTGRES_DB_HOST: globals.env.PGHOST
  CWP_REGISTRY_DISCOVERY_POSTGRES_DB_HOST: globals.env.PGHOST
  CWP_SERVERLESS_SCAN_POSTGRES_DB_HOST: globals.env.PGHOST
  CWP_REGISTRY_SCAN_SPEC_MANAGER_URL: '"http://xdr-st-"+ tenant.lcaas_id +"-cwp-scan-spec-manager-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  CWP_SERVERLESS_SCAN_SPEC_MANAGER_URL: '"http://xdr-st-"+ tenant.lcaas_id +"-cwp-scan-spec-manager-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  SBOM_ANALYZER_POSTGRES_DB_HOST: globals.env.PGHOST
  SBOM_ANALYZER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sbom-analyzer-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  AGENT_ANALYTICS_PROJECT_ID: '"xdr-agent-analytics-" + region.viso_env + "-01"'
  CORTEXCLI_BUCKET_NAME: '"panw-cortex-cli-" + region.multi_project_postfix'
  CWP_REGISTRY_DISCOVERY_REDIS_HOST: globals.env.REDIS_CWP_URI
  PC1_MIGRATION_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-pc1-migration-svc." + globals.cwp_namespace + ".svc.cluster.local:8080"'
  KAPI_DISTRIBUTION_URL: '"https://distributions" + local._distribution_postfix + ".traps.paloaltonetworks.com"'
  KAPI_REALTIME_AGENT_VERSION: 'region.is_fedramp ? "9.0.0.141088" : region.viso_env == "dev" ? "9.0.0.141079" : "9.0.0.141085"'
  KAPI_RULES_CALC_NOTIFICATION_SUBSCRIPTION: '"cwp-policy-rules-notification-sub-" + tenant.lcaas_id'
  CAS_BASE_URL: '"http://artifactory-management." + globals.cas_namespace + ".svc.cluster.local:80"'

stConfig:
  fullnameOverride: '"cwp-config"'
  namespaceOverride: globals.st_namespace
  data: local.config
