envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
restartPolicy: ""
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}

terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

replicas: 1

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: cwp
  team: core
podLabels:
  group: cwp
  team: core
podAnnotations:
  prometheus.io/port: "8080"
  prometheus.io/scrape: "true"

serviceAccount:
  create: true
  automountServiceAccountToken: false

livenessProbe:
  failureThreshold: 9
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

nodeSelector:
  xdr-pool: "wi-dynamic"

deployment:
  strategy:
    type: "RollingUpdate"

env:
  SERVICE_NAME: "CORE_ASSET_ANALYZER"

hpa:
  enabled: true
  minReplicas: 1
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 120

vsg:
  enabled: true
  pollingMinutes: 3
  project: ""
  oomScaling:
    cooldownMinutes: 5
    enabled: true
    memoryConstantIncrease: "1Gi"
    podRequestMemoryMax: "8Gi"
  verticalScaling: {}
  zeroScaling:
    downscaleThreshold: 0
    downscaleWindowMinutes: 60
    enabled: true
    upscaleThreshold: 1
    upscaleWindowMinutes: 60
