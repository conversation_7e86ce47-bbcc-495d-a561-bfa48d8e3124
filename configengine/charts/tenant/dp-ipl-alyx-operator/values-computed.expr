_component_name: "'dp-ipl-alyx-operator'"

namespace: "globals.dp_ipl_alyx_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: "'dp-ipl-alyx-operator'"

topology:
  followers:
    - kind: "'ServiceAccount'"
      name: "'xql-engine'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'dms-pod'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'pipeline'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'association-replication-job'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'api-pod'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'secdo-init'"
      namespace: globals.st_namespace
    - kind: "'ServiceAccount'"
      name: "'analytics-po'"
      namespace: globals.st_namespace

replicas: 'infra_ff.enable_alyx ? 1 : 0'
