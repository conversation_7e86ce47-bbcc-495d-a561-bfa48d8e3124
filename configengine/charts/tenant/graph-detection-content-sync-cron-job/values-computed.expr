_component_name: "'api'"
_app_name: "'graph-detection-content-sync'"

appName: 'globals.st_resource_prefix + "-" + local._app_name'
fullnameOverride: 'globals.st_resource_prefix + "-" + local._app_name'
namespaceOverride: "globals.st_namespace"

_images:
  component: "get(images.components, local._component_name)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

resources:
  requests:
    cpu: '"1"'
    memory: '"2Gi"'
  limits:
    cpu: '"1"'
    memory: '"2Gi"'

nodeSelector:
    xdr-pool: '"wi-dynamic"'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-feature-flags"'
      optional: false

env:
  MYSQLCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: globals.tenant_secrets
        key: "'mysql_password'"
        optional: false
  REDISCONF_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: globals.tenant_secrets
        key: "'redis_password'"
        optional: false
  ANALYTICSCONF_REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: globals.tenant_secrets
        key: "'analytics_redis_password'"
        optional: false

serviceAccount:
  name: "'dp-analytics-graph-de-sync'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
