envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
namespaceOverride: ""
fullnameOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}
resources: {}

podAnnotations:
  prometheus.io/port: "4982"
  prometheus.io/scrape: "true"

terminationGracePeriodSeconds: 30
automountServiceAccountToken: true

cronjob:
  annotations:
    prometheus.io/port: "4982"
    prometheus.io/scrape: "true"
  labels: {}
  backoffLimit: 0
  schedule: "0 * * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  ttlSecondsAfterFinished: 0
  parallelism: 1
  completions: 1
  suspend: true

restartPolicy: "Never"

deploymentLabels:
  group: dp
  team: analytics
podLabels:
  group: dp
  team: analytics

serviceAccount:
  create: true
  automountServiceAccountToken: false
  annotations: {}


### Copied from api-deployment

livenessProbe:
  failureThreshold: 40
  httpGet:
    path: "/ping/"
    port: 4999
    scheme: "HTTP"
  periodSeconds: 15

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/ping/"
    port: 4999
    scheme: "HTTP"


env: {}