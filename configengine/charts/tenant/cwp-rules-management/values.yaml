envFrom: []
image: {}
affinity: {}
tolerations: {}
extraVolumes: {}
extraVolumeMounts: {}
restartPolicy: ""
namespaceOverride: ""
priorityClassName: ""
topologySpreadConstraints: {}

terminationGracePeriodSeconds: 30
automountServiceAccountToken: false

deploymentAnnotations:
  reloader.stakater.com/auto: "true"
deploymentLabels:
  group: cwp
  team: core
podLabels:
  group: cwp
  team: core
podAnnotations:
  prometheus.io/port: "8080"
  prometheus.io/scrape: "true"

serviceAccount:
  create: true
  automountServiceAccountToken: false

service:
  annotations: {}

livenessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: "/healthz"
    port: 8080
    scheme: "HTTP"
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

startupProbe:
  failureThreshold: 3
  httpGet:
    path: /healthz
    port: 8080
    scheme: HTTP
  periodSeconds: 5
  successThreshold: 1
  timeoutSeconds: 2

hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: 90
          type: Utilization
      type: Resource
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 120
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 600
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Percent
          value: 10
      selectPolicy: Max
      stabilizationWindowSeconds: 120

vsg:
  enabled: true
  pollingMinutes: 5
  project: ""
  oomScaling:
    enabled: true
    cooldownMinutes: 5
    memoryConstantIncrease: "256Mi"
    podRequestMemoryMax: "4Gi"
  verticalScaling: {}
  zeroScaling: {}

nodeSelector:
  xdr-pool: "wi-dynamic"

deployment:
  strategy:
    type: "RollingUpdate"

env:
  POSTGRESQLCONF_USERNAME: "root"
