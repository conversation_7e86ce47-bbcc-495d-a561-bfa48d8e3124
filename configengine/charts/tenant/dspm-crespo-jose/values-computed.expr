_component_name: "'dspm-crespo-jose'"
_component_image: "'dspm-crespo-jose'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "1Gi" : "1Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "3"'
    memory: '!infra_ff.is_enable_prod_spec ? "4Gi" : "4Gi"'

env:
  ST_RESIDENT_ID: 'tenant.lcaas_id'
  AWS_DEFAULT_REGION: "'eu-central-1'"
  AWS_DIG_RESOURCES_TOPIC_ID: "'dummy'"
  AZURE_DIG_RESOURCES_TOPIC_ID: "'dummy'"
  CLASSI_DB_NAME: "'garbage-db'"
  CLASSI_DB_URL: "'garbage'"
  DB_NAME: "'dspm-crespo'"
  DIG_ENV: "'tmp'"
  GCP_DIG_RESOURCES_TOPIC_ID: "'tmp'"
  GENIE_INTERNAL_DOMAIN: '"dspm-oo-genie-svc." + local.namespaceOverride + ".svc.cluster.local"'
  JOBS_TOPIC_ID: "''"
  MIA_HERMES_BASE_URL: '"http://mia." + local.namespaceOverride + ".svc.cluster.local:8099"'
  OUTPOST_ORCHESTRATOR_INTERNAL_URL: '"http://dspm-oo-ready-job-evaluator-svc." + local.namespaceOverride + ".svc.cluster.local"'
  PAYLOAD_BUCKET: 'tenant.project_id + "-dspm_crespo_payload"'
  SIA_REGISTRY_REGION: "''"
  SERVICE_ACCOUNT: "'tmp'"
  SIS_INTERNAL_URL: "'dummy'"
  TASK_QUEUE_NAME: "'crespo_queue'"
  TEMPORAL_NAMESPACE: "'dspm-crespo'"
  TEMPORAL_TLS: "'false'"
  TEMPORAL_URL: 'globals.env.TEMPORAL_URL'
  UNDISCO_HANDLER_INTERNAL_URL: "''"
  WORKERS: "'2'"
  X_FILES_SKINNER_INTERNAL_URL: '"http://dspm-x-files-skinner-svc." + local.namespaceOverride + ".svc.cluster.local"'

  DB_URL:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'mongodb_connection_string'"
        optional: false

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false


initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
      - name: "'TEMPORAL_NAMESPACE'"
        value: '"dspm-crespo"'
