fullnameOverride: "'opentelemetry-collector-agentless'"

command:
  name: "'otelcol-contrib'"

image:
  repository: 'globals.gcr + "/golden-images/otel/opentelemetry-collector-contrib"'
  tag: 'region.is_fedramp ? "0.115.1-amd64" : "0.115.1-amd64"'
mode: "'deployment'"

resources:
  limits:
    cpu: 1
    memory: "'1Gi'"

serviceAccount:
  create: true
  name: "'otel-collector-agentless'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

config:
  exporters:
    debug:
      verbosity: "'detailed'"
    otlphttp:
      endpoint: '"http://monitoring-" + tenant.lcaas_id + "-prometheus." + globals.monitoring_namespace + ".svc.cluster.local:8080"'
      metrics_endpoint: '"http://monitoring-" + tenant.lcaas_id + "-prometheus." + globals.monitoring_namespace + ".svc.cluster.local:8080/api/v1/otlp/v1/metrics"'
      tls:
        insecure: true
  receivers:
    googlecloudpubsub:
      encoding: "'otlp_proto_metric'"
      project: tenant.project_id
      subscription: '"projects/" + tenant.project_id + "/subscriptions/cwp-sp-bc-metrics-" + tenant.lcaas_id + "-sub"'
  service:
    pipelines:
      metrics:
        exporters:
          - "'otlphttp'"
          - "'debug'"
        receivers:
          - "'googlecloudpubsub'"

vsg:
  enabled: true
  project: tenant.project_id
  zeroScaling:
    pubsubSubscriptions:
      - '"cwp-sp-bc-metrics-" + tenant.lcaas_id + "-sub"'
