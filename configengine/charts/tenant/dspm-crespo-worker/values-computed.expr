# values-computed.expr
_component_name: "'dspm-crespo-worker'"
_component_image: "'dspm-crespo-worker'"

fullnameOverride: 'local._component_name'
namespaceOverride: "globals.dspm_namespace"
cwpNamespaceOverride: "globals.cwp_namespace"

_shared_resources_aws_accounts:
  dev: 'tenant.creation_date > ************* ? "************" : "************"'
  prod-commercial-all: '"************"'
  prod-fr: '"************"'
  prod-gv: '"************"'

_images:
  component: "get(images.components, local._component_image)"
  family: "get(images.families, local._images.component.family)"

image:
  registry: "globals.gcr"
  tag: "local._images.component.tag ?? local._images.family.tag"
  repository: "local._images.component.repository ?? local._images.family.repository"

serviceAccount:
  name: local._component_name
  annotations:
    iam.gke.io/gcp-service-account: 'local._component_name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

resources:
  requests:
    cpu: '!infra_ff.is_enable_prod_spec ? "200m" : "200m"'
    memory: '!infra_ff.is_enable_prod_spec ? "2Gi" : "2Gi"'
  limits:
    cpu: '!infra_ff.is_enable_prod_spec ? "3" : "4"'
    memory: '!infra_ff.is_enable_prod_spec ? "6Gi" : "6Gi"'

env:
  ST_RESIDENT_ID: 'tenant.lcaas_id'
  AWS_CA_BUNDLE: "'/etc/certs/egress.crt'"
  EGRESSPROXY_URL: '"10.181.0.100:" + globals.egress_proxy_port'
  EGRESSPROXY_CA_PATH: "'/etc/certs/egress.crt'"
  AWS_SHARED_RESOURCES_ACCOUNT_ID: 'get(local._shared_resources_aws_accounts, region.viso_env) ?? (local._shared_resources_aws_accounts["prod-commercial-all"])'
  KMS_KEY_ALIAS: 'region.viso_env + "-pan-managed-encrypt-snapshots-customers-generic"'
  AWS_DEFAULT_REGION: "'eu-central-1'"
  CLASSI_DB_NAME: "'garbage'"
  DB_NAME: "'dspm-crespo'"
  DIG_ENV: "'tmp'"
  SIA_REGISTRY_REGION: "''"
  SERVICE_ACCOUNT: "'tmp'"
  SIS_INTERNAL_URL: "'dummy'"
  TASK_QUEUE_NAME: "'crespo_queue'"
  TEMPORAL_NAMESPACE: "'dspm-crespo'"
  TEMPORAL_TLS: "'false'"
  TEMPORAL_URL: 'globals.env.TEMPORAL_URL'
  UNDISCO_HANDLER_INTERNAL_URL: "''"
  WORKERS: "'25'"
  X_FILES_SKINNER_INTERNAL_URL: '"http://dspm-x-files-skinner-svc." + local.namespaceOverride + ".svc.cluster.local"'
  MIA_HERMES_BASE_URL: "'tmp'"
  GENIE_INTERNAL_DOMAIN: '"dspm-oo-genie-svc." + local.namespaceOverride + ".svc.cluster.local"'
  WORKLOAD_ORCHESTRATION_BASE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-workload-orchestration-svc." + local.cwpNamespaceOverride + ".svc.cluster.local:8080"'
  DACS_INTERNAL_URL: '"http://dspm-dacs-dashboard-svc." + local.namespaceOverride + ".svc.cluster.local"'
  JOBS_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/" + "outpost_orchestrator_job_registerer-" + tenant.lcaas_id'
  OUTPOST_ORCHESTRATOR_INTERNAL_URL: '"http://dspm-oo-ready-job-evaluator-svc." + local.namespaceOverride + ".svc.cluster.local"'
  CORTEX_BASE_PATH: 'globals.env.CORTEX_PLATFORM_URI'
  PLAYMAKER_RESOURCE_UPDATES_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/" + "playmaker_resource_updates-" + tenant.lcaas_id'
  STRUCTURED_METADATA_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/" + "dspm-column-listing-data-" + tenant.lcaas_id'
  SP_SNAPSHOT_API_ADDRESS: '"http://xdr-st-" + tenant.lcaas_id + "-cwp-sp-snapshot-svc." + local.cwpNamespaceOverride + ".svc.cluster.local:8080"'
  PAYLOAD_BUCKET: 'tenant.project_id + "-dspm_crespo_payload"'
  HEALTH_LOGS_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/cloud-health-monitoring-" + tenant.lcaas_id'
  DB_URL:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'mongodb_connection_string'"
        optional: false
  CLASSI_DB_URL:
    valueFrom:
      secretKeyRef:
        name: 'tenant.lcaas_id + "-dspm-d9-secrets"'
        key: "'mongodb_connection_string'"
        optional: false
  DSPM_DATAFLOW_BUCKET: 'tenant.project_id + "-dspm-data-flow"'
  DSPM_DISCOVERY_DATA_TOPIC_ID: '"projects/" + tenant.project_id + "/topics/dspm-discovery-data-" + tenant.lcaas_id'
  ONPREM_NOTIFICATION_BUCKET_PREFIX: "'notification/onprem/'"

initContainers:
  initTemporalNamespace:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
  initTemporalSearchAttribute:
    image:
      registry: globals.gcr
      repository: "'golden-images/temporalio/admin-tools'"
      tag: 'region.is_fedramp ? "1.29" : "********"'
    env:
      - name: "'TEMPORAL_ADDRESS'"
        value: 'globals.env.TEMPORAL_URL'
      - name: "'TEMPORAL_NAMESPACE'"
        value: '"dspm-crespo"'

envFrom:
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm"'
      optional: false
  - configMapRef:
      name: 'tenant.lcaas_id + "-configmap-dspm-feature-flags"'
      optional: false

hpa:
  maxReplicas: 'region.viso_env == "dev" ? "4" : "10"'
