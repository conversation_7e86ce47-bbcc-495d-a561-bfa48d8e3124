namespaceOverride: "globals.st_namespace"

_es_memory_required_gb: |-
  infra_ff.enable_elasticsearch_standalone ? 48 : 
  license.is_xsoar ? (infra_ff.is_enable_prod_spec ? 24 : 6) :
  globals.is_xsoar_legacy_spec ? 12 : 10

_cpu: |-
  infra_ff.enable_elasticsearch_standalone ? "12" :
  !infra_ff.is_enable_prod_spec ? "1" : globals.is_xsoar_legacy_spec ? "4" : "2"

cluster:
  auth:
    fileRealm:
      - secretName: '"es-elastic-user"'
  image:
    registry: "globals.gcr"
    tag: 'region.is_fedramp ? "8.19.8" : "8.18.6"'
  resources:
    requests:
      cpu: local._cpu
      memory: 'string(local._es_memory_required_gb) + "Gi"'
    limits:
      cpu: local._cpu
      memory: 'string(local._es_memory_required_gb) + "Gi"'

operator:
  image:
    registry: "globals.gcr"

is_prod: "infra_ff.is_enable_prod_spec"
is_xsoar: "license.is_xsoar"
is_xsiam: "globals.is_xsiam"

_es_java_mem_required_gb: "floor((local._es_memory_required_gb) / 2)"
es_java_opts: '"-Xms" + string(local._es_java_mem_required_gb) + "g -Xmx" + string(local._es_java_mem_required_gb) + "g"'

node_selector_value: 'infra_ff.enable_elasticsearch_standalone ? "wi-elasticsearch" : "wi-static"'

serviceAccount:
  name: "'elastic-search'"
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'
