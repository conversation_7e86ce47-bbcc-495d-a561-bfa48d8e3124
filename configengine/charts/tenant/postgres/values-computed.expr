_component_name: "'postgres'"

_tier: 'region.viso_env == "prod-pr" && tenant.authcode != "internal" ? "t4" : (region.viso_env == "dev" ? "t1" : "t2")'

_default_scaling_by_tier:
  postgres_request_cpu:
    t1: '2'
    t2: '4'
    t3: '8'
    t4: '16'
  postgres_request_memory:
    t1: '"8Gi"'
    t2: '"16Gi"'
    t3: '"32Gi"'
    t4: '"64Gi"'
  data_storage:
    t1: '"5Gi"'
    t2: '"75Gi"'
    t3: '"125Gi"'
    t4: '"200Gi"'
  postgres_max_connections:
    t1: '250'
    t2: '300'
    t3: '400'
    t4: '500'
  postgres_pod_shared_memory:
    t1: '"160Mi"'
    t2: '"228Mi"'
    t3: '"288Mi"'
    t4: '"416Mi"'
  postgres_shared_buffers:
    t1: '"128MB"'
    t2: '"196MB"'
    t3: '"256MB"'
    t4: '"384MB"'

namespaceOverride: globals.st_namespace
fullnameOverride: 'globals.st_resource_prefix + "-" + local._component_name'

image:
  registry: "globals.gcr"
  tag: 'region.is_fedramp ? "16.11" : "16.2"'
  repository: '"golden-images/postgres"'

env:
  POSTGRES_PASSWORD:
    valueFrom:
      secretKeyRef:
        key: "'postgres_password'"
        name: "globals.tenant_secrets"
        optional: false

serviceAccount:
  name: '"postgres"'
  annotations:
    iam.gke.io/gcp-service-account: 'local.serviceAccount.name + "@" + tenant.project_id + ".iam.gserviceaccount.com"'

persistence:
  size: 'get(local._default_scaling_by_tier.data_storage, local._tier)'
  storageClassName: 'tenant.is_metro_tenant ? "ssd-csi-storage" : "ssd-storage"'

resources:
  limits:
    cpu: 'get(local._default_scaling_by_tier.postgres_request_cpu, local._tier)'
    memory: 'get(local._default_scaling_by_tier.postgres_request_memory, local._tier)'
  requests:
    cpu: 'get(local._default_scaling_by_tier.postgres_request_cpu, local._tier)'
    memory: 'get(local._default_scaling_by_tier.postgres_request_memory, local._tier)'

extraVolumes:
  - configMap:
      name: 'tenant.lcaas_id + "-configmap-postgres"'
      defaultMode: 420
      optional: false
    name: '"postgres-config"'

configmap:
  name: 'tenant.lcaas_id + "-configmap-postgres"'

postgres:
  podSharedMemory: 'get(local._default_scaling_by_tier.postgres_pod_shared_memory, local._tier)'
  parameters:
    max_connections: 'get(local._default_scaling_by_tier.postgres_max_connections, local._tier)'
    shared_buffers: 'get(local._default_scaling_by_tier.postgres_shared_buffers, local._tier)'
    max_wal_size: '"1GB"'
    min_wal_size: '"80MB"'
