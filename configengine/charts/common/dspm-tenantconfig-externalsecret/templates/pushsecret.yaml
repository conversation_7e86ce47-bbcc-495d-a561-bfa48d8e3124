apiVersion: external-secrets.io/v1alpha1
kind: PushSecret
metadata:
  labels:
    {{- include "common.sre.panw.labels" . | nindent 4 }}
  name: {{ include "chart.fullname" . }}
spec:
  refreshInterval: 10s
  updatePolicy: Replace
  deletionPolicy: Delete
  secretStoreRefs: 
    - name: {{ .Values.secretStoreName }}
      kind: ClusterSecretStore
  selector:
    secret:
      name: {{ include "chart.fullname" . }}
  data:
    - match:
        secretKey: {{ .Values.lcaasId }}-{{ .Values.targetSecretKey }}.yaml
        remoteRef:
          remoteKey: {{ .Values.pushSecretRemoteKey }}
          property: {{ .Values.lcaasId }}-{{ .Values.targetSecretKey }}.yaml