apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ include "chart.fullname" . }}
spec:
  refreshInterval: 10s
  secretStoreRef:
    name: xdr-st-{{ .Values.lcaasId }}
    kind: ClusterSecretStore
  target:
    name: {{ include "chart.fullname" . }}
    creationPolicy: Owner
    template:
      engineVersion: v2
      type: Opaque
      data:
        {{ .Values.lcaasId }}-{{ .Values.targetSecretKey }}.yaml: |-
        {{- $config := deepCopy .Values.config }}
        {{- range .Values.secrets }}
        {{- $path := .configPath | trimAll "\"" | splitList "." }}
        {{- $secretKey := .secretKey | trimAll "\"" }}
        {{- $val := printf "{{ .%s }}" $secretKey }}
            {{- $current := $config }}
            {{- range initial $path }}
              {{- $current = index $current . }}
            {{- end }}
            {{- $_ := set $current (last $path) $val }}
          {{- end }}
          {{- $config | toYaml | nindent 10 }}
  data:
    {{- range .Values.secrets }}
    {{- $secretKey := .secretKey | trimAll "\"" }}
                             - secretKey: {{ $secretKey }}
                               remoteRef:
                                 key: {{ .remoteRef.key }}
                                 property: {{ .remoteRef.property }}
    {{- end }}