apiVersion: v1
kind: Namespace
metadata:
  name: "{{ .globals.st_namespace }}"
---
apiVersion: v1
kind: Namespace
metadata:
  name: "{{ .globals.cortex_cts_namespace }}"
---
apiVersion: v1
kind: Namespace
metadata:
  name: "{{ .globals.cas_namespace }}"
---
apiVersion: v1
kind: Namespace
metadata:
  name: "{{ .globals.cwp_namespace }}"
---
apiVersion: v1
kind: Namespace
metadata:
  name: "{{ .globals.apilot_shardscape_namespace }}"
---
apiVersion: v1
kind: Secret
metadata:
  name: xsoar-secrets
  namespace: "{{ .globals.st_namespace }}"
stringData:
  values.yaml: |
    demistoConf: 
      hashedMonitoringKey: hashedMonitoringKey
      hashedWildfireKey: hashedWildfireKey
      hashedXdrHttpToken: hashedXdrHttpToken
      migrationToken: migrationToken
---
apiVersion: v1
kind: Secret
metadata:
  name: common-configmaps-secrets
  namespace: "{{ .globals.st_namespace }}"
stringData:
  AGENTCONF_ENCRYPTION_KEY: AGENTCONF_ENCRYPTION_KEY
  COMMUNICATION_SLACK_TOKEN: COMMUNICATION_SLACK_TOKEN
  GONZO_SLACK_TOKEN: GONZO_SLACK_TOKEN
  WILDFIRE_APIKEY: WILDFIRE_APIKEY
  XSOARCONF_AUTH_KEY: XSOARCONF_AUTH_KEY
  XSOARCONF_HTTP_COLLECTION: XSOARCONF_HTTP_COLLECTION
  XSOARMIGRATIONCONF_SOURCE_TENANT_ACCESS_SA: XSOARMIGRATIONCONF_SOURCE_TENANT_ACCESS_SA
  XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY: XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY
  values.yaml: |
    config: 
      AGENTCONF_ENCRYPTION_KEY: AGENTCONF_ENCRYPTION_KEY
      COMMUNICATION_SLACK_TOKEN: COMMUNICATION_SLACK_TOKEN
      GONZO_SLACK_TOKEN: GONZO_SLACK_TOKEN
      WILDFIRE_APIKEY: WILDFIRE_APIKEY
      XSOARCONF_AUTH_KEY: XSOARCONF_AUTH_KEY
      XSOARCONF_HTTP_COLLECTION: XSOARCONF_HTTP_COLLECTION
      XSOARMIGRATIONCONF_SOURCE_TENANT_ACCESS_SA: XSOARMIGRATIONCONF_SOURCE_TENANT_ACCESS_SA
      XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY: XSOARMIGRATIONCONF_SOURCE_TENANT_API_KEY
---
apiVersion: v1
kind: Secret
metadata:
  name: singlestore-secrets
  namespace: "{{ .globals.st_namespace }}"
stringData:
  values.yaml: |
    adminHashedPassword: adminHashedPassword
    license: license
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: common-confimaps-external-ips
  namespace: "{{ .globals.st_namespace }}"
data:
  values.yaml: |
    config: 
      APPLICATIONHUB_ENGINE_PROJECT_EXTERNAL_IPS: APPLICATIONHUB_ENGINE_PROJECT_EXTERNAL_IPS
      XSOARMIGRATIONCONF_IP_TO_WHITELIST_1: XSOARMIGRATIONCONF_IP_TO_WHITELIST_1
      XSOARMIGRATIONCONF_IP_TO_WHITELIST_2: XSOARMIGRATIONCONF_IP_TO_WHITELIST_2
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: common-configmaps-terraform-exports
  namespace: "{{ .globals.st_namespace }}"
data:
  values.yaml: |
    config: 
      CLOUDONBOARDING_AZURE_DSPM_O365_SA_UNIQUE_ID: CLOUDONBOARDING_AZURE_DSPM_O365_SA_UNIQUE_ID
      CLOUDONBOARDING_AZURE_DSPM_O365_SA_EMAIL: CLOUDONBOARDING_AZURE_DSPM_O365_SA_EMAIL
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cts-configmaps-terraform-exports
  namespace: "{{ .globals.cortex_cts_namespace }}"
data:
  values.yaml: |
    config: 
      CTSCONF_CALLERS_LIST: CTSCONF_CALLERS_LIST
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cwp-configmaps-terraform-exports
  namespace: "{{ .globals.cwp_namespace }}"
data:
  values.yaml: |
    config: 
      CWP_REGISTRY_SCANNER_VERSION: CWP_REGISTRY_SCANNER_VERSION
      SP_AUTH_KEY_NAME: SP_AUTH_KEY_NAME
---
apiVersion: v1
kind: Secret
metadata:
  name: argo-workflows-postgress
  namespace: "{{ .globals.cas_namespace }}"
stringData:
  password: password
  username: username
---
apiVersion: v1
kind: Secret
metadata:
  name: '{{ printf "%s-secrets" .tenant.lcaas_id }}'
  namespace: "{{ .globals.st_namespace }}"
stringData:
  neo4j_password: neo4j_password
  mongodb_uri_cwp: mongodb_uri
---
apiVersion: v1
kind: Secret
metadata:
  name: '{{ printf "%s-cns-secrets" .tenant.lcaas_id }}'
  namespace: "{{ .globals.cwp_namespace }}"
stringData:
  cns_neo4j_password: neo4j_password
---
apiVersion: v1
kind: Secret
metadata:
  name: '{{ printf "%s-neo4j-cluster-password" .tenant.lcaas_id }}'
  namespace: "{{ .globals.st_namespace }}"
stringData:
  NEO4J_AUTH: neo4j/test-password
---
apiVersion: v1
kind: Secret
metadata:
  name: 'apilot-secrets'
  namespace: "{{ .globals.apilot_shardscape_namespace }}"
stringData:
  neo4j_password: neo4j/test-password
