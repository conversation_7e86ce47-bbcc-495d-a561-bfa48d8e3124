package models

import (
	"errors"
	"fmt"

	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/exprparser"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/types"
	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/utils"
)

// Required to implement into CR struct
// +kubebuilder:object:generate=true
// InfraFeatureFlags represents the configuration for infrastructure feature flags
type InfraFeatureFlags struct {
	EnableAssuredWorkloads                  bool `json:"enable_assured_workloads"`
	EnableEgress                            bool `json:"enable_egress"`
	IsEnableEgress                          bool `json:"is_enable_egress"` // computed field
	EnableExposureManagement                bool `json:"enable_exposure_management"`
	IsEnableExposureManagement              bool `json:"is_enable_exposure_management"` // computed field
	EnableXCloud                            bool `json:"enable_xcloud"`
	IsEnableXCloud                          bool `json:"is_enable_xcloud"` // computed field
	EnableRedisSplit                        bool `json:"enable_redis_split"`
	EnableAPISplit                          bool `json:"enable_api_split"`
	IsEnableAPISplit                        bool `json:"is_enable_api_split"` // computed field
	EnableRedisDSS                          bool `json:"enable_redis_dss"`
	EnableRedisAgentAPI                     bool `json:"enable_redis_agent_api"`
	EnableCloudOnboardingManagerHealth      bool `json:"enable_cloud_onboarding_manager_health"`
	EnableRedisFetcher                      bool `json:"enable_redis_fetcher"`
	EnableBYOK                              bool `json:"enable_byok"`
	EnableTwistlock                         bool `json:"enable_twistlock"`
	EnableRocksdbStandalone                 bool `json:"enable_rocksdb_standalone"`
	IsEnableRocksdbStandalone               bool `json:"is_enable_rocksdb_standalone"`
	EnableRocksdbBlueGreenMode              bool `json:"enable_rocksdb_blue_green_mode"`
	EnableRocksdbClusterMode                bool `json:"enable_rocksdb_cluster_mode"`
	IsEnableRocksdbClusterMode              bool `json:"is_enable_rocksdb_cluster_mode"` // computed field
	EnableRocksdbWriter                     bool `json:"enable_rocksdb_writer"`
	IsEnableRocksdbWriter                   bool `json:"is_enable_rocksdb_writer"` // computed field
	EnableScyllaIPLen                       bool `json:"enable_scylla_iplen"`
	EnableMSFTS1                            bool `json:"enable_msft_s1"`
	EnableApplicationHub                    bool `json:"enable_application_hub"`
	IsEnableApplicationHub                  bool `json:"is_enable_application_hub"` // computed field
	EnableMegatronXdr                       bool `json:"enable_megatron_xdr"`
	EnableMegatronXsoar                     bool `json:"enable_megatron_xsoar"`
	EnableGMP                               bool `json:"enable_gmp"`
	EnableMysqlProdSpec                     bool `json:"enable_mysql_prod_spec"`
	EnableProdSpec                          bool `json:"enable_prod_spec"`
	IsEnableProdSpec                        bool `json:"is_enable_prod_spec"` // computed field
	EnableMysqlSlave                        bool `json:"enable_mysql_slave"`
	EnableMysqlUvem                         bool `json:"enable_mysql_uvem"`
	EnableBigTenant                         bool `json:"enable_big_tenant"`
	EnableXqlCdlEngine                      bool `json:"enable_xql_cdl_engine"`
	EnableXqlFdrEngine                      bool `json:"enable_xql_fdr_engine"`
	EnableXcloudStandaloneDeployment        bool `json:"enable_xcloud_standalone_deployment"`
	IsEnableXcloudStandaloneDeployment      bool `json:"is_enable_xcloud_standalone_deployment"` // computed field
	EnableCortexPlatform                    bool `json:"enable_cortex_platform"`
	EnablePrimaryPlaybookMirroring          bool `json:"enable_primary_playbook_mirroring"`
	EnableSecondaryPlaybookMirroring        bool `json:"enable_secondary_playbook_mirroring"`
	IsEnablePlaybookMirroring               bool `json:"is_enable_playbook_mirroring"`
	EnableGonzoDragonfly                    bool `json:"enable_gonzo_dragonfly"`
	EnableCronus                            bool `json:"enable_cronus"`
	EnableAlyx                              bool `json:"enable_alyx"`
	IsEnableAlyx                            bool `json:"is_enable_alyx"`
	EnableAlyxStandalone                    bool `json:"enable_alyx_standalone"`
	IsEnableAlyxStandalone                  bool `json:"is_enable_alyx_standalone"` // computed field
	CronusDeployIdleResources               bool `json:"cronus_deploy_idle_resources"`
	EnableNewScylla                         bool `json:"enable_new_scylla"`
	EnableScyllaMigration                   bool `json:"scylla_migration"`
	EnableAdditionalDetectionEngineNodepool bool `json:"enable_additional_detection_engine_nodepool"`
	EnableElasticsearchStandalone           bool `json:"enable_elasticsearch_standalone"`
	EnableXSoarWorkers                      bool `json:"enable_xsoar_workers"`
	IsEnableXSoarWorkers                    bool `json:"is_enable_xsoar_workers"` // computed field
	EnableXSoarAPI                          bool `json:"enable_xsoar_api"`
	IsEnableXSoarAPI                        bool `json:"is_enable_xsoar_api"` // computed field
	EnableCortexMCP                         bool `json:"enable_cortex_mcp"`
	EnableAllOutposts                       bool `json:"enable_all_outposts"`
	EnableAwsOutpost                        bool `json:"enable_aws_outpost"`
	EnableAzureOutpost                      bool `json:"enable_azure_outpost"`
	EnableGcpOutpost                        bool `json:"enable_gcp_outpost"`
	EnableOciOutpost                        bool `json:"enable_oci_outpost"`
	IsEnableAllOutposts                     bool `json:"is_enable_all_outposts"`  // computed field
	IsEnableAwsOutpost                      bool `json:"is_enable_aws_outpost"`   // computed field
	IsEnableAzureOutpost                    bool `json:"is_enable_azure_outpost"` // computed field
	IsEnableGcpOutpost                      bool `json:"is_enable_gcp_outpost"`   // computed field
	IsEnableOciOutpost                      bool `json:"is_enable_oci_outpost"`   // computed field
	EnableCACollectionDragonfly             bool `json:"enable_ca_collection_dragonfly"`
	IsEnableCACollectionDragonfly           bool `json:"is_enable_ca_collection_dragonfly"`
	DisableHealthMonitor                    bool `json:"disable_health_monitor"`
	EnableEmailArtifactsRelay               bool `json:"enable_email_artifacts_relay"`
	EnablePayingMonitoring                  bool `json:"enable_paying_monitoring"`
	EnableBiggerMemoryEngine                bool `json:"enable_bigger_memory_engine"`
	EnableOtelCollector                     bool `json:"enable_otel_collector"`
	EnableThanosRemoteWrite                 bool `json:"enable_thanos_remote_write"`
	EnableEgressProxyPlatform               bool `json:"enable_egress_proxy_platform"`
	EnableScyllaStandalone                  bool `json:"enable_scylla_standalone"`
	IsEnableScyllaStandalone                bool `json:"is_enable_scylla_standalone"`
	EnablePipeline                          bool `json:"enable_pipeline"`
	EnableScylla                            bool `json:"enable_scylla"`
	EnableXsoarSharedComponents             bool `json:"enable_xsoar_shared_components"`
	EnableReplicator                        bool `json:"enable_replicator"`
	EnableXsoarMigrationNode                bool `json:"enable_xsoar_migration_node"`
	EnableRegionalKubernetes                bool `json:"enable_regional_kubernetes"`
	EnableMultiZonedNodepools               bool `json:"enable_multi_zoned_nodepools"`
	IsEnableMultiZonedNodepools             bool `json:"is_enable_multi_zoned_nodepools"` // computed field
	EnableArgoEnchanced                     bool `json:"enable_argo_enhanced"`
	EnableMaxPodsPerNode                    bool `json:"enable_max_pods_per_node"`
	EnableAgentixHub                        bool `json:"enable_agentix_hub"`
	IsEnableAgentixHub                      bool `json:"is_enable_agentix_hub"`
	EnableK8sBetaApis                       bool `json:"enable_k8s_beta_apis"`
	EnableRedisPlatform                     bool `json:"enable_redis_platform"`
	EnableSpanner                           bool `json:"enable_spanner"`
	AssetPipelineIsSpannerMigration         bool `json:"asset_pipeline_is_spanner_migration"`
	EnableApilot                            bool `json:"enable_apilot"`
	EnableAlyxMigration                     bool `json:"enable_alyx_migration"`
	EnableScaleDownBeforeAlyxMigration      bool `json:"enable_scale_down_before_alyx_migration"`
	EnableMigrateAlyxDataToScylla           bool `json:"enable_migrate_alyx_data_to_scylla"`
	EnableNeo4jCluster                      bool `json:"enable_neo4j_cluster"`
	EnableAwsOutpostAllRegions              bool `json:"enable_aws_outpost_all_regions"`
	EnableAzureOutpostAllRegions            bool `json:"enable_azure_outpost_all_regions"`
	EnableGcpOutpostAllRegions              bool `json:"enable_gcp_outpost_all_regions"`
	EnableOciOutpostAllRegions              bool `json:"enable_oci_outpost_all_regions"`
}

func newInfraFeatureFlagsConfig() *infraFeatureFlags {
	iff := &infraFeatureFlags{
		ModelBase:         NewModelBase(),
		InfraFeatureFlags: &InfraFeatureFlags{},
	}
	iff.setAttrs()
	return iff
}

type infraFeatureFlags struct {
	*ModelBase
	*InfraFeatureFlags
}

func (c *infraFeatureFlags) Dependencies() []Configurable {
	return []Configurable{
		&license{},
		&region{},
		&tenant{},
	}
}

func (c *infraFeatureFlags) setAttrs() {
	c.ccFilename = c.Name() + "_config.yaml"
	c.cfFilename = c.Name() + "_config.expr"
}

func (c *infraFeatureFlags) Name() string {
	return "infra_ff"
}

//nolint:revive // no need, we need it as its required in the interface
func (c *infraFeatureFlags) PreLoad(customerConfig *CustomerConfig) error {
	return nil
}

func (c *infraFeatureFlags) LoadComputedFields(b []byte, data types.CustomerData) error {
	// Load the YAML into an OrderedMap
	cf, err := utils.LoadYAMLIntoOrderedMap(b)
	if err != nil {
		return fmt.Errorf("failed to unmarshal YAML: %w", err)
	}

	evalData, err := exprparser.ProcessMapForExpr(cf, data, false)
	if err != nil {
		return err
	}
	c.SetCf(evalData)

	return nil
}

func NewInfraFlagsConfigFromTenantVariables(tenantVariables map[string]interface{}) (*InfraFeatureFlags, error) {
	tenantOverridesData, err := utils.GetKeyFromMap(tenantVariables, "overrides")
	if err != nil {
		return nil, err
	}

	region, ok := tenantVariables["viso_env"].(string)
	if !ok {
		return nil, errors.New("could not find \"viso_env\" in tenant variables")
	}

	return &InfraFeatureFlags{
		// default infra feature flags for new tenant
		EnableEgress:                            tenantVariables["egress_enabled"].(bool),
		EnableAssuredWorkloads:                  Lookup(tenantVariables, "enable_assured_workloads", false),
		EnableBYOK:                              tenantVariables["enable_byok"].(bool),
		EnableXCloud:                            tenantVariables["enable_xcloud"].(bool),
		EnableApplicationHub:                    Lookup(tenantOverridesData, "enable_application_hub", false),
		EnableRedisSplit:                        Lookup(tenantOverridesData, "redis_split", false),
		EnableRedisDSS:                          Lookup(tenantOverridesData, "enable_dss_redis", false),
		EnableRedisAgentAPI:                     Lookup(tenantOverridesData, "enable_redis_agent_api", false),
		EnableCloudOnboardingManagerHealth:      Lookup(tenantOverridesData, "enable_cloud_onboarding_manager_health", false),
		EnableRedisFetcher:                      Lookup(tenantOverridesData, "enable_fetcher_redis", false),
		EnableRocksdbStandalone:                 Lookup(tenantOverridesData, "rocksdb_standalone", false),
		EnableScyllaStandalone:                  Lookup(tenantOverridesData, "scylla_standalone", false),
		EnableRocksdbBlueGreenMode:              Lookup(tenantOverridesData, "rocksdb_blue_green_mode", false),
		EnableRocksdbClusterMode:                Lookup(tenantOverridesData, "rocksdb_cluster_mode", false),
		EnableScyllaIPLen:                       Lookup(tenantOverridesData, "enable_scylla_iplen", false),
		EnableRocksdbWriter:                     Lookup(tenantOverridesData, "enable_rocksdb_writer", false),
		EnableMegatronXdr:                       Lookup(tenantOverridesData, "megatron_tenant", false) || Lookup(tenantOverridesData, "megatron_xdr", false),
		EnableMegatronXsoar:                     Lookup(tenantOverridesData, "megatron_xsoar", false),
		EnableTwistlock:                         region == "prod-fr",
		EnableProdSpec:                          Lookup(tenantOverridesData, "prod_spec", false),
		EnableBigTenant:                         Lookup(tenantOverridesData, "big_tenant", false),
		EnableMysqlSlave:                        Lookup(tenantOverridesData, "mysql_slave", false),
		EnableMysqlUvem:                         Lookup(tenantOverridesData, "enable_mysql_uvem", false),
		EnableMysqlProdSpec:                     Lookup(tenantOverridesData, "mysql_prod_spec_override", false) || region != "dev",
		EnableXqlCdlEngine:                      Lookup(tenantOverridesData, "enable_xql_cdl_engine", false),
		EnableXqlFdrEngine:                      Lookup(tenantOverridesData, "enable_xql_fdr_engine", false),
		EnableXcloudStandaloneDeployment:        Lookup(tenantOverridesData, "enable_xcloud_standalone_deployment", false),
		EnableCortexPlatform:                    Lookup(tenantOverridesData, "enable_cortex_platform", false),
		EnablePrimaryPlaybookMirroring:          Lookup(tenantOverridesData, "enable_primary_playbook_mirroring", false),
		EnableSecondaryPlaybookMirroring:        Lookup(tenantOverridesData, "enable_secondary_playbook_mirroring", false),
		EnableGonzoDragonfly:                    Lookup(tenantOverridesData, "enable_gonzo_dragonfly", false),
		EnableCACollectionDragonfly:             Lookup(tenantOverridesData, "enable_ca_collection_dragonfly", false),
		EnableAPISplit:                          Lookup(tenantOverridesData, "api_split", false),
		EnableGMP:                               Lookup(tenantOverridesData, "enable_gmp", false),
		EnableCronus:                            Lookup(tenantOverridesData, "enable_cronus", false),
		CronusDeployIdleResources:               Lookup(tenantOverridesData, "cronus_deploy_idle_resources", false),
		EnableNewScylla:                         Lookup(tenantOverridesData, "new_scylla", true),
		EnableScyllaMigration:                   Lookup(tenantOverridesData, "scylla_migration", false),
		EnableAdditionalDetectionEngineNodepool: Lookup(tenantOverridesData, "additional_detection_engine_nodepool", false),
		EnableElasticsearchStandalone:           Lookup(tenantOverridesData, "elasticsearch_standalone", false),
		EnableXSoarWorkers:                      Lookup(tenantOverridesData, "enable_xsoar_workers", false) || Lookup(tenantOverridesData, "enable_cortex_platform", false),
		EnableCortexMCP:                         Lookup(tenantOverridesData, "enable_cortex_mcp", false),
		EnableAllOutposts:                       Lookup(tenantOverridesData, "enable_all_outposts", false),
		EnableAwsOutpost:                        Lookup(tenantOverridesData, "enable_aws_outpost", false),
		EnableGcpOutpost:                        Lookup(tenantOverridesData, "enable_gcp_outpost", false),
		EnableAzureOutpost:                      Lookup(tenantOverridesData, "enable_azure_outpost", false),
		EnableOciOutpost:                        Lookup(tenantOverridesData, "enable_oci_outpost", false),
		DisableHealthMonitor:                    Lookup(tenantOverridesData, "disable_health_monitor", false),
		EnableEmailArtifactsRelay:               Lookup(tenantOverridesData, "enable_email_artifacts_relay", false) || Lookup(tenantVariables, "enable_email_artifacts_relay", false),
		EnablePayingMonitoring:                  Lookup(tenantOverridesData, "tenant_type", "") == string(PAYING),
		EnableBiggerMemoryEngine:                Lookup(tenantOverridesData, "bigger_memory_engine", false),
		EnableOtelCollector:                     Lookup(tenantOverridesData, "enable_otel_collector", false),
		EnableThanosRemoteWrite:                 Lookup(tenantOverridesData, "enable_thanos_remote_write", false),
		EnableEgressProxyPlatform:               Lookup(tenantOverridesData, "enable_egress_proxy_platform", false),
		EnableAlyxStandalone:                    Lookup(tenantOverridesData, "enable_alyx_standalone", false),
		EnableXsoarMigrationNode:                Lookup(tenantOverridesData, "enable_xsoar_migration_node", false),
		EnableRegionalKubernetes:                Lookup(tenantOverridesData, "regional_kubernetes", false),
		EnableMultiZonedNodepools:               Lookup(tenantOverridesData, "multi_zoned_nodepools", false),
		EnableArgoEnchanced:                     Lookup(tenantOverridesData, "enable_argo_enhanced", false),
		EnableMaxPodsPerNode:                    Lookup(tenantOverridesData, "enable_max_pods_per_node", false),
		EnableAgentixHub:                        Lookup(tenantOverridesData, "enable_agentix_hub", false),
		IsEnableAgentixHub:                      Lookup(tenantOverridesData, "is_enable_agentix_hub", false),
		EnableK8sBetaApis:                       Lookup(tenantOverridesData, "enable_k8s_beta_apis", false),
		EnableRedisPlatform:                     Lookup(tenantOverridesData, "enable_redis_platform", false),
		EnableSpanner:                           Lookup(tenantOverridesData, "enable_spanner", false),
		AssetPipelineIsSpannerMigration:         Lookup(tenantOverridesData, "asset_pipeline_is_spanner_migration", false),
		EnableApilot:                            Lookup(tenantOverridesData, "enable_apilot", false),
		EnableAlyxMigration:                     Lookup(tenantOverridesData, "enable_alyx_migration", false),
		EnableScaleDownBeforeAlyxMigration:      Lookup(tenantOverridesData, "enable_scale_down_before_alyx_migration", false),
		EnableAlyx:                              Lookup(tenantOverridesData, "enable_alyx", false),
		EnableMigrateAlyxDataToScylla:           Lookup(tenantOverridesData, "enable_migrate_alyx_data_to_scylla", false),
		EnableNeo4jCluster:                      Lookup(tenantOverridesData, "enable_neo4j_cluster", false),
		EnableAwsOutpostAllRegions:              Lookup(tenantOverridesData, "enable_aws_outpost_all_regions", false),
		EnableAzureOutpostAllRegions:            Lookup(tenantOverridesData, "enable_azure_outpost_all_regions", false),
		EnableGcpOutpostAllRegions:              Lookup(tenantOverridesData, "enable_gcp_outpost_all_regions", false),
		EnableOciOutpostAllRegions:              Lookup(tenantOverridesData, "enable_oci_outpost_all_regions", false),
	}, nil
}
