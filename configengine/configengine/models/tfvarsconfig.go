package models

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"reflect"

	"gitlab.xdr.pan.local/xdr/devops/gcp-configuration/configengine/configengine/utils"
)

type TFOverrides struct {
	LogSeverity                                  string `json:"log_severity"`
	EnableXsoarWorkers                           bool   `json:"enable_xsoar_workers"`
	UsePanwByok                                  bool   `json:"use_panw_byok"`
	EnableExactlyOnceDelivery                    bool   `json:"enable_exactly_once_delivery"`
	EnableMessageOrdering                        bool   `json:"enable_message_ordering"`
	AllowXdrGitlabNetworks                       bool   `json:"allow_xdr_gitlab_networks"`
	CronusNodePoolMachineType                    string `json:"cronus_node_pool_machine_type"`
	CronusNodePoolMinNodes                       int    `json:"cronus_node_pool_min_nodes"`
	DynamicNodepoolMachineType                   string `json:"dynamic_nodepool_machine_type"`
	GvsIPOverride                                string `json:"gvs_ip_override"`
	AdditionalDynamicNodepool                    bool   `json:"additional_dynamic_nodepool"`
	AdditionalDynamicNodepoolMachineType         string `json:"additional_dynamic_nodepool_machine_type"`
	AdditionalDetectionEngineNodepoolMachineType string `json:"additional_detection_engine_nodepool_machine_type"`
	ScyllaBlueNodepoolMachineType                string `json:"scylla_blue_nodepool_machine_type"`
	ScyllaGreenNodepoolMachineType               string `json:"scylla_green_nodepool_machine_type"`
	XsoarMachineType                             string `json:"xsoar_machine_type"`
	XsoarMigrationNodepoolDiskSize               int    `json:"xsoar_migration_nodepool_disk_size"` // should be string?
	StaticNodepoolNodeCount                      int    `json:"static_nodepool_node_count"`
	StaticMachineType                            string `json:"static_machine_type"`
	RocksdbNodepoolNodeCount                     int    `json:"rocksdb_nodepool_node_count"` // missing var.rocksdb_standalone??
	RocksdbMachineType                           string `json:"rocksdb_machine_type"`
	RocksdbMaxNodeCount                          int    `json:"rocksdb_max_node_count"` // should be string?
	ProxyVMSourceImageOverride                   string `json:"proxy_vm_source_image_override"`
	ProxyVMMachineTypeOverride                   string `json:"proxy_vm_machine_type_override"`
	EnableCustomKubeDNS                          bool   `json:"enable_custom_kube_dns"`
	EnableCortexPlatform                         bool   `json:"enable_cortex_platform"`
	EnableCortexMCP                              bool   `json:"enable_cortex_mcp"`
	EnableMaxPodsPerNode                         bool   `json:"enable_max_pods_per_node"`
	EnableK8sBetaApis                            bool   `json:"enable_k8s_beta_apis"`
	EnableRedisPlatform                          bool   `json:"enable_redis_platform"`
	EngineProxyVmMachineTypeOverride             string `json:"engine_proxy_vm_machine_type_override"`
	EngineProxyVmSourceImageOverride             string `json:"engine_proxy_vm_source_image_override"`
	AppHubProxyVmMachineTypeOverride             string `json:"app_hub_proxy_vm_machine_type_override"`
	EngineXsoarMachineType                       string `json:"engine_xsoar_machine_type"`
	EngineXsoarNodepoolNodeCount                 int    `json:"engine_xsoar_nodepool_node_count"`
	CustomDubeDNSSidecarImage                    string `json:"custom_kube_dns_sidecar_image"`
	ServicePerimeter                             bool   `json:"service_perimeter"`
	AppProxyBlueGreenTarget                      string `json:"app_proxy_blue_green_target"`
	CollectionNamespace                          string `json:"collection_namespace"`
	EnableSecureGKE                              bool   `json:"enable_secure_gke"`
	DynamicMaxNodeCount                          int    `json:"dynamic_max_node_count"`
	Dynamic2MaxNodeCount                         int    `json:"dynamic2_max_node_count"`
	CronusDeployIdleResources                    bool   `json:"cronus_deploy_idle_resources"`
	CronusStandalone                             bool   `json:"cronus_standalone"`
	SkipGcrCreation                              bool   `json:"skip_gcr_creation"`
	EnableOtelCollector                          bool   `json:"enable_otel_collector"`
	InitialScyllaIplenNodeCount                  int    `json:"initial_scylla_iplen_node_count"`
	CustomKubeDnsImage                           string `json:"custom_kube_dns_image"`
	ElasticsearchMachineType                     string `json:"elasticsearch_machine_type"`
	ResearchTenant                               bool   `json:"research_tenant"`
	ScyllaIplenNodepoolMachineType               string `json:"scylla_iplen_nodepool_machine_type"`
	AppHubProxyVmSourceImageOverride             string `json:"app_hub_proxy_vm_source_image_override"`
	CustomKubeDnsAutoscaler_image                string `json:"custom_kube_dns_autoscaler_image"`
	LogForwardingImageTag                        string `json:"log_forwarding_image_tag"`
	EngineNodePoolMinNodes                       int    `json:"engine_node_pool_min_nodes"`
	CustomKubeDnsDnsmasqImage                    string `json:"custom_kube_dns_dnsmasq_image"`
	AdditionalDetectionEngineNodepool            bool   `json:"additional_detection_engine_nodepool"`
	RocksdbStandalone                            bool   `json:"rocksdb_standalone"`
	EnableXqlCdlEngine                           bool   `json:"enable_xql_cdl_engine"`
	EnableAccessApiToEgress                      bool   `json:"enable_access_api_to_egress"`
	DeployPithos                                 bool   `json:"deploy_pithos"`
	EnableNewScylla                              bool   `json:"new_scylla"`
	KubeDnsCoresPerReplica                       string `json:"kube_dns_cores_per_replica"`
	ScyllaGreenNodepool                          bool   `json:"scylla_green_nodepool"`
	ScyllaBlueNodepool                           bool   `json:"scylla_blue_nodepool"`
	XcloudScyllaBlueNodepool                     bool   `json:"xcloud_scylla_blue_nodepool"`
	XcloudScyllaGreenNodepool                    bool   `json:"xcloud_scylla_green_nodepool"`
	IPLScyllaBlueNodepool                        bool   `json:"ipl_scylla_blue_nodepool"`
	IPLScyllaGreenNodepool                       bool   `json:"ipl_scylla_green_nodepool"`
	EnableMysqlUvem                              bool   `json:"enable_mysql_uvem"`
	CreateAndAttachSubsRoleId                    string `json:"create_and_attach_subs_role_id"`
	KubeDNSAutoscalerRequestsMemory              string `json:"kube_dns_autoscaler_reuqests_memory"`
	KubeDNSAutoscalerRequestsCPU                 string `json:"kube_dns_autoscaler_reuqests_cpu"`
	EnableAgentAPIRedis                          string `json:"enable_agent_api_redis"`
	EnableAgentixHub                             bool   `json:"enable_agentix_hub"`
	EnableGkeDataplaneV2                         bool   `json:"enable_gke_dataplane_v2"`
	EnableGkeDataplaneV2Metrics                  bool   `json:"enable_gke_dataplane_v2_metrics"`
	EnableGkeDataplaneV2Relay                    bool   `json:"enable_gke_dataplane_v2_relay"`
}

// Required to implement into CR struct
// +kubebuilder:object:generate=true
type TfVars struct {
	CortexPlatform            bool   `json:"cortex_platform"`
	VisoNewPlatform           bool   `json:"viso_new_platform"`
	BigTenant                 bool   `json:"big_tenant"`
	BQLocation                string `json:"bq_location"`
	CloudAgents               int    `json:"cloud_agents"`   // only used for project labels, maybe can be removed?
	ColdRetention             bool   `json:"cold_retention"` // only used to conditionally enable p/s, might not be needed
	ConsulDatacenter          string `json:"consul_datacenter"`
	ConsulHost                string `json:"consul_host"`
	CspID                     string `json:"csp_id"`
	Disposable                bool   `json:"disposable"`
	EdrAgents                 int    `json:"edr_agents"` // only used for project labels
	EgressEnabled             bool   `json:"egress_enabled"`
	EnableAssuredWorkloads    bool   `json:"enable_assured_workloads"`
	EnableASM                 bool   `json:"enable_asm"`
	EnableBYOK                bool   `json:"enable_byok"`
	EnableReplicator          bool   `json:"enable_replicator"`
	EnableXcloud              bool   `json:"enable_xcloud"`
	EppAgents                 int    `json:"epp_agents"` // only used for project labels
	EppOnly                   bool   `json:"epp_only"`   // seem to be used only for "expr" logic, might be removed
	ExternalFqdn              string `json:"external_fqdn"`
	ExternalCrtxFqdn          string `json:"external_crtx_fqdn"` // can be consolidated into 1 variable post expr logic
	Forensics                 bool   `json:"forensics"`          // can be used from "authcode==forensics"
	XdrGbLicenses             int    `json:"xdr_gb_licenses"`    // only used for project labels
	HostProject               string `json:"host_project"`
	HostProjectSubnetworkName string `json:"host_project_subnetwork_name"`
	Lcaas                     string `json:"lcaas"`
	Legacy                    bool   `json:"legacy"` // should be removed
	LicenseExpiration         int    `json:"license_expiration"`
	MultiProjectPostfix       string `json:"multi_project_postfix"`
	MetroHostProjectID        string `json:"metro_host_project_id"`
	MetroHostID               string `json:"metro_host_id"`
	MetroHostZone             string `json:"metro_host_zone"`
	MetroTenantIndex          int    `json:"metro_tenant_index"`
	MetroAllInOne             bool   `json:"metro_all_in_one"`
	IsMetroTenant             bool   `json:"is_metro_tenant"`
	NumLicenses               int    `json:"num_licenses"`
	Owner                     string `json:"owner"`
	OwnerGroup                string `json:"owner_group"`
	ParentProjectID           string `json:"parent_project_id"`
	// this variable should be temporary - longterm it should be removed and replaced with logic from the outside (the runner)
	PoolTenantCreation           bool                   `json:"pool_tenant_creation"`
	ProductType                  ProductType            `json:"product_type"`
	ProductCode                  string                 `json:"product_code"`
	ProjectID                    string                 `json:"project_id"`
	EngineProjectID              string                 `json:"engine_project_id"`
	ApphubProjectID              string                 `json:"app_hub_project_id"`
	ApilotProjectID              string                 `json:"apilot_project_id"`
	OutpostProjectID             string                 `json:"outpost_project_id"`
	ProjectPrefix                string                 `json:"project_prefix"`
	Region                       string                 `json:"region"`
	CreationDate                 int                    `json:"creation_date"`
	TenantType                   Authcode               `json:"tenant_type"`
	VisoEnv                      string                 `json:"viso_env"`
	XdrEnv                       string                 `json:"xdr_env"`
	XdrID                        string                 `json:"xdr_id"`
	XdrLicenses                  int                    `json:"xdr_licenses"`
	XsoarMsspChild               bool                   `json:"xsoar_mssp_child"`
	XsoarMsspMaster              bool                   `json:"xsoar_mssp_master"`
	XsiamMsspMaster              bool                   `json:"xsiam_mssp_master"`
	IsMsspChildXdrXsiam          bool                   `json:"is_mssp_child_xdr_xsiam"`
	Zone                         string                 `json:"zone"`
	EnableScylla                 bool                   `json:"enable_scylla"`
	EnableScyllaIplen            bool                   `json:"enable_scylla_iplen"`
	XsoarMsspDev                 bool                   `json:"xsoar_mssp_dev"`
	Xsoar6SN                     string                 `json:"xsoar_6_sn"`
	Xsoar6Env                    string                 `json:"xsoar_6_env"`
	Xsoar6Host                   string                 `json:"xsoar_6_host"`
	Xsoar6MigSize                string                 `json:"xsoar_6_mig_size"`
	Xsoar6AccountID              string                 `json:"xsoar_6_account_id"`
	IsXsoar6Migration            bool                   `json:"is_xsoar_6_migration"`
	IsXsoarOnPremMigration       bool                   `json:"is_xsoar_onprem_migration"`
	XsiamUsers                   int                    `json:"xsiam_users"`
	XsiamLicenses                int                    `json:"xsiam_licenses"`
	ParentExternalFqdn           string                 `json:"parent_external_fqdn"`
	UpgradePhase                 string                 `json:"upgrade_phase"`
	InstanceName                 string                 `json:"instance_name"`
	XpanseAumCount               int                    `json:"xpanse_aum_count"`
	ScaleXdrLicenses             int                    `json:"scale_xdr_licenses"`
	Overrides                    map[string]interface{} `json:"overrides"`
	AppImages                    map[string]interface{} `json:"app_images"`
	EnableCloudPosture           bool                   `json:"enable_cloud_posture"`
	EnableCloudAppsec            bool                   `json:"enable_cloud_appsec"`
	EnableITDR                   bool                   `json:"enable_itdr"`
	IsXsoarLegacySpec            bool                   `json:"is_xsoar_legacy_spec"`
	VisoVersion                  string                 `json:"viso_version"`
	OciId                        string                 `json:"oci_id"`
	OciProjectId                 string                 `json:"oci_project_id"`
	OciPoolOnboard               bool                   `json:"oci_pool_onboard"`
	OciPoolUpgrade               bool                   `json:"oci_pool_upgrade"`
	EnableAlyx                   bool                   `json:"enable_alyx"`
	EnableAwsOutpostAllRegions   bool                   `json:"enable_aws_outpost_all_regions"`
	EnableAzureOutpostAllRegions bool                   `json:"enable_azure_outpost_all_regions"`
	EnableGcpOutpostAllRegions   bool                   `json:"enable_gcp_outpost_all_regions"`
	EnableOciOutpostAllRegions   bool                   `json:"enable_oci_outpost_all_regions"`
}

func newTfVars() *tfvars {
	tc := &tfvars{
		ModelBase: NewModelBase(),
		TfVars: &TfVars{
			Overrides: make(map[string]interface{}),
		},
	}
	tc.setAttrs()
	return tc
}

type tfvars struct {
	*ModelBase
	*TfVars
}

func (c *tfvars) Dependencies() []Configurable {
	return []Configurable{
		&region{},
		&license{},
		&tenant{},
		&infraFeatureFlags{},
		&images{},
	}
}

func (c *tfvars) Name() string {
	return "tfvars"
}

func (c *tfvars) setAttrs() {
	c.ccFilename = c.Name() + "_config.yaml"
	c.cfFilename = c.Name() + "_config.expr"
}

func (c *tfvars) DumpTfVars(f string) error {
	jsonData, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}
	return os.WriteFile(f, jsonData, fs.ModePerm)
}

func NewTfVarsConfigFromTenantVariables(tenantVariables map[string]interface{}) (*TfVars, error) {
	tenantOverridesData, err := utils.GetKeyFromMap(tenantVariables, "overrides")
	if err != nil {
		return nil, err
	}
	overrides := make(map[string]interface{})

	ot := reflect.TypeOf(TFOverrides{})

	for i := 0; i < ot.NumField(); i++ {
		field := ot.Field(i)
		name := field.Tag.Get("json")
		if name == "" {
			return nil, fmt.Errorf("TFOverrieds %s attribute does not have json tag", field.Name)
		}
		if _, ok := tenantOverridesData[name]; !ok {
			continue
		}
		fieldType := field.Type
		// Check the type and call getDefaultValue with explicit type
		switch fieldType.Kind() {
		case reflect.String:
			overrides[name] = Lookup(tenantOverridesData, name, "")
		case reflect.Int:
			overrides[name] = Lookup(tenantOverridesData, name, 0)
		case reflect.Float64:
			overrides[name] = Lookup(tenantOverridesData, name, 0.0)
		case reflect.Bool:
			overrides[name] = Lookup(tenantOverridesData, name, false)
		default:
			return nil, fmt.Errorf("TFOverrides %s attribute has invalid value type: %v", name, fieldType)
		}
	}

	return &TfVars{
		Overrides: overrides,
	}, err
}
