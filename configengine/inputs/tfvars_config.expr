_consul_host_mapping:
  dev: '"**************"'
  prod-au: '"************"'
  prod-ca: '"***********"'
  prod-ch: '"************"'
  prod-de: '"************"'
  prod-eu: '"*************"'
  prod-es: '"*************"'
  prod-fa: '"*************"'
  prod-fr: '"************"'
  prod-gv: '"**************"'
  prod-id: '"*************"'
  prod-il: '"*************"'
  prod-in: '"*************"'
  prod-jp: '"************"'
  prod-pl: '"************"'
  prod-pr: '"*************"'
  prod-qt: '"************"'
  prod-sg: '"************"'
  prod-uk: '"************"'
  prod-sa: '"*************"'
  prod-tw: '"*************"'
  prod-us: '"*************"'
  prod-kr: '"*************"'
  prod-it: '"*************"'
  prod-za: '"*************"'
  prod-br: '"*************"'
  prod-dl: '"*************"'

_qa_csp_ids_list:
  [
    '"64647420"',
    '"184842"',
    '"64669089"',
    '"1838249"',
    '"184842"',
    '"64675498"',
    '"272283"',
    '"1791132"',
  ]

_gcr: |-
  region.is_fedramp ? "us.gcr.io/xdr-registry-" + region.viso_env + "-01" :
  region.is_dev ? "us-docker.pkg.dev/xdr-registry-dev-01" :
  "us-docker.pkg.dev/xdr-registry-prod-us-01"

viso_new_platform: true
big_tenant: infra_ff.enable_big_tenant
bq_location: tenant.bq_location
cloud_agents: license.cloud_agents
cold_retention: license.enable_cold_retention
consul_datacenter: region.viso_env
consul_host: 'local._consul_host_mapping[region.viso_env] + ":8500"'
csp_id: license.support_account_id
disposable: tenant.is_disposable
edr_agents: license.edr_agents
egress_enabled: infra_ff.is_enable_egress
enable_assured_workloads: infra_ff.enable_assured_workloads
enable_asm: license.enable_asm
enable_byok: infra_ff.enable_byok
enable_replicator: infra_ff.enable_replicator
enable_xcloud: infra_ff.enable_xcloud
epp_agents: license.epp_agents
epp_only: license.is_epp_only
external_fqdn: tenant.fqdn
external_crtx_fqdn: tenant.crtx_fqdn
forensics: license.forensics
xdr_gb_licenses: license.xdr_gb_licenses
host_project: region.host_project
host_project_subnetwork_name: tenant.host_project_subnetwork_name
lcaas: tenant.lcaas_id
legacy: tenant.is_legacy
license_expiration: license.license_expiration
multi_project_postfix: region.multi_project_postfix
metro_host_project_id: tenant.metro_host_project_id
metro_host_id: tenant.metro_host_id
metro_host_zone: tenant.metro_host_zone
metro_tenant_index: tenant.metro_tenant_index
metro_all_in_one: tenant.metro_all_in_one
is_metro_tenant: tenant.is_metro_tenant
num_licenses: license.num_licenses
owner: tenant.owner
owner_group: tenant.owner_group
parent_project_id: tenant.parent_project_id
pool_tenant_creation: false
product_type: license.product_type
project_id: tenant.project_id
engine_project_id: tenant.engine_project_id
app_hub_project_id: tenant.apphub_project_id
apilot_project_id: tenant.apilot_project_id
outpost_project_id: tenant.outpost_project_id
project_prefix: region.project_prefix
region: region.gcp_region
creation_date: tenant.creation_date
tenant_type: tenant.authcode
viso_env: region.viso_env
xdr_env: region.xdr_env
xdr_id: tenant.xdr_id
xdr_licenses: license.xdr_tb_licenses
xsoar_mssp_child: tenant.xsoar_mssp_child
xsoar_mssp_master: tenant.xsoar_mssp_master
xsiam_mssp_master: tenant.xsiam_mssp_master
is_mssp_child_xdr_xsiam: tenant.is_mssp_child_xdr_xsiam
zone: tenant.zone
enable_scylla: infra_ff.enable_scylla
enable_scylla_iplen: infra_ff.enable_scylla_iplen
xsoar_mssp_dev: tenant.xsoar_mssp_dev
xsoar_6_sn: tenant.xsoar_6_sn
xsoar_6_env: tenant.xsoar_6_env
xsoar_6_host: tenant.xsoar_6_host
xsoar_6_mig_size: tenant.xsoar_6_mig_size
xsoar_6_account_id: tenant.xsoar_6_account_id
is_xsoar_6_migration: tenant.is_xsoar_6_migration
is_xsoar_onprem_migration: tenant.is_xsoar_onprem_migration
xsiam_users: license.xsiam_users
xsiam_licenses: license.xsiam_gb_licenses
parent_external_fqdn: tenant.parent_external_fqdn
upgrade_phase: tenant.upgrade_phase
rocksdb_standalone: infra_ff.enable_rocksdb_standalone
cronus_standalone: infra_ff.enable_cronus && (!region.is_dev || infra_ff.enable_prod_spec || tenant.is_metro_tenant)
instance_name: tenant.instance_name
xpanse_aum_count: license.xpanse_aum_count
scale_xdr_licenses: license.scale_xdr_licenses
cortex_platform: tenant.is_cortex_platform
product_code: license.product_code
enable_cloud_posture: infra_ff.enable_cortex_platform && license.enable_cloud_posture
enable_cloud_appsec: infra_ff.enable_cortex_platform && license.enable_cloud_appsec
enable_itdr: infra_ff.enable_cortex_platform && license.enable_itdr
is_xsoar_legacy_spec: not infra_ff.enable_cortex_platform || (tenant.is_migrated_cortex_platform && license.is_xsiam)
viso_version: images.families.viso.tag
oci_id: tenant.oci_id
oci_project_id: tenant.oci_project_id
oci_pool_onboard: tenant.oci_pool_onboard
oci_pool_upgrade: tenant.oci_pool_upgrade
enable_alyx: infra_ff.enable_alyx

enable_aws_outpost_all_regions: |-
  infra_ff.enable_cortex_platform &&
  (infra_ff.enable_aws_outpost_all_regions || !region.is_dev)

enable_azure_outpost_all_regions: |-
  infra_ff.enable_cortex_platform &&
  (infra_ff.enable_azure_outpost_all_regions || !region.is_dev)

enable_gcp_outpost_all_regions: |-
  infra_ff.enable_cortex_platform &&
  (infra_ff.enable_gcp_outpost_all_regions || !region.is_dev)


enable_oci_outpost_all_regions: |-
  infra_ff.enable_cortex_platform &&
  (infra_ff.enable_oci_outpost_all_regions || !region.is_dev)



overrides:
  rocksdb_standalone: infra_ff.is_enable_rocksdb_standalone
  is_enable_exposure_management: infra_ff.is_enable_exposure_management
  enable_cronus: infra_ff.enable_cronus
  enable_xql_cdl_engine: infra_ff.enable_xql_cdl_engine
  additional_detection_engine_nodepool: infra_ff.enable_additional_detection_engine_nodepool
  mysql_prod_spec_override: infra_ff.enable_mysql_prod_spec
  megatron_xsoar: infra_ff.enable_megatron_xsoar
  megatron_xdr: infra_ff.enable_megatron_xdr
  scylla_standalone: infra_ff.is_enable_scylla_standalone
  enable_gmp: infra_ff.enable_gmp
  redis_split: infra_ff.enable_redis_split
  enable_dss_redis: infra_ff.enable_redis_dss
  enable_xql_fdr_engine: infra_ff.enable_xql_fdr_engine
  elasticsearch_standalone: infra_ff.enable_elasticsearch_standalone
  is_perf_tenant: tenant.is_perf_tenant
  prod_spec: infra_ff.is_enable_prod_spec
  enable_fetcher_redis: infra_ff.enable_redis_fetcher
  enable_rocksdb_writer: infra_ff.is_enable_rocksdb_writer
  scylla_migration: infra_ff.scylla_migration
  rocksdb_cluster_mode: infra_ff.is_enable_rocksdb_cluster_mode
  enable_email_artifacts_relay: infra_ff.enable_email_artifacts_relay
  xcloud_standalone_deployment: infra_ff.is_enable_xcloud_standalone_deployment
  enable_application_hub: infra_ff.is_enable_application_hub
  bigger_memory_engine: infra_ff.enable_bigger_memory_engine
  enable_alyx_standalone: infra_ff.is_enable_alyx_standalone
  enable_argo_enhanced: infra_ff.enable_argo_enhanced
  enable_playbook_mirroring: infra_ff.enable_primary_playbook_mirroring || infra_ff.enable_secondary_playbook_mirroring
  enable_primary_playbook_mirroring: infra_ff.enable_primary_playbook_mirroring
  enable_secondary_playbook_mirroring: infra_ff.enable_secondary_playbook_mirroring
  primary_project_id: tenant.primary_project_id
  secondary_project_id: tenant.secondary_project_id
  secondary_external_fqdn: tenant.secondary_external_fqdn
  enable_xsoar_migration_node: infra_ff.enable_xsoar_migration_node
  enable_xsoar_workers: infra_ff.is_enable_xsoar_workers
  enable_agent_api_redis: infra_ff.enable_redis_agent_api
  enable_cloud_onboarding_manager_health: infra_ff.enable_cloud_onboarding_manager_health
  regional_kubernetes: infra_ff.enable_regional_kubernetes
  multi_zoned_nodepools: infra_ff.is_enable_multi_zoned_nodepools
  enable_k8s_beta_apis: infra_ff.enable_k8s_beta_apis
  enable_redis_platform: infra_ff.enable_redis_platform
  enable_cortex_mcp: infra_ff.enable_cortex_mcp
  enable_max_pods_per_node: infra_ff.enable_max_pods_per_node
  qa_tenant: tenant.is_qa_automation_tenant || license.support_account_id in local._qa_csp_ids_list
  enable_agentix_hub: infra_ff.is_enable_agentix_hub
  enable_spanner: infra_ff.enable_spanner
  enable_neo4j_cluster: infra_ff.enable_neo4j_cluster

app_images:
  backend: local._gcr + "/" + images.families.backend.repository + ":" + images.families.backend.tag
  dspm_sia: |-
    infra_ff.enable_cortex_platform ? local._gcr + "/" + images.families.dspm_sia.repository + ":" + images.families.dspm_sia.tag : ""
  dspm_dbear: |-
    infra_ff.enable_cortex_platform ? local._gcr + "/" + images.families.dspm_dbear.repository + ":" + images.families.dspm_dbear.tag : ""
  dspm_xporter: |-
    infra_ff.enable_cortex_platform ? local._gcr + "/" + images.families.dspm_xporter.repository + ":" + images.families.dspm_xporter.tag : ""
  dspm_scar: |-
    infra_ff.enable_cortex_platform && ( get(images?.families?.dspm_scar, "tag")  ?? "" ) != ""  ?
      local._gcr + "/" + get(images.families, "dspm_scar").repository + ":" + get(images.families, "dspm_scar").tag :
      ""
