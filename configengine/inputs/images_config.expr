families:
  cloudsec_init:
    repository: '"prismacloud/cloudsec-init"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_init_secdo_wait:
    repository: '"prismacloud/cloudsec-init-secdo-wait"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_init_wait:
    repository: '"prismacloud/cloudsec-init-wait"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_cloud_api_service:
    repository: '"prismacloud/cloud-api-service"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_cloud_api_service_liquibase_init:
    repository: '"prismacloud/cloud-api-service-liquibase-init"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_action_plan_gen_cron:
    repository: '"prismacloud/action-plan-gen-cron"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_action_plan_recon_cron:
    repository: '"prismacloud/action-plan-recon-cron"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_vuln_ap_gen_cron:
    repository: '"prismacloud/vuln-action-plan-gen-cron"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_vuln_ap_recon_cron:
    repository: '"prismacloud/vuln-action-plan-recon-cron"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_action_plan_cleanup_cron:
    repository: '"prismacloud/action-plan-cleanup-cron"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_dashboard_api:
    repository: '"prismacloud/dashboard-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_search_and_investigate:
    repository: '"prismacloud/search-and-investigate"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_xspm_scanner:
    repository: '"prismacloud/xspm-scanner"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_rule_management:
    repository: '"prismacloud/rule-management-service"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_xspm_rules_sync_job:
    repository: '"prismacloud/xspm-rule-sync-job"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_rule_management_init:
    repository: '"prismacloud/rule-management-service-postgres-init"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_secops_dash_api:
    repository: '"prismacloud/secops-dashboard"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_secops_dash_mttr:
    repository: '"prismacloud/secops-dashboard-mttr"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_secops_dash_burndown:
    repository: '"prismacloud/secops-dashboard-burndown"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_verdict_manager:
    repository: '"prismacloud/verdict-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_verdict_manager_init:
    repository: '"prismacloud/verdict-manager-pre-processor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_verdict_manager_liquibase_init:
    repository: '"prismacloud/verdict-manager-liquibase-init"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_attack_path_pre_processor:
    repository: '"prismacloud/attack-path-batch-pre-processor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_attack_path_post_processor:
    repository: '"prismacloud/attack-path-batch-post-processor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_attack_path:
    repository: '"prismacloud/attack-path-batch-scanner"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_inline_scanner:
    repository: '"prismacloud/cspm-inline-scanner"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cloudsec_batch_scanner:
    repository: '"prismacloud/cspm-batch-scanner"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  backend:
    repository: '"cortex-xdr/backend"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
  workload_manager:
    repository: '"cortex-xdr/cortex-workload-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  frontend:
    repository: '"cortex-xdr/frontend"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
  pipeline:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"ipl-master-hf"'
      - '"ipl-master"'
      - '"ipl-stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  rocksdb:
    repository: '"cortex-xdr/analytics-rocksdb"'
    allowed_prod_prefixes:
      - '"analytics-rocksdb"'
      - '"master"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  xsoar:
    repository: '"cortex-xdr/xsoar/demisto/server"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-xsiam"'
      - '"master-xsiam-hf"'
      - '"master-xsoar-ng"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
    extra_registry_paths:
      xsoar: '"cortex-xdr/xsoar/demisto/server"'
      xsoar-workers-router: '"cortex-xdr/xsoar/demisto/xsoar-worker-router"'
      xsoar-engine: '"cortex-xdr/xsoar/demisto/d1"'
      xsoar-workers-operator: '"cortex-xdr/xsoar/demisto/xsoar-worker-operator"'
      xsoar-init: '"cortex-xdr/xsoar/demisto/xsoarinit"'
      xsoar-content: '"cortex-xdr/xsoar/demisto/xsoarcontent"'
      engine-hub: '"cortex-xdr/xsoar/demisto/xsoarenginehub"'
      xsoar-migration/xsoar-8-migration: '"cortex-xdr/xsoar/demisto/xsoarmigration"'
      pb-runner-v2: '"cortex-xdr/xsoar/demisto/xsoarpbrunner"'
      xsoar-api: '"cortex-xdr/xsoar/demisto/xsoarapi"'
      xsoar-workers-gateway: '"cortex-xdr/xsoar/demisto/xsoar-worker-gateway"'
      task-scheduling: '"cortex-xdr/xsoar/demisto/task-scheduling"'
  storybuilder:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"ipl-master-hf"'
      - '"ipl-master"'
      - '"ipl-stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  cronus:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"ipl-master-hf"'
      - '"ipl-master"'
      - '"ipl-stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  dp_ipl_alyx:
    repository: '"data-platform/alyx"'
    allowed_prod_prefixes:
      - '"master-hf"'
      - '"master"'
      - '"stable"'
    product_types:
      - '"XSIAM"'
      - '"XDR"'
      - '"CLOUD"'
  dp_ipl_alyx_operator:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"master-hf"'
      - '"master"'
      - '"stable"'
    product_types:
      - '"XSIAM"'
      - '"XDR"'
      - '"CLOUD"'
  pithos_operator:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"ipl-master-hf"'
      - '"ipl-master"'
      - '"ipl-stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XDR"'
  pithos_cluster:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"ipl-master-hf"'
      - '"ipl-master"'
      - '"ipl-stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XDR"'
  scortex:
    repository: '"cortex-xdr/scortex-model"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  vsg:
    repository: '"cortex-xdr/vsg-operator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  shardscape:
    repository: '"cortex-xdr/shardscape/shardscape"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
  chrome:
    repository: '"cortex-xdr/chrome"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  octopus:
    repository: '"cortex-xdr/hubbub/octopus"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XDR"'
  cwp_k8s_api:
    repository: '"cwp/k8s-connector-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  ca_collection:
    repository: '"cortex-xdr/agent-gateway"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  platform:
    repository: '"cortex-xdr/cortex-platform"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  mcp:
    repository: '"cortex-xdr/agentix/mcp"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
  cwp_analyzer:
    repository: '"cwp/analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sbom_analyzer:
    repository: '"cwp/sbom-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_vulnerability_analyzer:
    repository: '"cwp/vulnerability-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_malware_detection_service:
    repository: '"cwp/malware-detection"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_bc_distributor:
    repository: '"cwp/sp-bc-distributor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_rules_management:
    repository: '"cwp/rules-management"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_scan_results_enricher:
    repository: '"cwp/scan-results-enricher"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_core_asset_analyzer:
    repository: '"cwp/core-asset-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_scan_orchestrator:
    repository: '"cwp/scan-orchestrator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_registry_discovery_orchestrator:
    repository: '"cwp/registry-discovery-orchestrator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_billing:
    repository: '"cwp/billing"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_scan_spec_manager:
    repository: '"cwp/scan-spec-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_k8s_inventory_publisher:
    repository: '"cwp/k8s-inventory-publisher"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_compliance_uai_scanner:
    repository: '"cwp/compliance-uai-scanner"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_compliance_agent_rules_api:
    repository: '"cwp/compliance-agent-rules-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_compliance_xdr_calc:
    repository: '"cwp/compliance-agent-rules-calculator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_compliance_publisher:
    repository: '"cwp/compliance-publisher"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_workload_orchestration:
    repository: '"cwp/sp-workload-orchestrator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_scan_status_observer:
    repository: '"cwp/crtx-ads-scan-status-observer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_prioritization:
    repository: '"prismacloud/compute/agentless/instance-scanning/cwp-ais-prioritization"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_scanner_launch:
    repository: '"cwp/crtx-ads-scanner-launch"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_account_cleaner:
    repository: '"prismacloud/compute/agentless/instance-scanning/cwp-ais-scanned-account-cleaning"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_bc_fetcher:
    repository: '"cwp/sp-bc-fetcher"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_bc_object_handler:
    repository: '"cwp/sp-bc-object-handler"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_cna_engine:
    repository: '"cwp/cna-engine"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_pc1_migration:
    repository: '"cwp/pc1-migration"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cns_api:
    repository: '"cwp/cns-services/cns-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cns_evaluator:
    repository: '"cwp/cns-services/cns-evaluator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cns_graph_engine:
    repository: '"cwp/cns-services/cns-graph-engine"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cns_graph_ingester:
    repository: '"cwp/cns-services/cns-graph-ingester"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cns_neo4j:
    repository: '"cwp/cns-services/cns-neo4j"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  dbre_backuper:
    repository: '"cwp/dbre-backuper"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_serverless_api:
    repository: '"cwp/serverless-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_grouping_service:
    repository: '"prismacloud/apisec-grouping"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_asset_manager:
    repository: '"prismacloud/apisec-asset-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_asset:
    repository: '"prismacloud/apisec-asset-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_issuer:
    repository: '"prismacloud/apisec-issuer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_risk_engine:
    repository: '"prismacloud/apisec-risks-engine"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  ciem:
    repository: '"prismacloud/iam"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  ciem_liquibase:
    repository: '"prismacloud/iam-liquibase"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  ciem_liquibase_bq:
    repository: '"prismacloud/iam-liquibase-bq"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_bc_log_ingestion:
    repository: '"cwp/sp-bc-log-ingestor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_bc_metrics_ingestor:
    repository: '"cwp/sp-bc-metrics-ingestor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_image_analyzer:
    repository: '"cwp/image-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_bff_service:
    repository: '"prismacloud/apisec-bff"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  dspm_content_bouncer:
    repository: '"dig/content-bouncer"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_das:
      repository: '"dig/das"'
      allowed_prod_prefixes:
        - '"stable"'
        - '"master"'
        - '"master-hf"'
      product_types:
        - '"CLOUD"'
  dspm_crespo_jose:
    repository: '"dig/crespo-jose"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_crespo_midfilder:
    repository: '"dig/crespo-midfielder"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_crespo_resource_listener:
    repository: '"dig/crespo-resource-listener"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_crespo_worker:
    repository: '"dig/crespo-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_fda_cortex:
    repository: '"dig/fda-cortex"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_mac:
    repository: '"dig/mac"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_bigquery_migrator:
    repository: '"dig/bigquery-migrator"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_dpc:
    repository: '"dig/dpc"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_worker:
      repository: '"dig/dspm-worker"'
      allowed_prod_prefixes:
        - '"main"'
        - '"master"'
        - '"master-hf"'
      product_types:
        - '"CLOUD"'
  dspm_ace:
    repository: '"dig/ace"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_ack:
    repository: '"dig/files-access-checker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_momento:
    repository: '"dig/momento"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_outpost_orchestrator:
    repository: '"dig/outpost-orchestrator"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_polaris:
    repository: '"dig/polaris"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_datasource:
    repository: '"dig/datasource"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_datasource_worker:
    repository: '"dig/datasource-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_voyager:
    repository: '"dig/voyager"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_xporter:
    repository: '"dig/xporter"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_sia:
    repository: '"dig/sia-standalone"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_dbear:
    repository: '"dig/dbear"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_scar:
    repository: '"dig/scar"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_skinner:
    repository: '"dig/skinner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  dspm_data_classification_settings:
    repository: '"dig/data-classification-settings"'
    allowed_prod_prefixes:
      - '"main"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
  cwp_vuln_evaluator:
    repository: '"cwp/vulnerability-evaluator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_api:
    repository: '"cwp/cwp-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ci_analyzer:
    repository: '"cwp/ci-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_k8s_rules_calculator:
    repository: '"cwp/k8s-rule-calculator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_snapshot:
    repository: '"cwp/sp-snapshot"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_api:
    repository: '"cwp/ads-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_snapshots:
    repository: '"prismacloud/compute/agentless/instance-scanning/cwp-ais-snapshot"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_grouping:
    repository: '"prismacloud/compute/agentless/instance-scanning/cwp-ais-grouping"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_platform_sync:
    repository: '"cwp/crtx-ads-platform-sync"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_ads_account_controller:
    repository: '"prismacloud/compute/agentless/instance-scanning/cwp-ais-account-controller"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_account_controller:
    repository: '"cwp/sp-account-controller"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_sp_health_tracker:
    repository: '"cwp/sp-health-tracker"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_inspection_service:
    repository: '"prismacloud/apisec-inspection"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_enricher_service:
    repository: '"prismacloud/apisec-enricher"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_spec_service:
    repository: '"prismacloud/apisec-spec-service"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  apisec_scan_manager_service:
    repository: '"cwp/apisec-scan-manager"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_application_application_api:
    repository: '"cas-docker/application-application-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_dashboards_dashboards_api:
    repository: '"cas-docker/dashboards-dashboards-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_db_migrations_job:
      repository: '"cas-docker/db-migrations-db-migrations-job"'
      allowed_prod_prefixes:
        - '"main"'
        - '"main-hf"'
        - '"stable"'
      product_types:
        - '"CLOUD"'
  cas_customers_management_customers_management:
    repository: '"cas-docker/customers-management-customers-management"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_orchestration_orchestration:
    repository: '"cas-docker/orchestration-orchestration"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_orchestration_scans_management:
    repository: '"cas-docker/orchestration-scans-management"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_issues:
    repository: '"cas-docker/persistence-issues"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_persistence_api:
    repository: '"cas-docker/persistence-persistence-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_persistence_job:
    repository: '"cas-docker/persistence-persistence-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_remediations_get_findings:
    repository: '"cas-docker/remediations-get-findings"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_remediations_iac_fix:
    repository: '"cas-docker/remediations-iac-fix"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_remediations_post_fix:
    repository: '"cas-docker/remediations-post-fix"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_remediations_pre_fix:
    repository: '"cas-docker/remediations-pre-fix"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_resources_calculator_resources_calculator_api:
    repository: '"cas-docker/resources-calculator-resources-calculator-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_iac_iac_scanner:
    repository: '"cas-docker/scanner-iac-iac-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sast_sast_scanner:
    repository: '"cas-docker/scanner-sast-sast-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_sca_findings_scanner:
    repository: '"cas-docker/scanner-sca-sca-findings-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_sca_scanner:
    repository: '"cas-docker/scanner-sca-sca-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_secrets_secrets_scanner:
    repository: '"cas-docker/scanner-secrets-secrets-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_appcode_secrets_secrets_scanner:
    repository: '"cas-docker/scanner-appcode-secrets-secrets-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_blueprint_scanner_python:
    repository: '"cas-docker/scanner-blueprint-scanner-python"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_plan_scan:
    repository: '"cas-docker/scanners-orchestration-plan-scan"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_scan_failure_handler:
    repository: '"cas-docker/scanners-orchestration-scan-failure-handler"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_post_scan:
    repository: '"cas-docker/scanners-orchestration-post-scan"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_pre_scan:
    repository: '"cas-docker/scanners-orchestration-pre-scan"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_pre_scan_periodic:
    repository: '"cas-docker/scanners-orchestration-pre-scan-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_pre_scan_pr:
    repository: '"cas-docker/scanners-orchestration-pre-scan-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_post_scan_periodic:
    repository: '"cas-docker/scanners-orchestration-post-scan-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_post_scan_pr:
    repository: '"cas-docker/scanners-orchestration-post-scan-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_lobby_periodic:
    repository: '"cas-docker/scanners-orchestration-lobby-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_lobby_pr:
    repository: '"cas-docker/scanners-orchestration-lobby-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_scan_result:
    repository: '"cas-docker/source-control-scan-result"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_files_fetching:
    repository: '"cas-docker/source-control-files-fetching"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_pr_comments:
    repository: '"cas-docker/source-control-pr-comments"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_source_control:
    repository: '"cas-docker/source-control-source-control"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_sync_customer:
    repository: '"cas-docker/source-control-sync-customer"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_sync_branches:
    repository: '"cas-docker/source-control-sync-branches"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_trigger_branch_scans:
    repository: '"cas-docker/source-control-trigger-branch-scans"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_webhook:
    repository: '"cas-docker/source-control-webhook"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_transporter_transporter:
    repository: '"cas-docker/transporter-transporter"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_webhooks_subscription:
    repository: '"cas-docker/source-control-webhooks-subscription"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_collectors_collectors:
      repository: '"cas-docker/collectors-collectors"'
      allowed_prod_prefixes:
        - '"main"'
        - '"main-hf"'
        - '"stable"'
      product_types:
        - '"CLOUD"'
  cas_collectors_parser:
      repository: '"cas-docker/collectors-parser"'
      allowed_prod_prefixes:
        - '"main"'
        - '"main-hf"'
        - '"stable"'
      product_types:
        - '"CLOUD"'
  cas_orchestration_scans:
    repository: '"cas-docker/orchestration-scans"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_fetch_external_project_report:
    repository: '"cas-docker/source-control-fetch-external-project-report"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_vcs_enrichment:
    repository: '"cas-docker/source-control-vcs-enrichment"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_aspm_cases_cases_service:
    repository: '"cas-docker/aspm-cases-cases-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_billing_billing_api:
    repository: '"cas-docker/billing-billing-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_ci_logs_ingestion:
    repository: '"cas-docker/cicd-ci-logs-ingestion"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_cicd_graphql:
    repository: '"cas-docker/cicd-cicd-graphql"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_persist_vcs_ci_manifest:
    repository: '"cas-docker/cicd-persist-vcs-ci-manifest"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_supply_chain_tools:
    repository: '"cas-docker/cicd-supply-chain-tools"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_detection_rules_detection_rules:
    repository: '"cas-docker/detection-rules-detection-rules"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_parser_third_party_uploads:
    repository: '"cas-docker/parser-third-party-uploads"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_parser_third_party_scanner:
    repository: '"cas-docker/parser-third-party-third-party-parser"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_policies_policies_api:
    repository: '"cas-docker/policies-policies-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_policies_ai_guardrails:
      repository: '"cas-docker/policies-ai-guardrails-suggestor"'
      allowed_prod_prefixes:
        - '"main"'
        - '"main-hf"'
        - '"stable"'
      product_types:
        - '"CLOUD"'
  cas_source_control_ci_cd_transformer:
    repository: '"cas-docker/source-control-ci-cd-transformer"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_git_users:
    repository: '"cas-docker/source-control-git-users"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_get_scanned_branches:
    repository: '"cas-docker/source-control-get-scanned-branches"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_sync_external_projects:
    repository: '"cas-docker/source-control-sync-external-projects"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_vcs_ci_fetcher:
    repository: '"cas-docker/source-control-vcs-ci-fetcher"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_platform_bridge:
    repository: '"cas-docker/source-control-platform-bridge"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_enry_enry_scanner:
    repository: '"cas-docker/scanner-enry-enry-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_enry_enry_result_parser:
    repository: '"cas-docker/scanner-enry-enry-result-parser"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_pipex_pipex_scanner:
    repository: '"cas-docker/scanner-pipex-pipex-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_pipex_pipex_result_parser:
    repository: '"cas-docker/scanner-pipex-pipex-result-parser"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_deppy_deppy_scanner:
    repository: '"cas-docker/scanner-deppy-deppy-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_deppy_deppy_result_parser:
    repository: '"cas-docker/scanner-ci-match-ci-match-result-parser"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_ci_match_ci_match_scanner:
    repository: '"cas-docker/scanner-ci-match-ci-match-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_ci_match_ci_match_result_parser:
    repository: '"cas-docker/scanner-ci-match-ci-match-result-parser"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_assets_generator:
    repository: '"cas-docker/cicd-assets-generator"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_circleci:
    repository: '"cas-docker/cicd-circleci"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_graph_entities_loader:
    repository: '"cas-docker/cicd-graph-entities-loader"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_inspect_build_logs_job:
    repository: '"cas-docker/cicd-inspect-build-logs-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_pipeline_image_match:
    repository: '"cas-docker/cicd-pipeline-image-match"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_jenkins_manager:
    repository: '"cas-docker/cicd-jenkins"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_webhook_handler:
    repository: '"cas-docker/cicd-webhook-handler"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_cli_api:
    repository: '"cas-docker/scanners-orchestration-cli-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanners_orchestration_scanner_tasks_manager_api:
    repository: '"cas-docker/scanners-orchestration-scanner-tasks-manager-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_secrets_secrets_scanner_worker:
    repository: '"cas-docker/scanner-secrets-secrets-scanner-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_iac_iac_scanner_worker:
    repository: '"cas-docker/scanner-iac-iac-scanner-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_sca_scanner_worker:
    repository: '"cas-docker/scanner-sca-sca-scanner-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_sca_findings_scanner_worker:
    repository: '"cas-docker/scanner-sca-sca-findings-scanner-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_tf_run_task_results:
    repository: '"cas-docker/source-control-tf-run-task-results"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_handle_billing_workflow:
    repository: '"cas-docker/billing-report-usage"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_product_analytics_workflow:
    repository: '"cas-docker/product-analytics-report-product-analytics"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_packages_api:
    repository: '"cas-docker/scanner-sca-packages-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_sca_artifactory_management:
    repository: '"cas-docker/scanner-sca-artifactory-management"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_sbom_management_sbom_management:
    repository: '"cas-docker/sbom-management-sbom-management"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_code_to_cloud_api:
    repository: '"cas-docker/code-to-cloud-code-to-cloud-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_code_to_cloud_external_modules_api:
    repository: '"cas-docker/code-to-cloud-code-to-cloud-external-modules-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_tracing:
    repository: '"cas-docker/code-to-cloud-tracing"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_vcs_pipeline_tools_processor:
    repository: '"cas-docker/cicd-vcs-pipeline-tools-processor"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_pipeline_tools_sync:
    repository: '"cas-docker/cicd-pipeline-tools-sync"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_tagging_bot:
    repository: '"cas-docker/code-to-cloud-tagging-bot"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_drift_detection:
    repository: '"cas-docker/code-to-cloud-drift-detection"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_finding_issue_tracing:
    repository: '"cas-docker/code-to-cloud-finding-issue-tracing"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_sop_ppe_findings:
    repository: '"cas-docker/cicd-calculate-sop-ppe-findings-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_unified_cli_unified_cli_api:
    repository: '"cas-docker/unified-cli-unified-cli-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_client_metrics_client_metrics_api:
    repository: '"cas-docker/client-metrics-client-metrics-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_code_to_cloud_internet_exposure_scanner:
    repository: '"cas-docker/code-to-cloud-internet-exposure-scanner"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  healops:
    repository: '"prismacloud/healops"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  uvem_netscan_processor:
    repository: '"uvem/netscan-processor"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  uvem_netscan_controller:
    repository: '"uvem/netscan-controller"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  uvem_vip_api:
    repository: '"uvem/vulnerability-intelligence-platform-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  uvem_vxp_api:
    repository: '"uvem/vulnerability-experience-platform"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  viso:
    repository: '"viso/viso"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
  xdr_agent_asset:
    repository: '"cwp/agent-asset"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  itdr_risk_processor:
    repository: '"prismacloud/itdr-risk-processor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  itdr_api:
    repository: '"prismacloud/itdr-api"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  itdr_data_pipeline:
    repository: '"prismacloud/itdr-data-pipeline"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  itdr_bigquery_migrator:
    repository: '"prismacloud/itdr-bigquery-migrator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  itdr_data_syncer:
    repository: '"prismacloud/data-syncer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  notification_forwarding_transport:
    repository: '"prismacloud/external_integrations/forwarding-service-transport"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  notification_forwarding_decorator:
    repository: '"prismacloud/external_integrations/forwarding-service-decorator"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  notification_api_service:
    repository: '"prismacloud/external_integrations/notifications_api_service"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_containers_analyzer:
    repository: '"cwp/containers-analyzer"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cwp_fpp:
    repository: '"cwp/finding-processor"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  agentix_hub:
    repository: '"cortex-xdr/agentix/agentix-hub"'
    allowed_prod_prefixes:
      - '"stable"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
  ai_gateway:
    repository: '"cortex-xdr/ai-gateway"'
    allowed_prod_prefixes:
      - '"stable"'
      - '"master"'
      - '"master-hf"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XDR"'
  qr_reader_server:
    repository: '"golden-images/qr-reader-server"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
      - '"1.0-2505211451"'
      - '"1.0"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XPANSE"'
      - '"XDR"'
  cas_cicd_persist_vcs_ci_manifest:
    repository: '"cas-docker/cicd-persist-vcs-ci-manifest"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_remove_integration_job:
    repository: '"cas-docker/cicd-remove-integration-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_cicd_api:
    repository: '"cas-docker/cicd-cicd-api"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_cicd_remove_integration:
    repository: '"cas-docker/cicd-remove-integration-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_application_application_job:
    repository: '"cas-docker/application-application-job"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_scan_result:
    repository: '"cas-docker/source-control-scan-result"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_manager_persistence_manager:
    repository: '"cas-docker/persistence-manager-persistence-manager"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_application_repository_urgency_runtime_metrics_collector:
    repository: '"cas-docker/application-repository-urgency-runtime-metrics-collector"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_enrichment_service:
    repository: '"cas-docker/persistence-enrichment-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_prisma_migration_scanned_branches:
    repository: '"cas-docker/source-control-prisma-migration-scanned-branches"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_actions_handler:
    repository: '"cas-docker/source-control-actions-handler"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  email_security:
    repository: '"cortex-xdr/email-security-alerts"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
      - '"XDR"'
  agent_mgmt:
    repository: '"cwp/agent-mgmt"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSOAR"'
      - '"XPANSE"'
      - '"XSIAM"'
      - '"XDR"'
  itdr_audits:
    repository: '"prismacloud/itdr-cap-responder"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_orchestration_public_apis:
    repository: '"cas-docker/orchestration-public-apis"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_persistence_augmentor:
    repository: '"cas-docker/persistence-augmentor"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_data_gateway:
    repository: '"cas-docker/source-control-data-gateway"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_enrichment_manager_enrichment_manager:
    repository: '"cas-docker/enrichment-manager-enrichment-manager"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_data_collector_worker:
    repository: '"cas-docker/source-control-data-collector-worker"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_source_control_post_collection_processor:
    repository: '"cas-docker/source-control-post-collection-processor"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_deppy_scanner_deppy_service:
    repository: '"cas-docker/scanner-deppy-scanner-deppy-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_pipex_scanner_pipex_service:
    repository: '"cas-docker/scanner-pipex-scanner-pipex-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_ci_match_scanner_ci_match_service:
    repository: '"cas-docker/scanner-ci-match-scanner-ci-match-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  cas_scanner_enry_scanner_enry_service:
    repository: '"cas-docker/scanner-enry-scanner-enry-service"'
    allowed_prod_prefixes:
      - '"main"'
      - '"main-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
  db_init:
    repository: '"cwp/db-init"'
    allowed_prod_prefixes:
      - '"master"'
      - '"master-hf"'
      - '"stable"'
    product_types:
      - '"CLOUD"'
      - '"XSIAM"'
components:
  api:
    family: '"backend"'
  api-be:
    family: '"backend"'
  api-papi:
    family: '"backend"'
  api-xql:
    family: '"backend"'
  api-xsoar:
    family: '"backend"'
  api-agent-be:
    family: '"backend"'
  fetcher:
    family: '"backend"'
  rcs-results-processor:
    family: '"backend"'
  risk-score-cie-processor:
    family: '"backend"'
  agent-api:
    family: '"backend"'
  chat-api:
    family: '"backend"'
  agentix-hub:
    family: '"agentix_hub"'
  ai-gateway:
    family: '"ai_gateway"'
  mcp:
    family: '"mcp"'
  log-processor:
    family: '"backend"'
  forensics-processor:
    family: '"backend"'
  task-processor:
    family: '"backend"'
  task-scheduling:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/task-scheduling"'
  mega-processor:
    family: '"backend"'
  overseer:
    family: '"backend"'
  overseer/wlm:
    family: '"workload_manager"'
  overseer/wlm-scheduler:
    family: '"backend"'
  secdo-init:
    family: '"backend"'
  metrics:
    family: '"backend"'
  notifier:
    family: '"backend"'
  analytics-alerts-emitter:
    family: '"backend"'
  analytics-decider:
    family: '"backend"'
  analytics-task-processor:
    family: '"backend"'
  analytics-profiles-orchestrator:
    family: '"backend"'
  analytics-detection-engine:
    family: '"backend"'
  analytics-detection-engine-external-data:
    family: '"backend"'
  analytics-de:
    family: '"backend"'
  graph-detection-content-sync-cron-job:
    family: '"backend"'
  dp-analytics-graph-detection-engine:
    family: '"backend"'
  dp-analytics-graph-emitter:
    family: '"backend"'
  dp-analytics-graph-issue-mgr:
    family: '"backend"'
  pz-schema-manager:
    family: '"backend"'
  xcloud-ingester:
    family: '"backend"'
  saas-collection:
    family: '"backend"'
  ipl-controller-manager:
    family: '"storybuilder"'
  storybuilders:
    family: '"storybuilder"'
  ipl-cronus-operator-controller-manager:
    family: '"cronus"'
  ipl-pithos-node:
    family: '"pithos_cluster"'
  ipl-pithos-operator-controller-manager:
    family: '"pithos_operator"'
  ipl-cronus-node:
    family: '"cronus"'
  ipl-metrus-operator-controller-manager:
    family: '"cronus"'
  ipl-metrus-node:
    family: '"cronus"'
  pipeline:
    family: '"pipeline"'
  dp-ipl-alyx:
    family: '"dp_ipl_alyx"'
  dp-ipl-alyx-operator:
    family: '"dp_ipl_alyx_operator"'
  alyx-migration:
    family: '"backend"'
  alyx-migration:
    family: '"backend"'
  pipeline-helper:
    family: '"pipeline"'
  dms:
    family: '"pipeline"'
  xql-engine:
    family: '"pipeline"'
  xql-cdl-engine:
    family: '"pipeline"'
  xql-fdr-engine:
    family: '"pipeline"'
  cold-storage-datasets-aggregator-worker:
    family: '"pipeline"'
  cold-storage-datasets-aggregator:
    family: '"pipeline"'
  cold-storage-aggregator:
    family: '"pipeline"'
  egress-aggregator:
    family: '"pipeline"'
  egress-forwarding:
    family: '"pipeline"'
  metrics-aggregator:
    family: '"pipeline"'
  association-replication:
    family: '"backend"'
  frontend/nginx:
    family: '"frontend"'
  frontend/pyramid:
    family: '"backend"'
  analytics-rocksdb:
    family: '"rocksdb"'
  analytics-rocksdb-writer:
    family: '"rocksdb"'
  chrome-app:
    family: '"chrome"'
  xsoar:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/server"'
  xsoar-workers-router:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoar-worker-router"'
  xsoar-engine:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/d1"'
  xsoar-workers-operator:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoar-worker-operator"'
  xsoar-init:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarinit"'
  xsoar-content:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarcontent"'
  engine-hub:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarenginehub"'
  xsoar-migration/be3-migration:
    family: '"backend"'
  xsoar-migration/xsoar-8-migration:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarmigration"'
  pb-runner-v2:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarpbrunner"'
  xsoar-api:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoarapi"'
  xsoar-workers-gateway:
    family: '"xsoar"'
    repository: '"cortex-xdr/xsoar/demisto/xsoar-worker-gateway"'
  scortex:
    family: '"scortex"'
  vsg-controller-manager:
    family: '"vsg"'
  shardscape:
    family: '"shardscape"'
  octopus:
    family: '"octopus"'
  dp-uai-assets-legacy:
    family: '"backend"'
  ipl-archive-aggregator:
    family: '"pipeline"'
  ipl-preprocessed-data-batcher:
    family: '"pipeline"'
  email-artifacts-relay:
    family: '"backend"'
  email-security-alerts:
    family: '"email_security"'
    repository: '"cortex-xdr/email-security-alerts"'
  email-security-actions:
    family: '"email_security"'
    repository: '"cortex-xdr/email-security-actions"'
  email-security-runner:
    family: '"email_security"'
    repository: '"cortex-xdr/email-security-runner"'
  cts:
    family: '"backend"'
  cloud-onboarding-templates:
    family: '"backend"'
  dp-uai-findings:
    family: '"pipeline"'
  dp-uai-assets:
    family: '"pipeline"'
  dp-uai-assets-association:
    family: '"pipeline"'
  dp-uai-assets-migration:
    family: '"pipeline"'
  modules-issue-ingester:
    family: '"backend"'
  issue-fetcher:
    family: '"backend"'
  issue-enricher:
    family: '"backend"'
  issues-updater:
    family: '"backend"'
  platform:
    family: '"platform"'
  cloud-onboarding-manager:
    family: '"backend"'
  dspm-cb-listener:
    family: '"dspm_content_bouncer"'
  dspm-cb-worker:
    family: '"dspm_content_bouncer"'
  dspm-crespo-jose:
    family: '"dspm_crespo_jose"'
  dspm-crespo-midfielder:
    family: '"dspm_crespo_midfilder"'
  dspm-crespo-resource-listener:
    family: '"dspm_crespo_resource_listener"'
  dspm-crespo-worker:
    family: '"dspm_crespo_worker"'
  dspm-dpc:
    family: '"dspm_dpc"'
  dspm-das:
      family: '"dspm_das"'
  dspm-worker:
      family: '"dspm_worker"'
  dspm-fda-api:
    family: '"dspm_fda_cortex"'
  dspm-fda-lsr:
    family: '"dspm_fda_cortex"'
  dspm-fda-worker:
    family: '"dspm_fda_cortex"'
  dspm-oo-boarder:
    family: '"dspm_outpost_orchestrator"'
  dspm-oo-joeker:
    family: '"dspm_outpost_orchestrator"'
  dspm-oo-ready-job-evaluator:
    family: '"dspm_outpost_orchestrator"'
  dspm-oo-genie:
    family: '"dspm_outpost_orchestrator"'
  dspm-oo-worker:
    family: '"dspm_outpost_orchestrator"'
  dspm-polaris:
    family: '"dspm_polaris"'
  dspm-polaris_worker:
    family: '"dspm_polaris"'
  dspm-datasource:
    family: '"dspm_datasource"'
  dspm-datasource_worker:
    family: '"dspm_datasource_worker"'
  dspm-voyager:
    family: '"dspm_voyager"'
  dspm_xporter:
    family: '"dspm_xporter"'
  dspm-mac:
    family: '"dspm_mac"'
  dspm-mac_worker:
    family: '"dspm_mac"'
  dspm-bigquery-migrator:
    family: '"dspm_bigquery_migrator"'
  dspm-x-files-momento:
    family: '"dspm_momento"'
  dspm-x-files-skinner:
    family: '"dspm_skinner"'
  dspm-data-classification-settings:
    family: '"dspm_data_classification_settings"'
  init:
    family: '"cloudsec_init"'
  cloudsec_init_secdo_wait:
    family: '"cloudsec_init_secdo_wait"'
  cloudsec_init_wait:
    family: '"cloudsec_init_wait"'
  cloud-api-service:
    family: '"cloudsec_cloud_api_service"'
  xspm-scanner:
    family: '"cloudsec_xspm_scanner"'
  action-plan-gen-cron-job:
    family: '"cloudsec_action_plan_gen_cron"'
  action-plan-recon-cron-job:
    family: '"cloudsec_action_plan_recon_cron"'
  vuln-ap-gen-cron-job:
    family: '"cloudsec_vuln_ap_gen_cron"'
  vuln-ap-recon-cron-job:
    family: '"cloudsec_vuln_ap_recon_cron"'
  action-plan-cleanup-cron-job:
    family: '"cloudsec_action_plan_cleanup_cron"'
  dashboard-api:
    family: '"cloudsec_dashboard_api"'
  attack-path-scanner-cron-job:
    family: '"cloudsec_attack_path_post_processor"'
  verdict-manager:
    family: '"cloudsec_verdict_manager"'
  search-and-investigate:
    family: '"cloudsec_search_and_investigate"'
  inline-scanner:
    family: '"cloudsec_inline_scanner"'
  batch-scanner-cron-job:
    family: '"cloudsec_batch_scanner"'
  rule-management:
    family: '"cloudsec_rule_management"'
  xspm-rules-sync-job:
    family: '"cloudsec_xspm_rules_sync_job"'
  healops:
    family: '"healops"'
  secops-dash-api:
    family: '"cloudsec_secops_dash_api"'
  secops-dash-mttr-cron-job:
    family: '"cloudsec_secops_dash_mttr"'
  secops-dash-burndown-cron-job:
    family: '"cloudsec_secops_dash_burndown"'
  cwp-sbom-analyzer:
    family: '"cwp_sbom_analyzer"'
  cwp-vulnerability-analyzer:
    family: '"cwp_vulnerability_analyzer"'
  cwp-malware-analyzer:
    family: '"cwp_analyzer"'
  cwp-malware-detection-service:
    family: '"cwp_malware_detection_service"'
  cwp-secret-analyzer:
    family: '"cwp_analyzer"'
  cwp-trusted-image-analyzer:
    family: '"cwp_analyzer"'
  cwp-trust-dispatcher:
    family: '"cwp_fpp"'
  cwp-k8s-api:
    family: '"cwp_k8s_api"'
  cwp-sp-bc-distributor:
    family: '"cwp_sp_bc_distributor"'
  cwp-rules-management:
    family: '"cwp_rules_management"'
  cwp-api:
    family: '"cwp_api"'
  cwp-ci-analyzer:
    family: '"cwp_ci_analyzer"'
  cwp-scan-results-enricher:
    family: '"cwp_scan_results_enricher"'
  cwp-serverless-api:
    family: '"cwp_serverless_api"'
  cwp-core-asset-analyzer:
    family: '"cwp_core_asset_analyzer"'
  cwp-registry-scan-orchestrator:
    family: '"cwp_scan_orchestrator"'
  cwp-serverless-scan-orchestrator:
    family: '"cwp_scan_orchestrator"'
  cwp-registry-discovery-orchestrator:
    family: '"cwp_registry_discovery_orchestrator"'
  cwp-private-discovery-orchestrator:
    family: '"cwp_registry_discovery_orchestrator"'
  cwp-billing:
    family: '"cwp_billing"'
  cwp-cleanup:
    family: '"cwp_billing"'
  cwp-k8s-inventory-publisher:
    family: '"cwp_k8s_inventory_publisher"'
  cwp-compliance-uai-scanner:
    family: '"cwp_compliance_uai_scanner"'
  cwp-compliance-agent-rules-api:
    family: '"cwp_compliance_agent_rules_api"'
  cwp-compliance-xdr-calc:
    family: '"cwp_compliance_xdr_calc"'
  cwp-k8s-rules-calculator:
    family: '"cwp_k8s_rules_calculator"'
  cwp-compliance-publisher:
    family: '"cwp_compliance_publisher"'
  cwp-sp-workload-orchestration:
    family: '"cwp_sp_workload_orchestration"'
  cwp-ads-scan-status-observer:
    family: '"cwp_ads_scan_status_observer"'
  cwp-ads-prioritization:
    family: '"cwp_ads_prioritization"'
  cwp-ads-grouping:
    family: '"cwp_ads_grouping"'
  cwp-ads-scanner-launch:
    family: '"cwp_ads_scanner_launch"'
  cwp-ads-account-cleaner:
    family: '"cwp_ads_account_cleaner"'
  cwp-ads-platform-sync:
    family: '"cwp_ads_platform_sync"'
  cwp-sp-bc-fetcher:
    family: '"cwp_sp_bc_fetcher"'
  cwp-sp-bc-object-handler:
    family: '"cwp_sp_bc_object_handler"'
  cwp-sp-bc-log-ingestion:
    family: '"cwp_sp_bc_log_ingestion"'
  cwp-sp-bc-metrics-ingestor:
    family: '"cwp_sp_bc_metrics_ingestor"'
  cwp-image-analyzer:
    family: '"cwp_image_analyzer"'
  cwp-ads-account-controller:
    family: '"cwp_ads_account_controller"'
  cwp-containers-analyzer:
    family: '"cwp_containers_analyzer"'
  cwp-sp-account-controller:
    family: '"cwp_sp_account_controller"'
  cwp-sp-health-tracker:
    family: '"cwp_sp_health_tracker"'
  cwp-sp-snapshot:
    family: '"cwp_sp_snapshot"'
  cwp-ads-api:
    family: '"cwp_ads_api"'
  cwp-ads-snapshots:
    family: '"cwp_ads_snapshots"'
  cwp-scan-spec-manager:
    family: '"cwp_scan_spec_manager"'
  cwp-cna-engine:
    family: '"cwp_cna_engine"'
  cwp-pc1-migration:
    family: '"cwp_pc1_migration"'
  cwp-fpp-dispatcher:
    family: '"cwp_fpp"'
  cwp-fpp-dispatcher-2:
    family: '"cwp_fpp"'
  cwp-fpp-worker:
    family: '"cwp_fpp"'
  cwp-fpp-policy-evaluator:
    family: '"cwp_fpp"'
  cns-api:
    family: '"cns_api"'
  cns-evaluator:
    family: '"cns_evaluator"'
  cns-graph-engine:
    family: '"cns_graph_engine"'
  cns-graph-ingester:
    family: '"cns_graph_ingester"'
  cns-neo4j:
    family: '"cns_neo4j"'
  dbre-backuper:
    family: '"dbre_backuper"'
  xdr-agent-asset:
    family: '"xdr_agent_asset"'
  apisec-inspection-service:
    family: '"apisec_inspection_service"'
  apisec-bff-service:
    family: '"apisec_bff_service"'
  apisec-risk-engine:
    family: '"apisec_risk_engine"'
  apisec-enricher-service:
    family: '"apisec_enricher_service"'
  apisec-spec-service:
    family: '"apisec_spec_service"'
  apisec-scan-manager-service:
    family: '"apisec_scan_manager_service"'
  apisec-asset:
    family: '"apisec_asset"'
  apisec-issuer:
    family: '"apisec_issuer"'
  apisec-asset-manager:
    family: '"apisec_asset_manager"'
  apisec-grouping-service:
    family: '"apisec_grouping_service"'
  ca-collection-coordinator:
    family: '"ca_collection"'
  ca-collection-eai-workers/ca-collection-eai-workers:
    family: '"ca_collection"'
  ca-collection-fsi-workers/ca-collection-fsi-workers:
    family: '"ca_collection"'
  application-api:
    family: '"cas_application_application_api"'
  dashboards-api:
    family: '"cas_dashboards_dashboards_api"'
  db-migrations-deploy-workflow:
    family: '"cas_db_migrations_job"'
  customers-management:
    family: '"cas_customers_management_customers_management"'
  repo-scan-template:
    family: '"cas_orchestration_orchestration"'
  scans-management:
    family: '"cas_orchestration_scans_management"'
  scans:
    family: '"cas_orchestration_scans"'
  persist-findings-and-issues-workflow-template:
    family: '"cas_persistence_issues"'
  persistence-api:
    family: '"cas_persistence_persistence_api"'
  persist-data-pipeline-template:
    family: '"cas_persistence_persistence_job"'
  remediations-workflow:
    family: '"cas_remediations_post_fix"'
  iac-remediations-workflow:
    family: '"cas_remediations_iac_fix"'
  resources-calculator-api:
    family: '"cas_resources_calculator_resources_calculator_api"'
  enry-scanner-template:
    family: '"cas_scanner_enry_enry_scanner"'
  deppy-scanner-template:
    family: '"cas_scanner_deppy_deppy_scanner"'
  cimatch-scanner-template:
    family: '"cas_scanner_ci_match_ci_match_scanner"'
  iac-scanner-template:
    family: '"cas_scanner_iac_iac_scanner"'
  scanner-sast-sast-scanner:
    family: '"cas_scanner_sast_sast_scanner"'
  sca-scanner-template:
    family: '"cas_scanner_sca_sca_scanner"'
  secrets-scanner-template:
    family: '"cas_scanner_secrets_secrets_scanner"'
  scanner-blueprint-workflow-template:
    family: '"cas_scanner_blueprint_scanner_python"'
  plan-scan-template:
    family: '"cas_scanners_orchestration_plan_scan"'
  scan-failure-handler-template:
    family: '"cas_scanners_orchestration_scan_failure_handler"'
  scanners-orchestration-workflow-template:
    family: '"cas_scanners_orchestration_pre_scan"'
  scan-result-template:
    family: '"cas_source_control_scan_result"'
  files-fetching-template:
    family: '"cas_source_control_files_fetching"'
  pr-comments-template:
    family: '"cas_source_control_pr_comments"'
  source-control:
    family: '"cas_source_control_source_control"'
  sync-customer-template:
    family: '"cas_source_control_sync_external_projects"'
  webhook:
    family: '"cas_source_control_webhook"'
  transporter:
    family: '"cas_transporter_transporter"'
  collectors:
    family: '"cas_collectors_collectors"'
  collectors-parser-template:
    family: '"cas_collectors_parser"'
  webhooks-subscription-template:
    family: '"cas_source_control_webhooks_subscription"'
  cases-service:
    family: '"cas_aspm_cases_cases_service"'
  billing-api:
    family: '"cas_billing_billing_api"'
  ci-logs-ingestion:
    family: '"cas_cicd_ci_logs_ingestion"'
  cicd-graphql:
    family: '"cas_cicd_cicd_graphql"'
  supply-chain-tools:
    family: '"cas_cicd_supply_chain_tools"'
  detection-rules:
    family: '"cas_detection_rules_detection_rules"'
  uploads:
    family: '"cas_parser_third_party_uploads"'
  thirdparty-scanner-template:
    family: '"cas_parser_third_party_scanner"'
  policies-api:
    family: '"cas_policies_policies_api"'
  ai-guardrails:
      family: '"cas_policies_ai_guardrails"'
  githistory-scanner-template:
    family: '"cas_scanner_secrets_secrets_scanner"'
  source-control-ci-cd-transformer:
    family: '"cas_source_control_ci_cd_transformer"'
  get-external-project-report-template:
    family: '"cas_source_control_fetch_external_project_report"'
  git-users-template:
    family: '"cas_source_control_git_users"'
  get-scanned-branches-template:
    family: '"cas_source_control_get_scanned_branches"'
  vcs-enrichment-template:
    family: '"cas_source_control_vcs_enrichment"'
  webhooks-sync-template:
    family: '"cas_source_control_webhooks_subscription"'
  vcs-ci-fetcher:
    family: '"cas_source_control_vcs_ci_fetcher"'
  platform-bridge:
    family: '"cas_source_control_platform_bridge"'
  ci-data-persistence-workflow:
    family: '"cas_cicd_graph_entities_loader"'
  circleci-template:
    family: '"cas_cicd_circleci"'
  circle-ci-build-logs-workflow:
    family: '"cas_cicd_circleci"'
  org-cicd-enrichment-template:
    family: '"cas_cicd_graph_entities_loader"'
  repo-cicd-enrichment-template:
    family: '"cas_cicd_graph_entities_loader"'
  jenkins-integration-status-update-cron-workflow:
    family: '"cas_cicd_jenkins_manager"'
  jenkins-manager-workflow:
    family: '"cas_cicd_jenkins_manager"'
  inspect-build-logs-workflow:
    family: '"cas_cicd_inspect_build_logs_job"'
  pipeline-image-match-template:
    family: '"cas_cicd_pipeline_image_match"'
  calculate-sop-ppe-findings-workflow:
    family: '"cas_cicd_sop_ppe_findings"'
  calculate-vcs-pipeline-tools-processor-workflow:
    family: '"cas_cicd_vcs_pipeline_tools_processor"'
  pipeline-tools-sync-cron-workflow:
    family: '"cas_cicd_pipeline_tools_sync"'
  code-to-cloud-tracing-workflow-template:
    family: '"cas_code_to_cloud_tracing"'
  drift-detection-workflow-template:
    family: '"cas_code_to_cloud_drift_detection"'
  code-to-cloud-tagging-bot-workflow:
    family: '"cas_code_to_cloud_tagging_bot"'
  code-to-cloud-finding-issue-tracing-workflow-template:
    family: '"cas_code_to_cloud_finding_issue_tracing"'
  cli-api:
    family: '"cas_scanners_orchestration_cli_api"'
  scanner-tasks-manager-api:
    family: '"cas_scanners_orchestration_scanner_tasks_manager_api"'
  secrets-scanner-worker:
    family: '"cas_scanner_secrets_secrets_scanner_worker"'
  iac-scanner-worker:
    family: '"cas_scanner_iac_iac_scanner_worker"'
  sca-scanner-worker:
    family: '"cas_scanner_sca_sca_scanner_worker"'
  sca-findings-scanner-worker:
    family: '"cas_scanner_sca_sca_findings_scanner_worker"'
  code-to-cloud-external-modules-api:
    family: '"cas_code_to_cloud_code_to_cloud_external_modules_api"'
  tf-run-task-results-template:
    family: '"cas_source_control_tf_run_task_results"'
  webhook-handler:
    family: '"cas_cicd_webhook_handler"'
  cas-source-control-sync-and-persist-customer:
    family: '"cas_source_control_sync_customer"'
  pipex-scanner-template:
    family: '"cas_scanner_pipex_pipex_scanner"'
  scanner-pipex-pipex-result-parser:
    family: '"cas_scanner_pipex_pipex_result_parser"'
  scanner-deppy-deppy-scanner:
    family: '"cas_scanner_deppy_deppy_scanner"'
  scanner-ci-match-ci-match-scanner:
    family: '"cas_scanner_ci_match_ci_match_scanner"'
  scanner-ci-match-ci-match-result-parser:
    family: '"cas_scanner_ci_match_ci_match_result_parser"'
  repository-urgency-runtime-metrics-workflow:
    family: '"cas_application_repository_urgency_runtime_metrics_collector"'
  handle-billing-workflow:
    family: '"cas_handle_billing_workflow"'
  product-analytics-workflow:
    family: '"cas_product_analytics_workflow"'
  artifactory-management:
    family: '"cas_scanner_sca_artifactory_management"'
  packages-api:
    family: '"cas_scanner_sca_packages_api"'
  sbom-management:
    family: '"cas_sbom_management_sbom_management"'
  code-to-cloud-api:
    family: '"cas_code_to_cloud_code_to_cloud_api"'
  unified-cli-api:
    family: '"cas_unified_cli_unified_cli_api"'
  client-metrics-api:
    family: '"cas_client_metrics_client_metrics_api"'
  ciem-api:
    family: '"ciem"'
  ciem-ipc:
    family: '"ciem"'
  ciem-account-manager:
    family: '"ciem"'
  ciem-epc:
    family: '"ciem"'
  ciem-pre-calculation:
    family: '"ciem"'
  ciem-static-evidence:
    family: '"ciem"'
  dspm-aispm-static-evidence:
    family: '"ciem"'
  ciem-rule-scanner-ciem:
    family: '"ciem"'
  ciem-rule-scanner-dspm:
    family: '"ciem"'
  ciem-rule-scanner-aispm:
    family: '"ciem"'
  ciem-sync:
    family: '"ciem"'
  ciem-asset-change:
    family: '"ciem"'
  ciem-lap:
    family: '"ciem"'
  ciem-db-maintenance:
    family: '"ciem"'
  ciem-access-table-cron-job:
    family: '"ciem"'
  ciem-la-publisher-cron-job:
    family: '"ciem"'
  ciem-aws-access-analyzer:
    family: '"ciem"'
  ciem-gam-cron-job:
      family: '"ciem"'
  uvem-netscan-processor:
    family: '"uvem_netscan_processor"'
  uvem-netscan-controller:
    family: '"uvem_netscan_controller"'
  uvem-vip-api:
    family: '"uvem_vip_api"'
  uvem-vxp-api:
    family: '"uvem_vxp_api"'
  external-integration:
    family: '"backend"'
  itdr-risk-processor:
    family: '"itdr_risk_processor"'
  itdr-api:
    family: '"itdr_api"'
  itdr-data-pipeline:
    family: '"itdr_data_pipeline"'
  itdr-bigquery-migrator:
    family: '"itdr_bigquery_migrator"'
  itdr-data-syncer:
    family: '"itdr_data_syncer"'
  notification-forwarding-transport:
    family: '"notification_forwarding_transport"'
  notification-forwarding-decorator:
    family: '"notification_forwarding_decorator"'
  notification-api-service:
    family: '"notification_api_service"'
  platform-compliance-calc:
    family: '"backend"'
  dp-analytics-detection:
    family: '"backend"'
  dp-analytics-scan-logs:
    family: '"pipeline"'
  cloudsec-cloud-api-service-liquibase-init:
    family: '"cloudsec_cloud_api_service_liquibase_init"'
  cloudsec-rule-management-init:
    family: '"cloudsec_rule_management_init"'
  cloudsec-verdict-manager-init:
    family: '"cloudsec_verdict_manager_init"'
  cloudsec-verdict-manager-liquibase-init:
    family: '"cloudsec_verdict_manager_liquibase_init"'
  cloudsec-attack-path-pre-processor:
    family: '"cloudsec_attack_path_pre_processor"'
  cloudsec-attack-path:
    family: '"cloudsec_attack_path"'
  ciem-liquibase:
    family: '"ciem_liquibase"'
  ciem-liquibase-bq:
    family: '"ciem_liquibase_bq"'
  dspm-sia:
    family: '"dspm_sia"'
  dspm-dbear:
    family: '"dspm_dbear"'
  dspm-scar:
    family: '"dspm_scar"'
  remediations-get-findings:
    family: '"cas_remediations_get_findings"'
  remediations-pre-fix:
    family: '"cas_remediations_pre_fix"'
  scanner-sca-sca-findings-scanner:
    family: '"cas_scanner_sca_sca_findings_scanner"'
  scanners-orchestration-post-scan:
    family: '"cas_scanners_orchestration_post_scan"'
  source-control-sync-branches:
    family: '"cas_source_control_sync_branches"'
  source-control-trigger-branch-scans:
    family: '"cas_source_control_trigger_branch_scans"'
  scanner-enry-enry-result-parser:
    family: '"cas_scanner_enry_enry_result_parser"'
  cicd-persist-vcs-ci-manifest:
    family: '"cas_cicd_persist_vcs_ci_manifest"'
  cicd-assets-generator:
    family: '"cas_cicd_assets_generator"'
  code-to-cloud-internet-exposure-scanner:
    family: '"cas_code_to_cloud_internet_exposure_scanner"'
  persist-vcs-ci-manifest:
    family: '"cas_cicd_persist_vcs_ci_manifest"'
  remove-integration-job:
    family: '"cas_cicd_remove_integration_job"'
  remove-integration-workflow:
    family: '"cas_cicd_remove_integration"'
  cicd-api:
    family: '"cas_cicd_cicd_api"'
  remove-integration:
    family: '"cas_cicd_remove_integration"'
  application-job:
    family: '"cas_application_application_job"'
  scan-result:
    family: '"cas_source_control_scan_result"'
  persistence-manager:
    family: '"cas_persistence_manager_persistence_manager"'
  repository_urgency_runtime_metrics_collector:
    family: '"cas_application_repository_urgency_runtime_metrics_collector"'
  enrichment-service:
    family: '"cas_persistence_enrichment_service"'
  prisma-migration-scanned-branches:
    family: '"cas_source_control_prisma_migration_scanned_branches"'
  actions-handler:
    family: '"cas_source_control_actions_handler"'
  public-apis:
    family: '"cas_orchestration_public_apis"'
  augmentor:
    family: '"cas_persistence_augmentor"'
  data-gateway:
    family: '"cas_source_control_data_gateway"'
  enrichment-manager:
    family: '"cas_enrichment_manager_enrichment_manager"'
  data-collector-worker:
    family: '"cas_source_control_data_collector_worker"'
  pre-scan-periodic:
    family: '"cas_scanners_orchestration_pre_scan_periodic"'
  pre-scan-pr:
    family: '"cas_scanners_orchestration_pre_scan_pr"'
  post-scan-periodic:
    family: '"cas_scanners_orchestration_post_scan_periodic"'
  post-scan-pr:
    family: '"cas_scanners_orchestration_post_scan_pr"'
  lobby-periodic:
    family: '"cas_scanners_orchestration_lobby_periodic"'
  lobby-pr:
    family: '"cas_scanners_orchestration_lobby_pr"'
  post-collection-processor:
    family: '"cas_source_control_post_collection_processor"'
  scanner-deppy-service:
    family: '"cas_scanner_deppy_scanner_deppy_service"'
  scanner-pipex-service:
    family: '"cas_scanner_pipex_scanner_pipex_service"'
  scanner-ci-match-service:
    family: '"cas_scanner_ci_match_scanner_ci_match_service"'
  scanner-enry-service:
    family: '"cas_scanner_enry_scanner_enry_service"'
  viso:
    family: '"viso"'
  qr-reader-server:
    family: '"qr_reader_server"'
  agent-mgmt-processor:
    family: '"agent_mgmt"'
  itdr-audits:
    family: '"itdr_audits"'
  itdr-cap-block-responder:
    family: '"itdr_audits"'
  db-init:
    family: '"db_init"'
