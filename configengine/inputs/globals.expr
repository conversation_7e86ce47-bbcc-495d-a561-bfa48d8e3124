is_metro_v2: "tenant.is_metro_tenant && !tenant.is_legacy_metro"

#TODO: remove cloud/cortex_platform condition
is_xsiam: license.is_xsiam || infra_ff.enable_cortex_platform
is_xsoar_legacy_spec: not infra_ff.enable_cortex_platform || (tenant.is_migrated_cortex_platform && license.is_xsiam)

region_short_code: 'replace(region.viso_env, "prod-", "")'

# GCPCONF_REGION_ABBR
_region_abbr:
  default: "local.region_short_code"
  prod-fa: '"fr"'
  prod-fr: '"us"'
  prod-gv: '"us"'
  prod-qt: '"qa"'
  prod-pr: '"us"'
  dev: '"us"'

# FLIPPERCONF_CHANGED_VERDICTS_API_URL
_gvs_url_map:
  default: '"https://" + local.region_short_code + "gvs.wildfire.paloaltonetworks.com"'
  dev: '"https://usgvs.wildfire.paloaltonetworks.com"'
  prod-fa: '"https://frgvs.wildfire.paloaltonetworks.com"'
  prod-fr: '"https://globalservice.wildfire.gov.paloaltonetworks.com"'
  prod-gv: '"https://govgvs.wildfire.paloaltonetworks.com"'
  prod-qt: '"https://qagvs.wildfire.paloaltonetworks.com"'
  prod-pr: '"https://usgvs.wildfire.paloaltonetworks.com"'

metro_namespace: '"xdr-mt"'
pithos_namespace: '"pithos"'
metrus_namespace: '"metrus"'
scylla_namespace: '"scylla"'
scylla_iplen_namespace: '"scylla-iplen"'
scylla_xcloud_namespace: '"scylla-xcloud"'
external_secrets_namespace: '"external-secrets"'
xdr_st_namespace: '"xdr-st"'
default_namespace: "!tenant.is_metro_tenant || tenant.is_legacy_metro ? local.xdr_st_namespace : local.metro_namespace"
st_namespace: 'tenant.is_metro_tenant ? "xdr-st-" + tenant.lcaas_id : local.xdr_st_namespace'
dp_ipl_alyx_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "dp-ipl-alyx"'
cas_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "cas"'
cas_redis_namespace: "tenant.is_metro_host ? local.metro_namespace : local.cas_namespace"
cwp_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "cwp"'
ciem_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "ciem"'
dspm_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "dspm"'
apisec_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "apisec"'
cloudsec_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "cloudsec"'
cortex_cts_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "cortex-cts"'
xsoar_jobs_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "xsoar-jobs"'
vsg_namespace: "tenant.is_metro_host ? local.metro_namespace : local.st_namespace"
monitoring_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "monitoring"'
apilot_shardscape_namespace: '"shardscape"'
custom_metrics_namespace: 'tenant.is_metro_tenant ? local.st_namespace : "custom-metrics"'
st_resource_prefix: '"xdr-st-"+ tenant.lcaas_id'
tenant_secrets: 'tenant.lcaas_id + "-secrets"'
metro_secrets: '"mt-secrets"'
buckets:
  edr_data: 'tenant.project_id + "-edr-data"'
subscriptions:
  analytics_task_processor_sub: '"analytics-task-processor-" + tenant.lcaas_id + "-sub"'
topics:
  analytics_task_processor: '"analytics-task-processor-" + tenant.lcaas_id'
  analytics_detection_hits: '"analytics-detection-hits-" + tenant.lcaas_id'
gcr: '"us-docker.pkg.dev/xdr-registry-" + region.multi_project_postfix + "-01"'
chart_registry: 'local.gcr + "/cortex-helm-charts"'
storage_classes:
  ssd_storage: 'tenant.is_metro_tenant? "ssd-csi-storage" : "ssd-storage"'
  ssd_storage_no_replication: 'tenant.is_metro_tenant? "ssd-csi-storage-no-replication" : "ssd-storage-no-replication"'
priority_classes:
  high_priority: '"high-priority-deployment"'
  monitoring_priority: '"monitoring-priority-class"'
startup_probe:
  total_timeout_seconds: "60 * 3"
  timeout_seconds: "tenant.is_metro_tenant ? 20 : 2"
  period_seconds: 5
  failure_threshold: "local.startup_probe.total_timeout_seconds / local.startup_probe.period_seconds * 3"
kms_keyring_region: '"us-central1"'
global_bioc_project: 'region.is_fedramp ? "xdr-mag-shared-" + region.viso_env + "-01" : "xdr-zeus"'
global_bioc_bucket: 'region.is_fedramp ? "global-bioc-rules-" + region.viso_env : "global-bioc-rules-prod"'

_cwp_aws_shared_resources_account_id:
  dev: 'tenant.creation_date > ************* ? "************" : "************"'
  prod-pr: '"************"'
  prod-us: '"************"'
  prod-uk: '"************"'
  prod-tw: '"************"'
  prod-sg: '"************"'
  prod-sa: '"************"'
  prod-qt: '"************"'
  prod-pl: '"************"'
  prod-kr: '"************"'
  prod-jp: '"************"'
  prod-it: '"************"'
  prod-in: '"************"'
  prod-il: '"************"'
  prod-id: '"************"'
  prod-fa: '"************"'
  prod-eu: '"************"'
  prod-es: '"************"'
  prod-de: '"************"'
  prod-ch: '"************"'
  prod-ca: '"************"'
  prod-au: '"************"'
  prod-za: '"************"'
  prod-fr: '"************"'
  prod-gv: '"************"'
  prod-br: '"************"'
  prod-dl: '"************"'
cwp_aws_shared_resources_account_id: "infra_ff.enable_cortex_platform ? local._cwp_aws_shared_resources_account_id[region.viso_env] : nil"

dumpsterconf_activate_gcs_mode:
  [
    "'prod-au'",
    "'prod-ca'",
    "'prod-ch'",
    "'prod-de'",
    "'prod-jp'",
    "'prod-in'",
    "'prod-pl'",
    "'prod-qt'",
    "'prod-sg'",
    "'prod-tw'",
    "'prod-fa'",
    "'prod-il'",
    "'prod-id'",
    "'prod-sa'",
    "'prod-es'",
    "'prod-pr'",
    "'prod-it'",
    "'prod-kr'",
    "'prod-za'",
    "'prod-br'",
    "'prod-dl'",
  ]

render_dependency_chart: "false"
is_xsoar_6_migration_phase: "tenant.is_xsoar_6_migration && !tenant.is_xsoar_6_migration_completed"

# TODO: move to a dedicated file
cronus_standalone: infra_ff.enable_cronus && (infra_ff.is_enable_prod_spec || tenant.is_metro_tenant)
cronus_node_count: "license.is_xpanse || !local.cronus_standalone ? 1 : max(min(ceil(license.dml_scale_base / 2500), 150), 1)"
scylla_user: 'tenant.is_legacy_metro ? "dml_" + tenant.lcaas_id : "user"'
scylla_admin: '"root"'
scylla_conf_xcloud_password: 'infra_ff.is_enable_xcloud_standalone_deployment ? "scylla_xcloud_password" : "scylla_password"'
scylla_node_count_license: |-
  license.is_xpanse || !infra_ff.is_enable_scylla_standalone ? 1 :
  min(max(ceil(license.dml_scale_base / (infra_ff.enable_cronus ? 7000.0: 1400.0)), 1), (infra_ff.enable_cronus ? 35 : 60))
scylla_nodes_count: |-
  infra_ff.enable_scylla ? local.scylla_node_count_license : 0
alyx_readiness: |-
  infra_ff.enable_alyx_migration ? false : true
scylla_endpoint: 'infra_ff.enable_new_scylla ? "scylla-client." + local.scylla_namespace + ".svc" : "scylla-cluster-client.scylla.svc"'
alyx_readiness: |-
  infra_ff.enable_alyx_migration ? false : true
scylla_xcloud_username: |-
  tenant.is_metro_tenant && !tenant.is_metro_host ? "xcloud_" + tenant.lcaas_id :
  "user"
xcloud_endpoint: |-
  infra_ff.enable_new_scylla ? "scylla-xcloud-client." + local.scylla_xcloud_namespace + ".svc" : "scylla-cluster-client.scylla-xcloud.svc"
scylla_xcloud_endpoint: |-
  infra_ff.is_enable_xcloud_standalone_deployment ? local.xcloud_endpoint : local.scylla_endpoint
scylla_enrichment_username: |-
  tenant.is_metro_tenant && !tenant.is_metro_host ? "enrichment_" + tenant.lcaas_id :
  "user"
scylla_iplen_nodes_count: min(local.cronus_node_count, 60)
scylla_enrichment_endpoint: '"ipl-scylla-client." + local.scylla_iplen_namespace + ".svc"'
## END TODO

xcloud_redis_standalone_deployment: infra_ff.is_enable_xcloud && (infra_ff.is_enable_xcloud_standalone_deployment || infra_ff.is_enable_prod_spec)
xcloud_redisconf_password: 'local.xcloud_redis_standalone_deployment ? "xcloud_redis_password" : "redis_password"'
common_startup_probe_timeout_seconds: 'tenant.is_metro_tenant ? "20" : "2"'

expanse_embedded_policy_conf_global_storage_bucket_name: |-
  region.is_fedramp_moderate ? "xpanse-policies-prod-fr" :
  region.is_fedramp_high ? "xpanse-policies-prod-gv" :
  region.is_dev ? "xpanse-policies-dev" :
  "xpanse-policies-prod"

global_profiles_eu_regions: '[ "prod-eu", "prod-de", "prod-pl", "prod-fa", "prod-es", "prod-it"]'
global_profiles_project_env: |-
  (region.is_dev || region.is_fedramp) ? region.viso_env :
  (region.viso_env in local.global_profiles_eu_regions) ?  "prod-eu" :
  "prod-us"
global_profiles_source_region: '(region.viso_env in local.global_profiles_eu_regions) ? "eu" : "us"'
global_profiles_small_regions_list: |-
  ["prod-jp", "prod-ca", "prod-ch", "prod-sg", "prod-uk", "prod-de", "prod-au",
   "prod-in", "prod-pl", "prod-qt", "prod-tw", "prod-fa", "prod-il", "prod-id", "prod-sa", "prod-es", "prod-pr", "prod-it", "prod-kr", "prod-za", "prod-br", "prod-dl"]
dumpster_list: |-
  ["prod-au", "prod-ca", "prod-ch", "prod-de", "prod-jp", "prod-in", "prod-pl",
   "prod-qt", "prod-sg", "prod-tw", "prod-fa", "prod-il", "prod-id", "prod-sa", "prod-es"]

use_crtx_domain: "license.is_xpanse || license.is_xsoar"
external_fqdn: "local.use_crtx_domain ? tenant.crtx_fqdn : tenant.fqdn"

is_uat: hasSuffix(split(local.external_fqdn, ".")[1], "uat")

tenant_proxy: '"************"'
egress_proxy_port: 'tenant.is_perf_tenant ? "11118" : "11117"'
egress_proxy_address: 'local.tenant_proxy + ":" + local.egress_proxy_port'

# ConfigMap: ANALYTICSCONF_TIMEZONE_OFFSET
timezone_offsets:
  dev: '"-5"'
  prod-au: '"+10"'
  prod-ca: '"-4"'
  prod-ch: '"+1"'
  prod-de: '"+2"'
  prod-es: '"+1"'
  prod-eu: '"+2"'
  prod-fa: '"+2"'
  prod-fr: '"-5"'
  prod-gv: '"-5"'
  prod-id: '"+5"'
  prod-il: '"+2"'
  prod-in: '"+5:30"'
  prod-jp: '"+9"'
  prod-pl: '"+1"'
  prod-qt: '"+3"'
  prod-sa: '"+1"'
  prod-sg: '"+8"'
  prod-tw: '"+8"'
  prod-uk: '"+1"'
  prod-us: '"-5"'
  prod-pr: '"-5"'
  prod-kr: '"+9"'
  prod-it: '"+1"'
  prod-za: '"+2"'
  prod-br: '"-3"'
  prod-dl: '"+5:30"'
timezone_offset: "local.timezone_offsets[region.viso_env]"

# dev aware viiso env is aware to the diff between qa4 and qa2
dev_aware_viso_env: |-
  region.project_prefix == "qa4-test" ? "predev" :
  region.project_prefix == "qa2-test" ? "dev" :
  region.viso_env

viso_git_updater_bucket_name: |-
  local.dev_aware_viso_env contains("dev") ? "viso-git-updater-" + local.dev_aware_viso_env :
  region.is_fedramp ? "viso-git-updater-fed" :
  "viso-git-updater-prod"

is_internal: tenant.authcode == "internal"

_xsoar_registries:
  dev: '"us"'
  prod-au: '"us"'
  prod-ca: '"us"'
  prod-ch: '"asia"'
  prod-de: '"eu"'
  prod-es: '"es"'
  prod-eu: '"eu"'
  prod-fa: '"eu"'
  prod-fr: '"us"'
  prod-gv: '"us"'
  prod-id: '"asia"'
  prod-il: '"eu"'
  prod-in: '"asia"'
  prod-jp: '"asia"'
  prod-pl: '"eu"'
  prod-qt: '"asia"'
  prod-sa: '"asia"'
  prod-sg: '"asia"'
  prod-tw: '"asia"'
  prod-uk: '"eu"'
  prod-us: '"us"'
  prod-pr: '"us"'
  prod-it: '"eu"'
  prod-kr: '"asia"'
  prod-za: '"eu"'
  prod-br: '"us"'
  prod-dl: '"asia"'

xsoar_registry_region: local._xsoar_registries[region.viso_env]

global_verdict_service_conf_verdict_service_host: 'infra_ff.enable_assured_workloads ? "https://globalservice-eu-reg.wildfire.paloaltonetworks.com" : get(local._gvs_url_map, region.viso_env) ?? local._gvs_url_map["default"]'
gcpconf_region_abbr: 'infra_ff.enable_assured_workloads ? "de-rg" : get(local._region_abbr, region.viso_env) ?? local._region_abbr["default"]'

_rocksdb_logic: license.pro_agents_for_scaling + (200 * license.monthly_tb_licenses)

rocksdb_cpu: |-
  tenant.is_metro_tenant ? 0.5 :
  infra_ff.is_enable_rocksdb_standalone ? 14 :
  local._rocksdb_logic <= 1000 ? 0.5 :
  (local._rocksdb_logic > 1000 && local._rocksdb_logic <= 2000) ? 2 :
  (local._rocksdb_logic > 2000 && local._rocksdb_logic <= 10000) ? 4 : 7
rocksdb_memory: |-
  tenant.is_metro_tenant ? "7Gi" :
  infra_ff.is_enable_rocksdb_standalone ? "55Gi" :
  local._rocksdb_logic <= 10000 ? "7Gi" : "26Gi"

xdr_agent:
  registry: |-
    region.is_fedramp_moderate ? "us-central1-docker.pkg.dev/xdr-fr-**********" :
    region.is_fedramp_high ? "us-central1-docker.pkg.dev/xdr-gv-*************" :
    "us-central1-docker.pkg.dev/xdr-us-********"

xsoar_mig_specs:
  micro:
    node_pool_machine_type: '"none"'
    xsoar_mig_fuse_limit: '"0"'
    xsoar_mig_cpu_request: '"0.5"'
    xsoar_mig_mem_request: '"1Gi"'
    xsoar_mig_cpu_limit: '"0.5"'
    xsoar_mig_mem_limit: '"1Gi"'
    xsoar_mig_pvc_size: 'tenant.xsoar_6_account_id == "************" ? "100Gi" : "2000Gi"'
  small:
    node_pool_machine_type: '"e2-standard-8"'
    xsoar_mig_fuse_limit: '"1"'
    xsoar_mig_cpu_request: '"5"'
    xsoar_mig_mem_request: '"25Gi"'
    xsoar_mig_cpu_limit: '"7.5"'
    xsoar_mig_mem_limit: '"27Gi"'
    xsoar_mig_pvc_size: 'tenant.xsoar_6_account_id == "************" ? "100Gi" : "1000Gi"'
  medium:
    node_pool_machine_type: 'tenant.xsoar_6_account_id == "************" ? "e2-standard-16" : "e2-highmem-16"'
    xsoar_mig_fuse_limit: '"1"'
    xsoar_mig_cpu_request: 'tenant.xsoar_6_account_id == "************" ? "15.5" : "14"'
    xsoar_mig_mem_request: 'tenant.xsoar_6_account_id == "************" ? "60Gi" : "110Gi"'
    xsoar_mig_cpu_limit: 'tenant.xsoar_6_account_id == "************" ? "15.5" : "15"'
    xsoar_mig_mem_limit: 'tenant.xsoar_6_account_id == "************" ? "60Gi" : "120Gi"'
    xsoar_mig_pvc_size: 'tenant.xsoar_6_account_id == "************" ? "100Gi" : "1000Gi"'
  large:
    node_pool_machine_type: '"e2-standard-32"'
    xsoar_mig_fuse_limit: '"1"'
    xsoar_mig_cpu_request: '"30"'
    xsoar_mig_mem_request: '"110Gi"'
    xsoar_mig_cpu_limit: '"31"'
    xsoar_mig_mem_limit: '"120Gi"'
    xsoar_mig_pvc_size: 'tenant.xsoar_6_account_id == "************" ? "100Gi" : "2000Gi"'

flux:
  helm_release_drift_detection_mode: |
    region.viso_env == "dev" ? "warn" : "enabled"
  chart_pull_interval: "'2600h'"

# tenant replication feature:
replication_metadata:
  is_replication_enabled: tenant.replication_source_project_id != ""
  source_tenant_id: |-
    local.replication_metadata.is_replication_enabled ? split(tenant.replication_source_project_id, "-")[2] : ""
  lcaas:
    - '"replicate_all"'
    - '"replicate_all_no_agent_alerts"'
    - '"lcaas_only"'
  is_lcaas: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.lcaas
  ext_logs:
    - '"replicate_all"'
    - '"replicate_all_no_agent_alerts"'
    - '"ext_logs_only"'
  is_ext_logs: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.ext_logs
  edr:
    - '"replicate_all"'
    - '"replicate_all_no_agent_alerts"'
    - '"edr_only"'
  is_edr: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.edr
  dss:
    - '"replicate_all"'
    - '"replicate_all_no_agent_alerts"'
    - '"dss_only"'
  is_dss: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.dss
  app_hub_observability:
    - '"replicate_all"'
    - '"replicate_all_no_agent_alerts"'
    - '"app_hub_observability_only"'
  is_app_hub_observability: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.app_hub_observability
  agent_alerts:
    - '"replicate_all"'
    - '"agent_alerts_only_only"'
  is_agent_alerts: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.agent_alerts
  detection_engine_only:
    - '"detection_engine"'
  is_detection_engine_only: local.replication_metadata.is_replication_enabled && tenant.replication_type in local.replication_metadata.detection_engine_only

product_code_agentix: license.product_code == "agentix"
product_code_c1: infra_ff.enable_cortex_platform && license.product_code == "c1" && !local.product_code_agentix
product_code_c3: infra_ff.enable_cortex_platform && license.product_code == "c3" && !local.product_code_agentix
product_code_x0: infra_ff.enable_cortex_platform && license.product_code == "x0" && !local.product_code_agentix
product_code_x1: infra_ff.enable_cortex_platform && license.product_code == "x1" && !local.product_code_agentix
product_code_x3: infra_ff.enable_cortex_platform && license.product_code == "x3" && !local.product_code_agentix
product_code_x5: infra_ff.enable_cortex_platform && license.product_code == "x5" && !local.product_code_agentix
product_code_eplus: infra_ff.enable_cortex_platform && license.product_code == "ent_plus"
product_code_legacy: license.product_code == "legacy"
enable_cloud_appsec: infra_ff.enable_cortex_platform && license.enable_cloud_appsec
enable_cloud_posture: infra_ff.enable_cortex_platform && license.enable_cloud_posture && !local.product_code_agentix
enable_itdr: infra_ff.enable_cortex_platform && license.enable_itdr
enable_apisec: infra_ff.enable_cortex_platform && license.enable_apisec

agent_mgmt:
  enable_agent_redis: infra_ff.enable_redis_agent_api || local.product_code_eplus
  enable_forensics_processor: |
    !tenant.is_metro_tenant && (
      !license.is_xsoar &&
      !license.is_xpanse &&
      infra_ff.enable_pipeline &&
      !local.product_code_agentix &&
      !tenant.is_legacy_metro ||
      local.product_code_c3 ||
      local.product_code_x0 ||
      local.product_code_x1 ||
      local.product_code_x3 ||
      local.product_code_x5 ||
      local.product_code_eplus
    )

cooc_light:
    enable_cooc_light: local.product_code_x1 || local.product_code_x3 || local.product_code_eplus

mysql:
  port: '"3306"'
  main_database: 'tenant.lcaas_id + "_main"'

_CORTEX_PLATFORM_FQDN: 'tenant.is_metro_tenant ? "platform." + local.metro_namespace + ".svc.cluster.local" : local.st_resource_prefix + "-platform." + local.st_namespace + ".svc.cluster.local"'

_mysql:
  name: 'local.is_metro_v2 ? "mysql" : tenant.is_legacy_metro ? "xdr-mt-" + tenant.metro_host_id + "-mysql" : local.st_resource_prefix + "-mysql"'
  namespace: "tenant.is_metro_tenant ? local.metro_namespace : local.xdr_st_namespace"

env:
  ARGO_SERVER_URL: '"http://argo-workflows-server." + local.cas_namespace + ".svc.cluster.local:2746"'
  CORTEX_PLATFORM_FQDN: "local._CORTEX_PLATFORM_FQDN"
  CORTEX_PLATFORM_URL: 'local._CORTEX_PLATFORM_FQDN + ":8000"'
  CORTEX_PLATFORM_URI: '"http://" + local._CORTEX_PLATFORM_FQDN + ":8000"'
  MYSQL_MAIN_URI: 'local._mysql.name + "." + local._mysql.namespace + ".svc.cluster.local"'
  PGHOST: 'tenant.is_metro_tenant ? "postgres." + local.metro_namespace + ".svc.cluster.local" : local.st_resource_prefix + "-postgres." + local.st_namespace + ".svc.cluster.local"'
  MONGO_DB_HOST: 'tenant.is_metro_tenant ? "cwp-mongodb." + local.metro_namespace + ".svc.cluster.local" : "xdr-st-" + tenant.lcaas_id + "-cwp-mongodb." + local.st_namespace + ".svc.cluster.local"'
  NEO4J_SERVER_URI: 'tenant.is_metro_tenant ? "neo4j://dp-neo4j-cluster-lb-neo4j." + local.metro_namespace + ".svc.cluster.local:7687" : "neo4j://dp-neo4j-cluster-lb-neo4j." + local.st_namespace + ".svc.cluster.local:7687"'
  TEMPORAL_URL: '(local.is_metro_v2 ? "temporal-frontend." + local.default_namespace  :  "temporal-" + tenant.lcaas_id + "-frontend." + local.st_namespace) + ".svc.cluster.local:7233"'
  REDIS_MAIN_HOST: '(local.is_metro_v2 ? "redis" : local.st_resource_prefix + "-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  REDIS_ANALYTICS_HOST: '(local.is_metro_v2 ? "analytics-redis" : local.st_resource_prefix + "-analytics-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  REDIS_CA_COLLECTION_URI: '(local.is_metro_v2 ? "ca-collection-redis" : local.st_resource_prefix + "-ca-collection-redis") + "." + local.default_namespace + ".svc.cluster.local:6379"'
  REDIS_CAS_URI: '(local.is_metro_v2 ? "cas-redis" : local.st_resource_prefix + "-cas-redis") + "." + local.cas_redis_namespace + ".svc.cluster.local"'
  REDIS_CWP_URI: '(local.is_metro_v2 ? "cwp-redis" : local.st_resource_prefix + "-cwp-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  CTSCONF_URL_TARGET_HOST: '(local.is_metro_v2 ? "cts.xdr-mt" : local.st_resource_prefix + "-cts.cortex-cts") + ".svc.cluster.local:3435"'
  REDIS_DSPM_URI: '(local.is_metro_v2 ? "dspm-redis" : local.st_resource_prefix + "-dspm-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  REDIS_IPL_ASSET_URI: '(local.is_metro_v2 ? "ipl-asset-redis" : local.st_resource_prefix + "-ipl-asset-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  DRAGONFLY_URI: '(local.is_metro_v2 ? "gonzo-dragonfly" : local.st_resource_prefix + "-gonzo-dragonfly") + "." + local.default_namespace + ".svc.cluster.local" + ":6379"'
  REDIS_XCLOUD_HOST: '(local.is_metro_v2 ? "xcloud-redis" : local.st_resource_prefix + "-xcloud-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  REDIS_XSOAR_HOST: '(local.is_metro_v2 ? "xsoar-redis" : local.st_resource_prefix + "-xsoar-redis") + "." + local.default_namespace + ".svc.cluster.local"'
  REDIS_XCLOUD_URI: 'local.env.REDIS_XCLOUD_HOST + ":6379"'
  REDIS_XSOAR_URI: 'local.env.REDIS_XSOAR_HOST + ":6379"'
  ELASTICSEARCH_URI: '"elastic-es-xsoar" + "." + local.default_namespace + ".svc.cluster.local"'
  TASK_SCHEDULER_SERVICE_URL: '"http://xdr-st-" + tenant.lcaas_id + "-task-scheduling." + local.st_namespace + ".svc.cluster.local:8080"'

gonzo_redis: "infra_ff.enable_redis_split || infra_ff.enable_gonzo_dragonfly"
gonzo_redis_connection_string: 'local.env.REDIS_MAIN_HOST + ":6379"'
hpl_gonzo_redis_connection_string: |-
  local.gonzo_redis ? "xdr-st-" + tenant.lcaas_id + "-gonzo-dragonfly:6379" :
  local.env.REDIS_MAIN_HOST + ":6379"
hpl_gonzo_redis_use_default: 'infra_ff.enable_redis_split || infra_ff.enable_gonzo_dragonfly ? "False" : "True"'
dedup_redis_password: 'infra_ff.enable_gonzo_dragonfly || infra_ff.enable_redis_split ? "gonzo_redis_password" : "redis_password"'

_curl_api_healthy_command: |-
  [
       "echo 'Waiting for API to be ready...'",
       "until curl -f -s --max-time 10 http://" + local.st_resource_prefix + "-api." + local.st_namespace + ".svc.cluster.local:4999/ping; do",
       "  echo 'API not ready, retrying in 10 seconds...'",
       "  sleep 10",
       "done",
       "echo 'API is ready!'"
  ]

api_healthy_initContainer:
  name: "'wait-for-api'"
  image: 'local.gcr + "/golden-images/curl:8.11.1"'
  command:
    - "'/bin/sh'"
    - "'-c'"
    - join(local._curl_api_healthy_command, "\n") + "\n"

enable_email_sku: "(!tenant.is_metro_tenant || tenant.is_metro_host) && infra_ff.enable_email_artifacts_relay && (license.is_xdr || license.is_xsiam || infra_ff.enable_cortex_platform)"

is_agentix_deployment: |-
  infra_ff.enable_cortex_platform

enable_neo4j_cluster: infra_ff.enable_cortex_platform && infra_ff.enable_neo4j_cluster

_distribution_postfix: 'region.multi_project_postfix == "dev" ? "-dev" : region.multi_project_postfix == "prod-fr" ? "-prod-fed" : region.multi_project_postfix == "prod-gv" ? "-prod-gv" : ""'

viso_tag: images.families.viso.tag
